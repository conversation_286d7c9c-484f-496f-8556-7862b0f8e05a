{"filter": {"title": "Data Filter", "collapse": "Collapse", "expand": "Expand", "collapseFilter": "Collapse Filter", "expandFilter": "Expand Filter", "name": "Name", "namePlaceholder": "Enter advertiser name", "adSource": "Ad Source", "adSourcePlaceholder": "Ad Source", "salesman": "Manager", "salesmanPlaceholder": "Manager"}, "buttons": {"add": "Add", "reset": "Reset", "search": "Search", "edit": "Edit", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete"}, "table": {"id": "ID", "name": "Name", "gamId": "GAM ID", "gamEmail": "GAM", "adSource": "Ad Source", "salesman": "Manager", "payCycle": "Pay Cycle", "sharePoint": "Revenue Share", "note": "Notes", "createTime": "Create Time", "actions": "Actions", "results": "results", "perPage": "Items per page"}, "form": {"advertiserName": "Advertiser Name", "advertiserNamePlaceholder": "Enter advertiser name", "gam": "GAM", "gamPlaceholder": "Enter GAM Email", "gamId": "GAM ID", "gamIdPlaceholder": "Enter GAM ID", "sharePoint": "Revenue Share (%)", "sharePointPlaceholder": "<PERSON><PERSON> 80 for 28 points", "adSource": "Ad Source", "adSourcePlaceholder": "Select ad source", "assignTo": "Assign To", "assignToPlaceholder": "Select manager", "companyName": "Company Name", "companyNamePlaceholder": "Enter company name", "companyAddress": "Company Address", "companyAddressPlaceholder": "Enter company address", "contactPerson": "Contact Person", "contactPersonPlaceholder": "Enter contact person", "contactInfo": "Contact Info", "contactInfoPlaceholder": "Enter contact info", "contactEmail": "Contact Email", "contactEmailPlaceholder": "Enter contact email", "billingEmail": "Billing Email", "billingEmailPlaceholder": "Enter billing email", "billingAddress": "Billing Address", "billingAddressPlaceholder": "Enter billing address", "payCycle": "Payment Cycle", "payCyclePlaceholder": "Select payment cycle", "note": "Notes", "notePlaceholder": "Enter notes"}, "validation": {"advertiserNameRequired": "Advertiser name is required", "gamRequired": "GAM is required", "gamIdRequired": "GAM ID is required", "sharePointRequired": "Revenue share is required", "sharePointRange": "Revenue share must be between 0-100", "adSourceRequired": "Ad source is required", "assignToRequired": "Assignment is required"}, "modal": {"addAdvertiser": "Add Advertiser", "editAdvertiser": "Edit Advertiser", "deleteAdvertiser": "Delete Advertiser", "deleteConfirm": "Are you sure you want to delete advertiser \"{name}\"? This action cannot be undone."}, "messages": {"fetchFailed": "Failed to fetch data: {msg}", "updateSuccess": "Updated successfully", "addSuccess": "Added successfully", "deleteSuccess": "Deleted successfully", "apiError": "API call exception"}, "adSourceOptions": {"all": "All", "adx": "ADX", "outbrain": "Outbrain", "taboola": "<PERSON><PERSON><PERSON>", "native": "Native", "adsense": "Adsense", "search": "Search"}, "salesmanOptions": {"all": "All", "ruby": "<PERSON>", "company": "Company"}}
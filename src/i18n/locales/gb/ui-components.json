{"cards": {"cards": "Cards", "fixed": "Fixed", "floating": "Floating", "contentText": "The unique stripes of zebras make them one of the animals most familiar to people.", "contentTextLong": "The unique stripes of zebras make them one of the animals most familiar to people. They occur in a variety of habitats, such as grasslands, savannas, woodlands, thorny scrublands, mountains, and coastal hills. Various anthropogenic factors have had a severe impact on zebra populations, in particular hunting for skins and habitat destruction. <PERSON><PERSON><PERSON>'s zebra and the mountain zebra are endangered. While plains zebras are much more plentiful, one subspecies, the quagga.", "rowHeight": "Row height", "title": {"default": "<PERSON><PERSON><PERSON>", "withControls": "With controls", "customHeader": "Custom header", "withoutHeader": "Without header", "withImage": "With Image", "withTitleOnImage": "With title on image", "withCustomTitleOnImage": "With custom title on image", "withStripe": "With stripe", "withBackground": "With background"}, "button": {"main": "Main", "cancel": "Cancel", "showMore": "Show More", "readMore": "Show More"}, "link": {"edit": "Edit", "setAsDefault": "Set as default", "delete": "Delete", "traveling": "Traveling", "france": "France", "review": "Review", "feedback": "Leave feedback", "readFull": "Read full article", "secondaryAction": "Secondary action", "action1": "Action 1", "action2": "Action 2"}}, "chat": {"title": "Cha<PERSON>", "sendButton": "Send"}, "spacingPlayground": {"value": "Value", "margin": "<PERSON><PERSON>", "padding": "Padding"}, "spacing": {"title": "Spacing"}, "colors": {"themeColors": "Theme Colors", "extraColors": "Extra Colors", "gradients": {"basic": {"title": "Button Gradients"}, "hovered": {"title": "Hovered <PERSON>ton Gradients", "text": "Lighten 15% applied to an original style (gradient or flat color) for hover state."}, "pressed": {"title": "Pressed <PERSON><PERSON>", "text": "Darken 15% applied to an original style (gradient or flat color) for pressed state."}}}, "tabs": {"alignment": "Tabs alignment", "overflow": "Tabs overflow", "hidden": "Tabs with hidden slider", "grow": "Tabs grow"}}
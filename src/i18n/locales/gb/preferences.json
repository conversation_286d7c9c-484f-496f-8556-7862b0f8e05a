{"breadcrumb": "Breadcrumb", "name": "Name", "account": "Account", "updatePasswordBtn": "Update Password", "forgetPasswordDesc": "Forget Password? Click here to reset password", "email": "Email", "password": "Password", "edit": "Edit", "twoFactorAuthentication": "Two-factor authentication", "theme": "Theme", "language": "Language", "resetPassword": "Reset Password", "title": "Preferences", "editName": "Edit Name", "cancel": "Cancel", "save": "Save", "nameChangedSuccess": "You've successfully changed your name", "oldPassword": "Old password", "newPassword": "New password", "repeatNewPassword": "Repeat new password", "updatePassword": "Update Password", "twoFactorAuthenticationButton": "Set up 2FA", "twoFactorAuthenticationDisabledButton": "Enable 2FA", "twoFactorAuthenticationContent": "Add an extra layer of security to your account. To sign in, you'll need to provide a code along with your username and password.", "twoFactorAuthenticationDisabledContent": "Two-Factor Authentication (2FA) is now enabled for your account, adding an extra layer of security to your sign-ins.", "twoFactorAuthenticationEnabledToast": "2FA successfully enabled", "twoFactorAuthenticationDisabledToast": "2FA successfully disabled", "breadcrumbOption1": "Show", "breadcrumbOption2": "<PERSON>de", "enableTwoFactorAuthentication": "Enable Two-Factor Authentication", "disableTwoFactorAuthentication": "Disable Two-Factor Authentication", "scanQRCodeToBindGoogleAuthenticator": "Scan the QR code to bind  Authenticator", "enterVerificationCode": "Enter verification code", "copySecret": "Copy Secret", "bind": "Bind", "disable": "Disable", "copySuccess": "Copy successful", "copyFailed": "Co<PERSON> failed", "getTwoFaFailed": "Failed to get 2FA information", "bindTwoFaFailed": "Failed to bind 2FA", "disableTwoFaFailed": "Failed to disable 2FA", "confirmDisableTwoFactor": "Are you sure you want to disable two-factor authentication? After disabling, you will not be able to use two-factor authentication to log in, and you will need to rebind it if you want to use it again.", "passwordRules": {"atLeast8Chars": "Must be at least 8 characters long", "atLeast6UniqueChars": "Must contain at least 6 unique characters", "oldPasswordRequired": "Old password field is required", "newPasswordRequired": "New password field is required", "newPasswordNotSame": "New password cannot be the same", "repeatPasswordRequired": "Repeat new password field is required", "passwordsDoNotMatch": "Confirm password does not match new password"}, "passwordChangedSuccess": "You've successfully changed your password", "adHighlight": {"title": "Ad Highlight Settings", "subtitle": "Configure highlight settings for ad data below thresholds", "tooltip": "When data values fall below the set threshold, they will be highlighted with custom colors", "threshold": "<PERSON><PERSON><PERSON><PERSON>", "color": "Highlight Color", "fields": {"clickRate": "Click Rate (CTR)", "impressionRate": "Impression Rate", "matchRate": "Match Rate", "activeViewRate": "Active View Rate"}, "descriptions": {"clickRate": "Highlight when click rate falls below the threshold", "impressionRate": "Highlight when impression rate falls below the threshold", "matchRate": "Highlight when match rate falls below the threshold", "activeViewRate": "Highlight when active view rate falls below the threshold"}, "messages": {"settingsSaved": "Setting<PERSON> saved successfully", "settingsSaveFailed": "Failed to save settings", "resetSuccess": "Reset to default settings"}, "validation": {"thresholdRange": "Threshold must be between 0-100"}, "actions": {"save": "Save", "reset": "Reset to De<PERSON>ult", "cancel": "Cancel"}}, "notifications": {"title": "Ad Notification Settings", "subtitle": "Manage your ad monitoring notification preferences", "tooltip": "When the value is below the threshold, a notification will be sent", "emailNotification": "Email Notification", "monitoringDays": "Monitoring Days", "threshold": "<PERSON><PERSON><PERSON><PERSON>", "days": "days", "email": "Email", "configureParameters": "Configure monitoring parameters and notification methods", "emptyStateTitle": "No notification settings", "emptyStateDescription": "Please contact administrator to configure ad notification settings", "settings": {"adsRequest": "R1 Ad Request Change", "revenue": "Revenue Change", "match": "Ad Match Rate", "matchRate": "R2 Lower Match Rate", "adsRequestRate": "Ads Request Drop", "revenueRate": "Revenue Drop", "matchRateDesc": "Match Rate Drop", "adsRequestRateDesc": "Ads Request Drop", "revenueRateDesc": "Revenue Drop", "clickRate": "R3 Lower Click Rate", "clickRateDesc": "Click Rate Drop"}, "descriptions": {"adsRequest": "When the ad request volume drops by {threshold}%, a notification will be sent", "revenue": "When the ad revenue drops by {threshold}%, a notification will be sent", "match": "When the ad match rate drops by {threshold}%, a notification will be sent", "rate": "When the value drops by {threshold}%, a notification will be sent", "matchRate": "Monitor the decline in ad match rate", "adsRequestRate": "Monitor the decline in ad request volume", "revenueRate": "Monitor the decline in ad revenue", "matchRateDesc": "Monitor the decline in ad match rate", "adsRequestRateDesc": "Monitor the decline in ad request volume", "revenueRateDesc": "Monitor the decline in ad revenue", "clickRate": "Monitor the decline in click rate", "clickRateDesc": "Monitor the decline in click rate"}, "messages": {"emailNotificationEnabled": "Email notification enabled successfully", "emailNotificationDisabled": "Email notification disabled successfully", "emailNotificationFailed": "Failed to update email notification settings", "monitoringDaysSaved": "Monitoring days setting saved", "thresholdSaved": "<PERSON><PERSON><PERSON><PERSON> setting saved", "settingsSaveFailed": "Failed to save settings", "settingsSaved": "Setting<PERSON> saved successfully", "settingSaveFailed": "Save setting failed", "settingSaved": "Setting saved successfully"}, "validation": {"thresholdRange": "Threshold must be between 0-100", "daysRange": "Monitoring days must be between 1-30", "required": "This field is required"}, "actions": {"save": "Save", "cancel": "Cancel", "saving": "Saving..."}}}
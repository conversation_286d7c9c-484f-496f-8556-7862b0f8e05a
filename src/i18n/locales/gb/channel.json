{"filter": {"title": "Data Filter", "collapse": "Collapse", "expand": "Expand", "collapseFilter": "Collapse Filter", "expandFilter": "Expand Filter", "name": "Name", "namePlaceholder": "Enter channel name", "type": "Type", "typePlaceholder": "Channel type"}, "buttons": {"add": "Add", "reset": "Reset", "search": "Search", "edit": "Edit", "delete": "Delete", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "assignData": "Assign Data"}, "table": {"name": "Channel Name", "contact": "<PERSON><PERSON> Account", "contactMail": "Channel Email", "isCreateSysUser": "Link Status", "company": "Channel Company", "type": "Channel Type", "affShare": "Revenue Share", "accessToken": "AccessToken", "promoteStartTime": "Promotion Start Time", "promoteEndTime": "Promotion End Time", "createTime": "Create Time", "actions": "Actions", "results": "results", "perPage": "Items per page"}, "form": {"type": "Channel Type", "typePlaceholder": "Select channel type", "name": "Channel Name", "namePlaceholder": "Enter channel name", "company": "Company", "companyPlaceholder": "Enter company name", "contact": "<PERSON><PERSON> Account", "contactPlaceholder": "Account assigned to channel", "contactMail": "Email", "contactMailPlaceholder": "Enter email", "affShare": "Revenue Share (%)", "affSharePlaceholder": "Enter 80 for 80% share", "promoteTime": "Promotion Period", "promoteStartPlaceholder": "Select start date", "promoteEndPlaceholder": "Select end date", "createSysUser": "Create Ad<PERSON> Account", "yes": "Yes", "no": "No", "password": "Account Password", "passwordPlaceholder": "Optional, default password is: login account+123."}, "validation": {"typeRequired": "Channel type is required", "nameRequired": "Channel name is required", "contactRequired": "Login account is required", "contactMailRequired": "Email is required", "affShareRange": "Revenue share must be between 0-100", "endTimeAfterStart": "End time must be after start time"}, "modal": {"addChannel": "Add Channel", "editChannel": "Edit Channel", "deleteChannel": "Delete Channel", "deleteConfirm": "Are you sure you want to delete channel \"{name}\"? This action cannot be undone."}, "messages": {"fetchFailed": "Failed to fetch data: {msg}", "updateSuccess": "Updated successfully", "addSuccess": "Added successfully", "deleteSuccess": "Deleted successfully", "apiError": "API call exception", "formValidationWarning": "Please complete the form", "endTimeWarning": "End time must be after start time"}, "status": {"linked": "Linked", "unlinked": "Unlinked"}, "channelType": {"revenueShare": "Revenue Share Channel", "cpa": "CPA Channel"}, "domain": {"title": "Assign Data to Channel: {name}", "assignedDomains": "Assigned Domains", "addDomain": "Add Domain", "editDomain": "Edit Domain", "domainCount": "Add Domain ({count})", "domain": "Domain", "domainPlaceholder": "Select domain", "startDate": "Start Date", "startDatePlaceholder": "Select start date", "endDate": "End Date", "endDatePlaceholder": "Select end date", "affShare": "Revenue Share (%)", "affSharePlaceholder": "Revenue share (0-100, e.g. 80)", "deductScale": "Deduction Rate (%)", "deductScalePlaceholder": "Deduction rate (0-100, e.g. 20)", "dataInfo": "From \"{startTime}\" to \"{lastTime}\" with \"{total}\" data records", "loading": "Loading...", "noDomains": "No domains available", "domainAlreadyAssigned": "This domain is already assigned to this channel", "assignSuccess": "Domain assigned successfully", "updateSuccess": "Domain assignment updated successfully", "deleteDomain": "Delete Domain Assignment", "deleteConfirm": "Are you sure you want to unassign domain \"{name}\"?", "deleteSuccess": "Domain assignment deleted successfully", "batchDelete": "Batch Delete Domain Assignments", "batchDeleteConfirm": "Are you sure you want to batch delete domain assignments?", "domainRequired": "Domain is required", "affShareRange": "Revenue share must be between 0-100", "deductScaleRange": "Deduction rate must be between 0-100", "endDateAfterStart": "End date must be after start date", "fetchDomainsFailed": "Failed to fetch domain list", "fetchAssignedDomainsFailed": "Failed to fetch assigned domains", "fetchAdvertisersFailed": "Failed to fetch advertiser list"}}
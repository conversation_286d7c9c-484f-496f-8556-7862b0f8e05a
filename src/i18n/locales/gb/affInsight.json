{"filter": {"title": "Data Filter", "collapse": "Collapse", "expand": "Expand", "collapseFilter": "Collapse Filter", "expandFilter": "Expand Filter", "dateRange": "Date Range", "dateRangePlaceholder": "Select date range", "channel": "Affiliate", "channelPlaceholder": "Select Affiliate", "domain": "Domain", "domainPlaceholder": "Select domain", "advertiser": "Advertiser", "advertiserPlaceholder": "Select advertiser", "adUnit": "Ad Unit", "adUnitPlaceholder": "Enter ad unit name", "groupBy": "Group By:"}, "buttons": {"settings": "Settings", "reset": "Reset", "search": "Search", "apply": "Apply", "cancel": "Cancel", "default": "<PERSON><PERSON><PERSON>", "lastThreeDays": "Last 3 Days", "lastWeek": "Last Week", "lastMonth": "Last Month"}, "table": {"date": "Date", "domain": "Domain", "country": "Country", "advertiser": "Advertiser", "affName": "Affiliate", "adUnit": "AdUnit", "discount": "Discount", "newUser": "NewUsers", "totalUser": "TotalUsers", "pageViews": "PageViews", "bounceRate": "Bounce Rate", "estRev": "Est. Rev.", "adRequest": "AdReq", "adMatch": "AdMatch", "matchRate": "MatchRate", "adImpression": "AdImp", "impressionRate": "ImpRate", "adDisplay": "ViewImp", "activeViewRate": "ViewImpRate", "adClick": "AdClick", "clickRate": "CTR", "originalRevenue": "Org. Rev.", "ecpm": "eCPM", "ecpc": "eCPC", "results": "results", "perPage": "Items per page", "gamEmail": "GAM"}, "modal": {"tableColumnSettings": "Table Column Settings", "selectColumns": "Select columns to display", "categoryBasic": "Group Columns", "categoryUser": "User Data", "categoryAd": "Ad Data", "categoryRevenue": "Revenue Data"}, "group": {"date": "Date", "domain": "Domain", "channel": "Affiliate", "advertiser": "Advertiser", "country": "Country", "adUnit": "AdUnit"}, "sync": {"success": "Sync successful", "error": "Sync failed", "inProgress": "Syncing data...", "modalTitle": "Sync", "dateRangeHint": "Please select the date range for synchronization:", "confirm": "Start Sync", "selectDateRange": "Please select a date range", "successMessage": "Data synchronized successfully", "defaultError": "Unknown error", "errorWithMessage": "Sync failed: {message}"}, "upload": {"buttonText": "Import", "success": "Import successful", "error": "Import failed", "inProgress": "Importing...", "successMessage": "Data imported successfully", "defaultError": "Unknown error", "errorWithMessage": "Import failed: {message}", "fileTypeHint": "Supports .xls, .xlsx formats"}}
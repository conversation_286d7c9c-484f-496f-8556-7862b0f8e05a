{"title": "Game Management", "filter": {"title": "Data Filter", "collapse": "Collapse Filter", "expand": "Expand Filter", "collapse_btn": "Collapse", "expand_btn": "Expand"}, "search": {"name": "Name", "name_placeholder": "Search game name", "category": "Category", "category_placeholder": "Game category", "enable": "Enable", "enable_placeholder": "Enable status", "featured": "Featured", "featured_placeholder": "Featured status"}, "status": {"enabled": "Enabled", "disabled": "Disabled", "featured": "Featured", "not_featured": "Not Featured"}, "buttons": {"search": "Search", "reset": "Reset", "save": "Save", "cancel": "Cancel", "edit": "Edit"}, "table": {"id": "Game ID", "name": "Game Name", "icon": "Game Icon", "category": "Game Category", "hot": "Popularity", "featured": "Featured", "enable": "Status", "create_time": "Create Time", "actions": "Actions", "no_icon": "No Icon", "results": "results", "per_page": "per page"}, "edit_modal": {"title": "Edit Game Information", "name": "Game Name", "name_placeholder": "Please enter game name", "uri": "Game URI", "uri_placeholder": "Please enter game URI", "category": "Game Category", "category_placeholder": "Please select game category", "hot": "Popularity", "hot_placeholder": "Please enter popularity value", "description": "Game Description", "description_placeholder": "Please enter game description", "enable": "Enable Game"}, "game_type": {"h5": "H5 Game", "mini": "Mini Game", "web": "Web Game"}, "messages": {"update_success": "Update successful", "update_failed": "Update failed", "get_list_error": "Failed to get game list", "get_category_error": "Failed to get game categories"}}
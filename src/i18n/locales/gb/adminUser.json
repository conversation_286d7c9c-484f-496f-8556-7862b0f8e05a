{"filter": {"title": "Data Filter", "collapse": "Collapse", "expand": "Expand", "collapseFilter": "Collapse Filter", "expandFilter": "Expand Filter", "status": "Status", "statusPlaceholder": "Status", "search": "Search", "searchPlaceholder": "Search"}, "buttons": {"add": "Add", "search": "Search", "reset": "Reset", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "edit": "Edit", "resetPassword": "Reset Password", "prevPage": "Previous", "nextPage": "Next"}, "table": {"nickname": "Nickname", "role": "Role", "loginAccount": "<PERSON><PERSON> Account", "twoFactorAuth": "Two-Factor Auth", "status": "Status", "actions": "Actions", "results": "results", "perPage": "Items per page"}, "form": {"editUser": "Edit User", "addUser": "Add User", "nickname": "Nickname", "loginAccount": "<PERSON><PERSON> Account", "role": "Role", "initialPassword": "Initial Password", "passwordPlaceholder": "Please enter password", "active": "Active"}, "resetPassword": {"title": "Reset Password", "titleWithName": "Reset Password - {name}", "newPassword": "New Password", "oldPassword": "Old Password", "confirmPassword": "Confirm Password", "passwordRequired": "Password is required", "passwordLength": "Password must be at least 6 characters", "confirmPasswordRequired": "Confirm password is required", "passwordMismatch": "Passwords do not match"}, "status": {"enabled": "Enabled", "disabled": "Disabled"}, "messages": {"userUpdated": "{name} has been updated", "userCreated": "{name} has been created", "userDeleted": "{name} has been deleted", "passwordResetSuccess": "Password reset successful", "deleteConfirmTitle": "Delete User", "deleteConfirmMessage": "Are you sure you want to delete {name}?"}, "validation": {"required": "This field is required"}}
{"title": "Notifications", "tabs": {"all": "All Notifications", "unread": "Unread", "read": "Read"}, "list": {"title": "Anomaly Notifications", "noMessages": "No notifications", "total": "{count} results"}, "detail": {"back": "Back to List", "markAsRead": "<PERSON> <PERSON>", "markSuccess": "Mark<PERSON> successfully", "markFailed": "Failed to mark", "notificationTime": "Notification Time", "dataDate": "Data Date", "status": "Status"}, "status": {"unread": "Unread", "read": "Read"}, "level": {"critical": "Critical", "warning": "Warning", "info": "Info", "unknown": "Unknown"}, "stats": {"anomalies": "Total Anomalies", "anomaliesDesc": "Number of detected anomalies", "domains": "Affected Domains", "domainsDesc": "Number of domains with anomalies", "adSlots": "Ad Slot Anomalies", "adSlotsDesc": "Number of affected ad slots", "avgDecline": "Average Decline", "avgDeclineDesc": "Average decline percentage of all anomalies", "chartTitle": "Anomaly Data Chart"}, "metrics": {"requestDecline": "Request Decline Rate", "revenueDecline": "Revenue Decline Rate", "matchRate": "Ad Match Rate"}, "chart": {"title": "{metric} Anomaly Data", "declinePercent": "Decline Percentage", "threshold": "<PERSON><PERSON><PERSON><PERSON>", "percentLabel": "Percentage (%)"}, "table": {"title": "Anomaly Details", "filterByMetric": "Filter by <PERSON><PERSON>", "filterByDomain": "Filter by Domain", "domain": "Domain", "adSlot": "Ad Slot", "metric": "Metric", "currentValue": "Current Value", "previousValue": "Previous Value", "declineNum": "Change Value", "declinePercent": "Change Percentage", "threshold": "<PERSON><PERSON><PERSON><PERSON>", "time": "Time"}, "notification": {"newMessage": "New Notification", "viewDetails": "View Details", "anomalyDetected": "Anomaly Detected", "clickToView": "Click to view details"}}
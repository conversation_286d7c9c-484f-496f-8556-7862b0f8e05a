{"filter": {"title": "Data Filter", "collapse": "Collapse", "expand": "Expand", "collapseFilter": "Collapse Filter", "expandFilter": "Expand Filter", "status": "Status", "statusPlaceholder": "Select site status", "advertiser": "Advertiser", "advertiserPlaceholder": "Select advertiser", "type": "Type", "typePlaceholder": "Select type"}, "buttons": {"add": "Add", "reset": "Reset", "search": "Search", "cancel": "Cancel", "confirm": "Confirm", "edit": "Edit"}, "table": {"id": "ID", "name": "Name", "hashOfferId": "Hash Offer ID", "advertiser": "Advertiser", "status": "Status", "type": "Type", "advUrl": "Adv URL", "affUrl": "Aff URL", "cap": "Cap", "creator": "Creator", "createTime": "Create Time", "updator": "Up<PERSON>tor", "updateTime": "Update Time", "actions": "Actions", "results": "results", "perPage": "Items per page"}, "form": {"addTitle": "Add Search Offer", "editTitle": "Edit Search Offer", "name": "Name", "namePlaceholder": "Please enter name", "nameRequired": "Name is required", "status": "Status", "statusPlaceholder": "Please select status", "statusRequired": "Status is required", "url": "URL", "urlPlaceholder": "Please enter URL", "urlRequired": "URL is required", "cap": "Cap", "capPlaceholder": "Please enter Cap", "advertiser": "Advertiser", "advertiserPlaceholder": "Please select advertiser", "advertiserRequired": "Advertiser is required", "type": "Type", "typePlaceholder": "Please select type", "typeRequired": "Type is required", "note": "Note", "notePlaceholder": "Please enter content"}, "status": {"connecting": "Connecting", "pending": "Pending", "online": "Online", "promoting": "Promoting", "paused": "Paused", "unknown": "Unknown Status"}, "type": {"yahoo": "Yahoo", "bing": "<PERSON>", "google": "Google", "ghs": "GHS", "bhs": "BHS", "b_n2s": "B_N2S", "y_n2s": "Y_N2S", "g_afd": "G_AFD", "g_afs": "G_AFS", "y_afs": "Y_AFS", "yangdex": "Yangdex", "unknown": "Unknown Type"}, "messages": {"fetchFailed": "Failed to fetch data", "updateSuccess": "Updated successfully", "updateFailed": "Update failed: {msg}", "addSuccess": "Added successfully", "addFailed": "Add failed: {msg}", "operationError": "Operation error: {msg}"}}
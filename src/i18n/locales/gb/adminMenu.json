{"filter": {"title": "Data Filter", "collapse": "Collapse", "expand": "Expand", "collapseFilter": "Collapse Filter", "expandFilter": "Expand Filter", "status": "Status", "statusPlaceholder": "Menu Status", "search": "Search", "searchPlaceholder": "Enter menu name"}, "buttons": {"add": "Add", "search": "Search", "reset": "Reset", "save": "Confirm", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "expand": "Expand"}, "table": {"expand": "Expand", "menuName": "<PERSON>u Name", "icon": "Icon", "orderNum": "Order", "perms": "Permission Key", "component": "Component Path", "status": "Status", "createTime": "Create Time", "actions": "Actions"}, "status": {"normal": "Normal", "disabled": "Disabled"}, "form": {"addMenu": "<PERSON><PERSON>", "editMenu": "<PERSON>", "parentMenu": "<PERSON><PERSON>", "parentMenuPlaceholder": "Click to select menu", "mainCategory": "Main Category", "menuType": "Menu Type", "directory": "Directory", "menu": "<PERSON><PERSON>", "button": "<PERSON><PERSON>", "menuName": "<PERSON>u Name", "menuNamePlaceholder": "Please enter menu name", "orderNum": "Order", "routePath": "Route Path", "routePathPlaceholder": "Please enter route path", "routeName": "Route Name", "routeNamePlaceholder": "Please enter route name", "isCache": "<PERSON><PERSON>", "yes": "Yes", "no": "No", "componentPath": "Component Path", "componentPathPlaceholder": "Please enter component path", "permIdentifier": "Permission Key", "permIdentifierPlaceholder": "Please enter permission key", "isExternal": "External Link", "displayStatus": "Display Status", "show": "Show", "hide": "<PERSON>de", "menuStatus": "Menu Status", "menuIcon": "Menu Icon"}, "iconSelector": {"title": "Select Icon", "searchPlaceholder": "Search icons...", "preview": "Click an icon below to select", "noMatch": "No matching icons", "tryOther": "Try another search term or category", "categories": {"common": "Common Icons", "navigation": "Navigation", "action": "Action", "communication": "Communication", "content": "Content", "editor": "Editor", "file": "File", "device": "<PERSON><PERSON>"}}, "validation": {"menuNameRequired": "Menu name cannot be empty", "orderNumRequired": "Menu order cannot be empty", "routePathRequired": "Route path cannot be empty"}, "messages": {"deleteConfirm": "Are you sure you want to delete the item named \"{name}\"?", "deleteSuccess": "Deleted successfully", "updateSuccess": "Updated successfully", "addSuccess": "Added successfully"}}
import { createI18n } from 'vue-i18n'

// 导入各语言的索引文件
const cnMessages = import.meta.glob('./locales/cn/*.json', {
  eager: true,
})
const gbMessages = import.meta.glob('./locales/gb/*.json', {
  eager: true,
})
// 处理导入的语言模块
function processLanguageModules(modules) {
  const result = {}
  // 处理每个模块文件
  Object.entries(modules).forEach(([path, module]) => {
    const pathParts = path.split('/')
    const fileName = pathParts[pathParts.length - 1].split('.json')[0]
    // 跳过索引文件，它只是用于引用其他文件
    if (fileName === 'index') return

    // 根据文件名确定模块类型
    switch (fileName) {
      case 'auth':
        result.auth = module.default
        break
      case 'common':
        // 将common中的内容展开到顶层
        Object.entries(module.default).forEach(([key, value]) => {
          result[key] = value
        })
        break
      case 'dashboard':
        result.dashboard = module.default
        break
      case 'navigation':
        // 将navigation中的内容展开到顶层
        Object.entries(module.default).forEach(([key, value]) => {
          result[key] = value
        })
        break
      case 'notifications':
        // 将notifications中的内容展开到顶层
        Object.entries(module.default).forEach(([key, value]) => {
          result[key] = value
        })
        break
      case 'ui-components':
        // 将ui-components中的内容展开到顶层
        Object.entries(module.default).forEach(([key, value]) => {
          result[key] = value
        })
        break
      case 'user':
        result.user = module.default
        break
      case 'vuestic':
        result.vuestic = module.default
        break
      case 'menu':
        // 将menu中的内容展开到顶层，这样可以直接使用菜单项的键
        Object.entries(module.default).forEach(([key, value]) => {
          result[key] = value
        })

        break
      default:
        // 其他模块直接添加
        result[fileName] = module.default
    }
  })

  return result
}

const messages = {
  cn: processLanguageModules(cnMessages),
  gb: processLanguageModules(gbMessages),
}
const languages = {
  english: 'English',
  simplified_chinese: '简体中文',
}
const languageCodes = {
  gb: languages.english,
  cn: languages.simplified_chinese,
}
// 从localStorage获取保存的语言设置，如果没有则使用默认语言
const savedLanguage = localStorage.getItem('language') || 'gb'
// 确保语言代码有效，如果无效则使用默认语言
const defaultLocale = Object.keys(languageCodes).includes(savedLanguage) ? savedLanguage : 'gb'
export default createI18n({
  legacy: false,
  locale: defaultLocale,
  fallbackLocale: 'gb',
  messages,
})

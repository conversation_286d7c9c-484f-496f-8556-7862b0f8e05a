import { defineStore } from 'pinia'
import { removeToken } from '../utils/index.js'
import { logout } from '../api/auth/index.js'
import { useTagsViewStore } from './tagsView'


export const useUserStore = defineStore('user', {
  state: () => {
    return {
      userName: '',
      account: '',
      email: '',
      memberSince: '8/12/2020',
      roles: [],
      roleInfo: {},
      pfp: 'https://picsum.photos/id/22/200/300',
      is2FAEnabled: JSON.parse(localStorage.getItem('is2FAEnabled') || 'false'),
      enableNavigation: JSON.parse(localStorage.getItem('enableNavigation') || 'true'), // 是否开启折叠导航
      language: localStorage.getItem('language') || 'cn', // 语言，默认使用中文
      permissions: [],
      admin: false,
      theme: localStorage.getItem('theme') || 'light',
      userSetting: [], // 用户通知设置
    }
  },

  actions: {
    setTheme(value) {
      this.theme = value
      localStorage.setItem('theme', value)
    },
    closeTwoFa() {
      this.is2FAEnabled = false
      localStorage.setItem('is2FAEnabled', JSON.stringify(this.is2FAEnabled))
    },
    openTwoFa() {
      this.is2FAEnabled = true
      localStorage.setItem('is2FAEnabled', JSON.stringify(this.is2FAEnabled))
    },
    changeUserName(userName) {
      this.userName = userName
    },
    setEnableNavigation(value) {
      this.enableNavigation = value
      localStorage.setItem('enableNavigation', JSON.stringify(value))
    },
    setLanguage(value) {
      this.language = value
      localStorage.setItem('language', value)
    },
    setUserInfo(userInfo) {
      this.userName = userInfo.userName
      this.email = userInfo.email
      this.memberSince = userInfo.memberSince
      this.roles = userInfo.roles
      this.pfp = userInfo.pfp
      this.account = userInfo.account
      if (userInfo.permissions) {
        this.permissions = userInfo.permissions
      }
      if (userInfo.admin !== undefined) {
        this.admin = userInfo.admin
      }
    },
    // 从API响应设置用户信息
    setUserInfoFromAPI(userInfo) {
      // 将API返回的用户信息转换为store中使用的格式
      this.userName = userInfo.name
      this.account = userInfo.account
      this.email = '' // API中没有提供email字段
      this.memberSince = userInfo.createTime ? new Date(userInfo.createTime * 1000).toLocaleDateString() : ''
      this.roles = userInfo.roles || []
      this.pfp = userInfo.avatar || 'https://picsum.photos/id/22/200/300'
      this.permissions = userInfo.permissions || []
      this.admin = userInfo.admin || false
      this.roleInfo = userInfo.role || {}
      this.userSetting = userInfo.userSetting || []
    },
    // 登出
    async logout() {
      await logout()
      removeToken()
      this.roles = []
      this.permissions = []
      // 清除缓存视图
      const tagsViewStore = useTagsViewStore()
      tagsViewStore.delAllCachedViews()
    },
  },
})

import { defineStore } from 'pinia'
export const useTagsViewStore = defineStore('tagsView', {
    state: () => ({
        cachedViews: ['Dashboard'], // 存储需要缓存的组件名称
    }),

    actions: {
        // 添加缓存视图
        addCachedView(view) {
            // 如果设置了 noCache 为 true 或没有 name，则不缓存
            if (view.meta && view.meta.noCache || !view.name) {
                return
            }
            if (this.cachedViews.includes(view.name)) {
                return
            }
            this.cachedViews.push(view.name)
        },

        // 删除缓存视图
        delCachedView(view) {
            const index = this.cachedViews.indexOf(view.name)
            if (index > -1) {
                this.cachedViews.splice(index, 1)
            }
        },
        // 删除其他缓存视图
        delOthersCachedViews(view) {
            if (view.name) {
                this.cachedViews = this.cachedViews.filter(name => name === view.name)
            } else {
                this.cachedViews = []
            }
        },
        // 删除所有缓存视图
        delAllCachedViews() {
            this.cachedViews = []
        }
    }
}) 
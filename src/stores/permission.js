import auth from '../plugins/auth'
import router, { constantRoutes, dynamicRoutes } from '@/router'
import { getRouters } from '@/api/menu/menu'
import Layout from '@/layouts/AppLayout.vue'
import ParentView from '@/components/ParentView.vue'
import InnerLink from '@/layout/components/InnerLink.vue'
import { defineStore } from 'pinia'

// 预加载所有页面组件
const pageModules = import.meta.glob(['/src/pages/**/*.vue', '/src/components/**/*.vue', '/src/layout/**/*.vue'])

export const usePermissionStore = defineStore('permission', {

    state: () => {
        return {
            routes: [],
            addRoutes: [],
            defaultRoutes: [],
            topbarRouters: [],
            sidebarRouters: [],
            hasRoutes: false // 标记动态路由是否已加载
        }
    },
    actions: {
        // 生成路由
        async GenerateRoutes() {
            let res = await getRouters()
            const sdata = JSON.parse(JSON.stringify(res.data))
            const rdata = JSON.parse(JSON.stringify(res.data))
            const sidebarRoutes = filterAsyncRouter(sdata)
            const rewriteRoutes = filterAsyncRouter(rdata, false, true)
            const asyncRoutes = [...dynamicRoutes, ...rewriteRoutes]
            // 使用 addRoute 方法添加动态路由
            asyncRoutes.forEach(route => {
                router.addRoute(route)
            });

            // 确保404路由在最后添加，避免提前匹配
            if (!router.hasRoute('404')) {
                router.addRoute({
                    name: '404-dynamic',
                    path: '/:pathMatch(.*)*',
                    redirect: '/404',
                    hidden: true
                })
            }
            this.addRoutes = rewriteRoutes
            this.routes = constantRoutes.concat(rewriteRoutes)
            this.defaultRoutes = constantRoutes.concat(sidebarRoutes)
            this.topbarRouters = sidebarRoutes
            this.sidebarRouters = dynamicRoutes.concat(sidebarRoutes)
            // 标记动态路由已加载
            this.hasRoutes = true

            return rewriteRoutes
        },
    },

})
// 遍历后台传来的路由字符串，转换为组件对象
function filterAsyncRouter(asyncRouterMap, lastRouter = false, type = false) {
    return asyncRouterMap.filter(route => {
        if (type && route.children) {
            route.children = filterChildren(route.children)
        }
        if (route.component) {
            // Layout ParentView 组件特殊处理
            if (route.component === 'Layout') {
                route.component = Layout
            } else if (route.component === 'ParentView') {
                route.component = ParentView
            } else if (route.component === 'InnerLink') {
                route.component = InnerLink
            } else {
                route.component = loadView(route.component)
            }
        }
        if (route.children != null && route.children && route.children.length) {
            route.children = filterAsyncRouter(route.children, route, type)
        } else {
            delete route['children']
            delete route['redirect']
        }
        return true
    })
}

function filterChildren(childrenMap, lastRouter = false) {
    var children = []
    childrenMap.forEach((el, index) => {
        if (el.children && el.children.length) {
            if (el.component === 'ParentView' && !lastRouter) {
                el.children.forEach(c => {
                    c.path = el.path + '/' + c.path
                    if (c.children && c.children.length) {
                        children = children.concat(filterChildren(c.children, c))
                        return
                    }
                    children.push(c)
                })
                return
            }
        }
        if (lastRouter) {
            el.path = lastRouter.path + '/' + el.path
            if (el.children && el.children.length) {
                children = children.concat(filterChildren(el.children, el))
                return
            }
        }
        children = children.concat(el)
    })
    return children
}

// 动态路由遍历，验证是否具备权限
export function filterDynamicRoutes(routes) {
    const res = []
    routes.forEach(route => {
        if (route.permissions) {
            if (auth.hasPermiOr(route.permissions)) {
                res.push(route)
            }
        } else if (route.roles) {
            if (auth.hasRoleOr(route.roles)) {
                res.push(route)
            }
        }
    })
    return res
}

// 处理组件路径
export const loadView = (view) => {
    // 移除可能的 .vue 后缀
    if (view.endsWith('.vue')) {
        view = view.slice(0, -4)
    }

    // 构建可能的路径列表
    const possiblePaths = []

    // 原始路径（如果view已经包含完整路径）
    possiblePaths.push(`/src/${view}.vue`)

    // 如果view以pages/或components/开头
    if (view.startsWith('pages/') || view.startsWith('components/')) {
        possiblePaths.push(`/src/${view}.vue`)
    } else {
        // 尝试不同目录下的路径
        possiblePaths.push(`/src/pages/${view}.vue`)
        possiblePaths.push(`/src/components/${view}.vue`)
        possiblePaths.push(`/src/views/${view}.vue`)
    }

    // 尝试所有可能的路径
    for (const path of possiblePaths) {
        if (pageModules[path]) {
            return pageModules[path]
        }
    }

    // 返回404页面
    return () => import('../pages/404.vue')
}
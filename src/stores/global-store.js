import { defineStore } from 'pinia'

export const useGlobalStore = defineStore('global', {
  state: () => {
    return {
      isSidebarMinimized: false,
      enableNavigation: false, // 是否开启面包屑
      deviceType: 'desktop', // 当前设备类型：'desktop'、'tablet'、'mobile'
    }
  },

  actions: {
    toggleSidebar() {
      this.isSidebarMinimized = !this.isSidebarMinimized
    },

    setDeviceType(type) {
      this.deviceType = type
    },

    // 根据当前设备类型收起侧边栏（仅在移动设备上）
    minimizeSidebarIfMobile() {
      if (this.deviceType === 'mobile' || this.deviceType === 'tablet') {
        this.isSidebarMinimized = true
      }
    },
  },
})

export const earningsColor = '#49A8FF'
export const expensesColor = '#154EC1'

export const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']


export const generateRevenues = (months) => {
  return months.map((month) => {
    const earning = Math.floor(Math.random() * 100000 + 10000)
    return {
      month,
      earning,
      expenses: Math.floor(earning * Math.random()),
    }
  })
}

export const getRevenuePerMonth = (month, revenues) => {
  const revenue = revenues.find((revenue) => revenue.month === month)
  return revenue || { month, earning: 0, expenses: 0 }
}

export const formatMoney = (amount, currency = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount)
}

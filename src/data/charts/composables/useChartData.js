import { computed } from 'vue'
import { useChartColors } from './useChartColors'


export function useChartData(data, alfa) {
  const datasetsColors = data.datasets.map((dataset) => dataset.backgroundColor)

  const datasetsThemesColors = datasetsColors.map(
    (colors) => useChartColors(colors, alfa)[alfa ? 'generatedHSLAColors' : 'generatedColors'],
  )

  return computed(() => {
    const datasets = data.datasets.map((dataset, idx) => ({
      ...dataset,
    }))

    return { ...data, datasets }
  })
}

<template>
  <div :class="{ 'app--with-footer': !isLogin }">
    <div class="copyright text-center text-sm text-gray-500 absolute bottom-[20px] w-full" v-if="!isLogin">
      <p>Copyright © 2025 SubDigi Network. All Rights  Reserved.</p>
    </div>
    <RouterView />
  </div>
</template>
<script setup lang="ts">
import { useUserStore } from './stores/user-store'
import { usePermissionStore } from './stores/permission'
import { onMounted, computed } from 'vue'
import { useColors } from 'vuestic-ui'
import { getUserInfo } from '@/api/auth'
const { applyPreset } = useColors()
const userStore = useUserStore()
const permissionStore = usePermissionStore()
import { useRoute } from 'vue-router'
const route = useRoute()
const isLogin = computed(() => route.fullPath === '/auth/login')
onMounted(async () => {
  await Promise.all([getUserInfoFn(), getRoutersFn()])
  applyPreset(userStore.theme)
})

const getRoutersFn = async () => {
// 调用 permission store 的 GenerateRoutes 方法处理动态路由
await permissionStore.GenerateRoutes()
}

// 获取用户信息
const getUserInfoFn = async () => {
try {
  const res = (await getUserInfo())
  if (res.code === 200 && res.data) {
    // 保存用户信息到store
    userStore.setUserInfoFromAPI(res.data)
    // 保存用户名到localStorage
    localStorage.setItem('userName', res.data.name || res.data.account || formData.username)
    // 检查是否启用了双因素认证
    if (res.data.verifyTwoSecret) {
      userStore.openTwoFa()
    }
    return res.data
  }
} catch (error) {
  console.error('获取用户信息失败:', error)
}
return null
}
</script>
<style lang="scss">
.app--with-footer {
  padding-bottom: 40px;
  position: relative;
  min-height: 100vh;
}

#app {
  font-family: 'Inter', Avenir, Helvetica, Arial, sans-serif;
  min-height: 100vh;
  box-sizing: border-box; /* 关键：让 padding 计算在高度内 */
  margin: 0; /* 确保没有默认 margin 影响 */
}
body {
  margin: 0;
  min-width: 20rem;
}
</style>

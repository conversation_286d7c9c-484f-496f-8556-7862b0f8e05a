import { ref } from 'vue'

export function useData(options = {}) {
    const {
        defaultDateRange = 'week', // 默认日期范围: 'week', 'month', 'threedays', 'today', 'yesterday', 'custom'
        customDefaultRange = null  // 自定义默认日期范围，当defaultDateRange为'custom'时使用
    } = options;

    // 日期选择相关
    const dateRange = ref([])

    /**
     * 初始化日期范围
     */
    const initDateRange = () => {
        switch (defaultDateRange) {
            case 'week':
                selectLastWeek();
                break;
            case 'month':
                selectLastMonth();
                break;
            case 'threedays':
                selectLastThreeDays();
                break;
            case 'today':
                selectToday();
                break;
            case 'yesterday':
                selectYesterday();
                break;
            case 'custom':
                if (Array.isArray(customDefaultRange) && customDefaultRange.length === 2) {
                    dateRange.value = customDefaultRange;
                } else {
                    selectLastWeek(); // 默认回退到一周
                }
                break;
            default:
                selectLastWeek(); // 默认为最近一周
        }
    }

    /**
     * 日期快捷选择函数，可选择最近的天数
     * @param {number} days 过去的天数
     * @param {function} queryFn 查询函数，选择日期后会自动调用
     */
    const selectRecentDays = (days, queryFn) => {
        const today = new Date()
        const pastDate = new Date()
        pastDate.setDate(today.getDate() - days)
        dateRange.value = [pastDate, today]

        if (typeof queryFn === 'function') {
            queryFn()
        }
    }

    // 预设快捷选择函数
    const selectLastThreeDays = (queryFn) => selectRecentDays(3, queryFn)
    const selectLastWeek = (queryFn) => selectRecentDays(6, queryFn)
    const selectLastMonth = (queryFn) => selectRecentDays(29, queryFn)

    /**
     * 处理日期范围变化事件
     * @param {Array} value 日期范围数组
     * @param {function} queryFn 查询函数
     */
    const onDateRangeChange = (value, queryFn) => {
        if (value && value.length === 2 && typeof queryFn === 'function') {
            // 当选择了有效的日期范围时执行查询
            queryFn()
        }
    }

    /**
     * 格式化日期为MM-DD格式
     * @param {number} timestamp 时间戳
     * @returns {string} 格式化后的日期字符串
     */
    const formatDate = (timestamp) => {
        if (!timestamp) return '--'

        const date = new Date(timestamp * 1000)
        const month = (date.getMonth() + 1).toString().padStart(2, '0')
        const day = date.getDate().toString().padStart(2, '0')
        return `${month}-${day}`
    }

    /**
     * 设置日期范围为今天
     * @param {function} queryFn 查询函数
     */
    const selectToday = (queryFn) => {
        const today = new Date()
        dateRange.value = [today, today]

        if (typeof queryFn === 'function') {
            queryFn()
        }
    }

    /**
     * 设置日期范围为昨天
     * @param {function} queryFn 查询函数
     */
    const selectYesterday = (queryFn) => {
        const yesterday = new Date()
        yesterday.setDate(yesterday.getDate() - 1)
        dateRange.value = [yesterday, yesterday]

        if (typeof queryFn === 'function') {
            queryFn()
        }
    }

    /**
     * 设置日期范围为本周
     * @param {function} queryFn 查询函数
     */
    const selectThisWeek = (queryFn) => {
        const today = new Date()
        const firstDayOfWeek = new Date(today)
        const day = today.getDay() || 7 // 将周日的0转换为7
        firstDayOfWeek.setDate(today.getDate() - day + 1) // 设置为本周一

        dateRange.value = [firstDayOfWeek, today]

        if (typeof queryFn === 'function') {
            queryFn()
        }
    }

    /**
     * 设置日期范围为上周
     * @param {function} queryFn 查询函数
     */
    const selectLastFullWeek = (queryFn) => {
        const today = new Date()
        const lastWeekEnd = new Date(today)
        const day = today.getDay() || 7 // 将周日的0转换为7

        // 设置为上周日
        lastWeekEnd.setDate(today.getDate() - day)

        // 设置为上周一
        const lastWeekStart = new Date(lastWeekEnd)
        lastWeekStart.setDate(lastWeekEnd.getDate() - 6)

        dateRange.value = [lastWeekStart, lastWeekEnd]

        if (typeof queryFn === 'function') {
            queryFn()
        }
    }

    // 初始化默认日期范围
    initDateRange();

    return {
        dateRange,
        selectRecentDays,
        selectLastThreeDays,
        selectLastWeek,
        selectLastMonth,
        selectToday,
        selectYesterday,
        selectThisWeek,
        selectLastFullWeek,
        onDateRangeChange,
        formatDate,
        initDateRange
    }
}
import { ref, nextTick, watch } from 'vue'

/**
 * 提供折叠/展开动画的通用逻辑
 * @param {Object} options 配置选项
 * @param {boolean} options.defaultExpanded 默认是否展开
 * @param {number} options.breakpoint 响应式断点，小于此宽度将自动折叠 (默认为992px)
 * @param {number} options.animationDuration 动画持续时间(毫秒) (默认为300ms)
 * @returns {Object} 返回折叠/展开相关的状态和方法
 */
export function useTransition(options = {}) {
    const {
        defaultExpanded = true,
        breakpoint = 992,
        animationDuration = 300
    } = options

    // 状态变量
    const isExpanded = ref(defaultExpanded)
    const contentRef = ref(null)
    const contentHeight = ref('auto')
    const isAnimating = ref(false)
    const isMobileView = ref(false)

    /**
     * 检测当前视图是否为移动设备
     */
    const checkMobileView = () => {
        const prevIsMobile = isMobileView.value
        isMobileView.value = window.innerWidth < breakpoint

        // 如果从PC视图切换到移动视图，则默认折叠
        if (!prevIsMobile && isMobileView.value) {
            collapse(false) // 无动画折叠
        }

        // 如果从移动视图切换到PC视图，则始终展开
        if (prevIsMobile && !isMobileView.value) {
            expand(false) // 无动画展开
        }
    }

    /**
     * 切换折叠/展开状态
     */
    const toggle = () => {
        if (isAnimating.value) return // 避免动画过程中重复触发

        if (isExpanded.value) {
            collapse(true)
        } else {
            expand(true)
        }
    }

    /**
     * 展开内容
     * @param {boolean} animate 是否使用动画
     */
    const expand = (animate = true) => {
        if (isAnimating.value) return

        // 如果不需要动画，直接设置状态
        if (!animate) {
            isExpanded.value = true
            contentHeight.value = 'auto'
            return
        }

        isAnimating.value = true
        isExpanded.value = true

        nextTick(() => {
            if (!contentRef.value) {
                isAnimating.value = false
                return
            }

            // 首先测量内容的实际高度
            const height = contentRef.value.scrollHeight

            // 从0开始过渡到目标高度
            contentHeight.value = '0px'

            // 强制重排
            contentRef.value.offsetHeight

            // 设置过渡到目标高度
            requestAnimationFrame(() => {
                contentHeight.value = `${height}px`

                // 过渡完成后设置为auto
                setTimeout(() => {
                    contentHeight.value = 'auto'
                    isAnimating.value = false
                }, animationDuration)
            })
        })
    }

    /**
     * 折叠内容
     * @param {boolean} animate 是否使用动画
     */
    const collapse = (animate = true) => {
        if (isAnimating.value || !contentRef.value) return

        // 如果不需要动画，直接设置状态
        if (!animate) {
            isExpanded.value = false
            contentHeight.value = '0px'
            return
        }

        isAnimating.value = true

        // 先设置为当前高度，然后过渡到0
        const height = contentRef.value.scrollHeight
        contentHeight.value = `${height}px`

        // 强制重排
        contentRef.value.offsetHeight

        requestAnimationFrame(() => {
            // 添加过渡
            contentHeight.value = '0px'

            // 过渡完成后更新状态
            setTimeout(() => {
                isExpanded.value = false
                isAnimating.value = false
            }, animationDuration)
        })
    }

    /**
     * 设置内联样式
     * @returns {Object} 内联样式对象
     */
    const getContentStyles = () => {
        return {
            height: contentHeight.value,
            overflow: contentHeight.value === 'auto' ? 'visible' : 'hidden',
            opacity: isExpanded.value ? 1 : 0,
            transition: `height ${animationDuration}ms ease-in-out, opacity ${animationDuration}ms ease-in-out`
        }
    }

    // 监听移动设备状态变化，确保PC端始终展开
    watch(isMobileView, (newValue) => {
        if (!newValue && !isExpanded.value) { // 如果不是移动设备且当前是折叠状态
            expand(true) // PC端始终展开
        }
    })

    // 提供一个初始化函数，在组件挂载时调用
    const init = () => {
        checkMobileView() // 初始检测视图类型
        window.addEventListener('resize', checkMobileView) // 添加窗口大小变化监听
    }

    // 提供一个清理函数，在组件卸载时调用
    const cleanup = () => {
        window.removeEventListener('resize', checkMobileView)
    }

    return {
        // 状态
        isExpanded,
        contentRef,
        contentHeight,
        isAnimating,
        isMobileView,

        // 方法
        toggle,
        expand,
        collapse,
        checkMobileView,
        getContentStyles,
        init,
        cleanup
    }
}

import { ref, reactive, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'

/**
 * 广告高亮标记Hook
 * 用于处理广告数据低于阈值时的高亮显示
 */
export function useAdHighlight() {
    const { t } = useI18n()

    // 默认配置
    const defaultConfig = {
        clickRate: {
            threshold: 1, // 默认阈值1%
            color: '#FF6B6B' // 默认红色
        },
        impressionRate: {
            threshold: 60, // 默认阈值60%
            color: '#FF9E7A' // 默认橙色
        },
        matchRate: {
            threshold: 60, // 默认阈值60%
            color: '#FFC759' // 默认黄色
        },
        activeViewRate: {
            threshold: 60, // 默认阈值60%
            color: '#5FA8D3' // 默认蓝色
        }
    }

    // 当前配置
    const config = reactive({ ...defaultConfig })

    // 配置是否已加载
    const configLoaded = ref(false)

    // 从localStorage加载配置
    const loadConfig = () => {
        try {
            const savedConfig = localStorage.getItem('adHighlightConfig')
            if (savedConfig) {
                const parsedConfig = JSON.parse(savedConfig)
                // 合并配置，确保所有字段都存在
                Object.keys(defaultConfig).forEach(key => {
                    if (parsedConfig[key]) {
                        config[key] = {
                            ...defaultConfig[key],
                            ...parsedConfig[key]
                        }
                    }
                })
            }
            configLoaded.value = true
        } catch (error) {
            console.error('Failed to load ad highlight config:', error)
        }
    }

    // 保存配置到localStorage
    const saveConfig = () => {
        try {
            localStorage.setItem('adHighlightConfig', JSON.stringify(config))
            return true
        } catch (error) {
            console.error('Failed to save ad highlight config:', error)
            return false
        }
    }

    // 重置配置为默认值
    const resetConfig = () => {
        Object.keys(defaultConfig).forEach(key => {
            config[key] = { ...defaultConfig[key] }
        })
        saveConfig()
    }

    // 判断是否需要高亮
    const shouldHighlight = (fieldName, value) => {
        if (!configLoaded.value || value === undefined || value === null) return false

        // 确保字段名存在于配置中
        if (!config[fieldName]) return false

        // 数值比较：如果值小于阈值，则需要高亮
        return parseFloat(value) < config[fieldName].threshold && parseFloat(value) > 0
    }

    // 获取高亮样式
    const getHighlightStyle = (fieldName) => {
        if (!configLoaded.value || !config[fieldName]) return {}

        return {
            color: config[fieldName].color,
            fontWeight: 'bold'
        }
    }

    // 获取字段配置列表（用于设置UI）
    const getFieldConfigs = () => {
        return Object.keys(config).map(key => ({
            key,
            label: t(`preferences.adHighlight.fields.${key}`),
            description: t(`preferences.adHighlight.descriptions.${key}`),
            threshold: config[key].threshold,
            color: config[key].color
        }))
    }

    // 更新字段配置
    const updateFieldConfig = (fieldName, threshold, color) => {
        if (!config[fieldName]) return false

        if (threshold !== undefined) {
            config[fieldName].threshold = parseFloat(threshold)
        }

        if (color !== undefined) {
            config[fieldName].color = color
        }

        return true
    }

    // 组件挂载时加载配置
    onMounted(() => {
        loadConfig()
    })

    return {
        config,
        configLoaded,
        loadConfig,
        saveConfig,
        resetConfig,
        shouldHighlight,
        getHighlightStyle,
        getFieldConfigs,
        updateFieldConfig
    }
} 
import { useUserStore } from '../../stores/user-store'


export default {
  mounted(el, binding) {
    const userStore = useUserStore()
    const { value } = binding
    const super_admin = 'admin'
    const roles = userStore.roles

    if (value && value instanceof Array && value.length > 0) {
      const roleFlag = value

      const hasRole = roles.some((role) => {
        return super_admin === role || roleFlag.includes(role)
      })

      if (!hasRole) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error(`请设置角色权限标签值`)
    }
  },

  // 添加 updated 钩子，以便在角色变化时重新检查
  updated(el, binding) {
    const userStore = useUserStore()
    const { value } = binding
    const super_admin = 'admin'
    const roles = userStore.roles

    if (value && value instanceof Array && value.length > 0) {
      const roleFlag = value

      const hasRole = roles.some((role) => {
        return super_admin === role || roleFlag.includes(role)
      })

      if (!hasRole) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error(`请设置角色权限标签值`)
    }
  },
}

import { useUserStore } from '@/stores/user-store'

export default {
  mounted(el, binding) {
    const userStore = useUserStore()
    const { value } = binding
    const all_permission = '*:*:*'
    const permissions = userStore.permissions

    if (value && value instanceof Array && value.length > 0) {
      const permissionFlag = value

      const hasPermissions = permissions.some((permission) => {
        return all_permission === permission || permissionFlag.includes(permission)
      })

      if (!hasPermissions) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error(`请设置操作权限标签值`)
    }
  },

  // 添加 updated 钩子，以便在权限变化时重新检查
  updated(el, binding) {
    const userStore = useUserStore()
    const { value } = binding
    const all_permission = '*:*:*'
    const permissions = userStore.permissions

    if (value && value instanceof Array && value.length > 0) {
      const permissionFlag = value

      const hasPermissions = permissions.some((permission) => {
        return all_permission === permission || permissionFlag.includes(permission)
      })

      if (!hasPermissions) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error(`请设置操作权限标签值`)
    }
  },
}

<template>
    <VaCard class="filter-card">
        <!-- 筛选区域标题和控制按钮 -->
        <div class="filter-header flex justify-between items-center pb-2">
            <div class="flex items-center gap-2">
                <VaIcon name="mso-filter_list" color="primary" />
                <h2 class="text-lg font-medium">{{ t('adminMenu.filter.title') }}</h2>
            </div>
            <div class="flex gap-2">
                <!-- 仅在移动设备和平板上显示展开/折叠按钮 -->
                <VaButton v-if="isMobileView" preset="secondary" border-color="primary" size="small"
                    class="filter-toggle" @click="toggleFilter"
                    :icon="isFilterExpanded ? 'mso-expand_less' : 'mso-expand_more'"
                    :aria-label="isFilterExpanded ? t('adminMenu.filter.collapseFilter') : t('adminMenu.filter.expandFilter')">
                    {{ isFilterExpanded ? t('adminMenu.filter.collapse') : t('adminMenu.filter.expand') }}
                </VaButton>
            </div>
        </div>

        <!-- 筛选区域内容 - 使用JS动画 -->
        <div ref="filterContent" class="filter-content" :style="{
            ...getContentStyles(),
        }">
            <!-- 筛选表单 -->
            <div class="filter-form" v-show="isFilterExpanded">
                <!-- 筛选条件网格 -->
                <div class="filter-grid">
                    <!-- 搜索筛选 -->
                    <div class="filter-item">
                        <div class="filter-item-header">
                            <label class="filter-label">搜索</label>
                        </div>
                        <VaInput v-model="queryParams.menuName" placeholder="请输入菜单名称" @keyup.enter="handleQuery"
                            class="filter-input">
                            <template #appendInner>
                                <VaIcon name="search" />
                            </template>
                        </VaInput>
                    </div>

                    <!-- 状态筛选 -->
                    <div class="filter-item">
                        <div class="filter-item-header">
                            <label class="filter-label">状态</label>
                        </div>
                        <VaSelect v-model="queryParams.status" placeholder="菜单状态" :options="statusOptions"
                            text-by="label" value-by="value" class="filter-input" />
                    </div>
                </div>

                <!-- 操作按钮区域 -->
                <div class="filter-actions mt-4">
                    <VaButton color="primary" icon="mso-search" @click="handleQuery">搜索</VaButton>
                    <VaButton preset="secondary" border-color="primary" icon="mso-refresh" @click="resetQuery">重置
                    </VaButton>
                    <VaButton preset="secondary" border-color="primary" icon="mso-add" @click="handleAdd">新增
                    </VaButton>
                </div>
            </div>
        </div>
    </VaCard>

    <!-- Data Table -->
    <VaDataTable v-if="refreshTable" :items="menuList" :columns="columns" :loading="loading"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expanded-rows="isExpandAll ? menuList : []" row-key="id">

        <template #cell(expand)="{ row, isExpanded }">
            <VaButton v-if="row.rowData.children && row.rowData.children.length > 0"
                :icon="isExpanded ? 'va-arrow-up' : 'va-arrow-down'" preset="secondary" @click="row.toggleRowDetails()">
            </VaButton>
        </template>
        <!-- Icon Column -->
        <template #expandableRow="{ rowData }">
            <div class="submenu-container pl-6 py-2">
                <div v-if="!rowData || !rowData.children || rowData.children.length === 0" class="text-gray-500 italic">
                    <!-- 暂无子菜单 -->
                </div>
                <div v-else>
                    <VaDataTable :items="rowData.children || []" :columns="subMenuColumns" class="nested-table"
                        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" row-key="id">

                        <template #cell(expand)="{ row, isExpanded }">
                            <VaButton v-if="row.rowData.children && row.rowData.children.length > 0"
                                :icon="isExpanded ? 'va-arrow-up' : 'va-arrow-down'" preset="secondary"
                                @click="row.toggleRowDetails()">
                            </VaButton>
                        </template>

                        <template #expandableRow="{ rowData }">
                            <div class="submenu-container pl-6 py-2">
                                <div v-if="!rowData || !rowData.children || rowData.children.length === 0"
                                    class="text-gray-500 italic">
                                    <!-- 暂无子菜单 -->
                                </div>
                                <div v-else>
                                    <VaDataTable :items="rowData.children || []" :columns="subMenuColumns"
                                        class="nested-table"
                                        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" row-key="id">
                                        <!-- 递归模板 -->
                                        <template #cell(icon)="{ row }">
                                            <div class="flex justify-center">
                                                <VaIcon :name="row.icon || 'va-file'" />
                                            </div>
                                        </template>

                                        <template #cell(status)="{ row }">
                                            <VaBadge :text="row.status === '0' ? '停用' : '正常'"
                                                :color="row.status === '0' ? 'danger' : 'success'" />
                                        </template>

                                        <template #cell(createTime)="{ row }">
                                            {{ formatTime(row.createTime) }}
                                        </template>

                                        <template #cell(actions)="{ row }">
                                            <div class="flex gap-2">
                                                <VaButton size="small" icon="mso-edit" @click="handleUpdate(row)"
                                                    color="info">
                                                </VaButton>
                                                <VaButton size="small" icon="mso-add" @click="handleAdd(row)"
                                                    color="primary">
                                                </VaButton>
                                                <VaButton size="small" icon="mso-delete" @click="handleDelete(row)"
                                                    color="danger">
                                                </VaButton>
                                            </div>
                                        </template>
                                    </VaDataTable>
                                </div>
                            </div>
                        </template>

                        <template #cell(icon)="{ row }">
                            <div class="flex justify-center">
                                <VaIcon :name="row.icon || 'va-file'" />
                            </div>
                        </template>

                        <template #cell(status)="{ row }">
                            <VaBadge :text="row.status === '0' ? '停用' : '正常'"
                                :color="row.status === '0' ? 'danger' : 'success'" />
                        </template>

                        <template #cell(createTime)="{ row }">
                            {{ formatTime(row.createTime) }}
                        </template>

                        <template #cell(actions)="{ row }">
                            <div class="flex gap-2">
                                <VaButton size="small" icon="mso-edit" @click="handleUpdate(row)" color="info">
                                </VaButton>
                                <VaButton size="small" icon="mso-add" @click="handleAdd(row)" color="primary">
                                </VaButton>
                                <VaButton size="small" icon="mso-delete" @click="handleDelete(row)" color="danger">
                                </VaButton>
                            </div>
                        </template>
                    </VaDataTable>
                </div>
            </div>
        </template>
        <template #cell(icon)="{ row }">
            <div class="flex justify-center">
                <VaIcon :name="row.icon || 'va-file'" />
            </div>
        </template>
        <!-- Status Column -->
        <template #cell(status)="{ row }">
            <VaBadge :text="row.status === '0' ? '停用' : '正常'" :color="row.status === '0' ? 'danger' : 'success'" />
        </template>

        <!-- Create Time Column -->
        <template #cell(createTime)="{ row }">
            {{ formatTime(row.createTime) }}
        </template>
        <!-- Actions Column -->
        <template #cell(actions)="{ row }">
            <div class="flex gap-2">
                <VaButton size="small" icon="mso-edit" @click="handleUpdate(row)" :loading="treeLoading" color="info">
                </VaButton>
                <VaButton size="small" icon="mso-add" @click="handleAdd(row)" :loading="treeLoading" color="primary">
                </VaButton>
                <VaButton size="small" icon="mso-delete" @click="handleDelete(row)" color="danger"></VaButton>
            </div>
        </template>
    </VaDataTable>

    <!-- Add/Edit Dialog -->
    <VaModal :modelValue="open" @update:modelValue="open = $event" :title="title" hide-default-actions max-width="700px"
        close-button mobile-fullscreen>
        <div class="p-2">
            <VaForm ref="formRef" id="form" :model="formData" :rules="rules" class="flex flex-wrap justify-start ">
                <!-- 上级菜单 -->
                <!-- 菜单图标 -->
                <div class="form-item w-full" v-if="formData.menuType !== 'F'">
                    <label class="primary-label whitespace-nowrap">菜单图标</label>
                    <div class="flex w-full">
                        <VaButton preset="secondary" :icon="formData.icon" border-color="primary"
                            @click="showIconSelector = true">
                        </VaButton>
                    </div>
                </div>
                <div class="form-item w-full">
                    <label class="primary-label whitespace-nowrap">上级菜单</label>
                    <div class="relative w-full">
                        <VaInput v-model="parentMenuName" readonly placeholder="点击选择菜单" class="w-full"
                            @click="toggleParentMenu">
                            <template #appendInner>
                                <VaIcon name="mso-expand_more" />
                            </template>
                        </VaInput>
                        <div v-if="showParentMenu"
                            class="absolute top-full left-0 w-full bg-white shadow-lg z-10 rounded mt-1 max-h-[300px] overflow-y-auto">
                            <MenuTreeSelector v-model="formData.parentId" :menuOptions="menuOptions"
                                @update:modelValue="updateParentMenuName" selectionType="independent" />
                        </div>
                    </div>
                </div>

                <!-- 菜单类型 -->
                <div class="form-item w-full">
                    <label class="primary-label whitespace-nowrap">菜单类型</label>
                    <div class="flex items-center">
                        <VaRadio v-model="formData.menuType" label="目录" option="M" />
                        <VaRadio v-model="formData.menuType" label="菜单" option="C" class="ml-4" />
                        <VaRadio v-model="formData.menuType" label="按钮" option="F" class="ml-4" />
                    </div>
                </div>



                <!-- 菜单名称与显示排序 -->
                <div class="form-item w-1/2">
                    <label class="primary-label whitespace-nowrap">菜单名称</label>
                    <VaInput v-model="formData.name" placeholder="请输入菜单名称" requiredMark />
                </div>

                <div class="form-item w-1/2">
                    <label class="primary-label whitespace-nowrap">排序</label>
                    <VaInput v-model="formData.orderNum" type="number" min="0" requiredMark />
                </div>

                <!-- 路由地址 -->
                <div class="form-item w-1/2" v-if="formData.menuType !== 'F'">
                    <label class="primary-label whitespace-nowrap required">路由地址</label>
                    <VaInput v-model="formData.path" placeholder="请输入路由地址" requiredMark label />
                </div>
                <div class="form-item w-1/2" v-if="formData.menuType !== 'F'">
                    <label class="primary-label whitespace-nowrap required">路由名称</label>
                    <VaInput v-model="formData.routeName" placeholder="请输入路由名称" requiredMark label />
                </div>
                <div class="form-item w-1/2">
                    <label class="primary-label whitespace-nowrap required">是否缓存</label>
                    <div class="flex items-center">
                        <VaRadio v-model="formData.isCache" label="是" option="0" />
                        <VaRadio v-model="formData.isCache" label="否" option="1" class="ml-4" />
                    </div>
                </div>
                <!-- 组件路径 -->
                <div class="form-item w-1/2" v-if="formData.menuType === 'C'">
                    <label class="primary-label whitespace-nowrap required">组件路径</label>
                    <VaInput v-model="formData.component" placeholder="请输入组件路径" />
                </div>

                <!-- 权限标识 -->
                <div class="form-item w-1/2" v-if="formData.menuType !== 'M'">
                    <label class="primary-label whitespace-nowrap required">权限标识</label>
                    <VaInput v-model="formData.perms" placeholder="请输入权限标识" />
                </div>

                <!-- 是否外链 -->
                <div class="form-item w-1/2" v-if="formData.menuType !== 'F'">
                    <label class="primary-label whitespace-nowrap required">是否外链</label>
                    <div class="flex items-center">
                        <VaRadio v-model="formData.isFrame" label="是" option="0" />
                        <VaRadio v-model="formData.isFrame" label="否" option="1" class="ml-4" />
                    </div>
                </div>

                <!-- 显示状态 -->
                <div class="form-item w-1/2" v-if="formData.menuType !== 'F'">
                    <label class="primary-label whitespace-nowrap required">显示状态</label>
                    <div class="flex items-center">
                        <VaRadio v-model="formData.visible" label="显示" option="0" />
                        <VaRadio v-model="formData.visible" label="隐藏" option="1" class="ml-4" />
                    </div>
                </div>

                <!-- 菜单状态 -->
                <div class="form-item w-1/2">
                    <label class="primary-label whitespace-nowrap required">菜单状态</label>
                    <div class="flex items-center">
                        <VaRadio v-model="formData.status" label="正常" option="1" />
                        <VaRadio v-model="formData.status" label="停用" option="0" class="ml-4" />
                    </div>
                </div>
            </VaForm>
        </div>

        <template #footer>
            <div class="flex justify-end gap-2">
                <VaButton preset="secondary" @click="cancel">取 消</VaButton>
                <VaButton @click="submitForm">确 定</VaButton>
            </div>
        </template>
    </VaModal>

    <!-- Icon Selector Modal -->
    <VaModal :modelValue="showIconSelector" @update:modelValue="showIconSelector = $event" title="选择图标"
        max-width="750px" hide-default-actions close-button mobile-fullscreen>
        <div class="p-2">
            <!-- 搜索框 -->
            <div>
                <VaInput v-model="iconSearch" placeholder="搜索图标..." class="w-full" clearable>
                    <template #prependInner>
                        <VaIcon name="search" />
                    </template>
                </VaInput>


            </div>

            <!-- 分类标签 -->
            <div class="mb-4 mt-4">
                <VaTabs v-model="activeIconCategory">

                    <template #tabs>
                        <VaTab v-for="category in iconCategories" :key="category.name" :name="category.name">
                            {{ category.label }}
                        </VaTab>
                    </template>
                </VaTabs>
            </div>

            <!-- 选中的图标预览 -->
            <div v-if="previewIcon" class="mb-4  bg-gray-50 rounded-md flex items-center icon-preview">
                <div class="w-16 h-16 flex items-center justify-center border rounded-md mr-4 bg-white shadow-sm">
                    <VaIcon :name="previewIcon" size="large" />
                </div>
                <div>
                    <div class="text-lg font-medium">{{ previewIcon }}</div>
                    <div class="text-sm text-gray-500">点击下方图标可以选择</div>
                </div>
            </div>

            <!-- 图标网格 -->
            <div class="max-h-[400px] overflow-y-auto pr-2 icon-grid-container">
                <div v-if="filteredIcons.length > 0"
                    class="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-3">
                    <div v-for="icon in filteredIcons" :key="icon"
                        class="icon-item p-3 border rounded cursor-pointer transition-all duration-200 flex flex-col items-center justify-center"
                        :class="{ 'icon-selected': formData.icon === icon }" @click="selectIcon(icon)"
                        @mouseover="previewIcon = icon">
                        <VaIcon :name="icon" size="large" class="mb-2" />
                        <div class="text-xs text-center text-gray-600 icon-name">{{ icon }}</div>
                    </div>
                </div>
                <div v-else class="flex flex-col items-center justify-center py-10 text-gray-500">
                    <VaIcon name="search_off" size="large" class="mb-3" />
                    <div class="text-lg">无匹配图标</div>
                    <div class="text-sm">请尝试其他搜索词或切换类别</div>
                </div>
            </div>
        </div>

        <template #footer>
            <div class="flex justify-end gap-2">
                <VaButton preset="secondary" @click="showIconSelector = false">取消</VaButton>
                <VaButton @click="confirmIconSelection" :disabled="!formData.icon">确定</VaButton>
            </div>
        </template>
    </VaModal>

</template>

<script setup>
import { ref, reactive, onMounted, nextTick, watch, computed, onUnmounted } from "vue";
import { listMenu, getMenu, delMenu, addMenu, updateMenu } from "../../api/menu/menu";
import { handleTree } from '../../utils/subdigi';
import { useForm, useToast } from 'vuestic-ui';
import MenuTreeSelector from './components/MenuTreeSelector.vue';
import { useTransition } from '@/composables/useTransition.js';
import { useI18n } from 'vue-i18n';

// 使用 i18n
const { t } = useI18n();

// 使用过渡动画功能
const {
    isExpanded: isFilterExpanded,
    contentRef: filterContent,
    isMobileView,
    toggle: toggleFilter,
    getContentStyles,
    init: initTransition,
    cleanup: cleanupTransition
} = useTransition({
    defaultExpanded: true,  // PC端默认展开
    breakpoint: 992,        // 小于992px为移动设备
    animationDuration: 300  // 动画持续时间
})

const treeLoading = ref(false);

const { init: toast } = useToast()

// Loading state
const loading = ref(false);

// Search parameters
const queryParams = reactive({
    menuName: undefined,
    status: undefined
});

// Status options for dropdown
const statusOptions = [
    { label: "正常", value: "1" },
    { label: "停用", value: "0" }
];

// Menu list data
const menuList = ref([]);

// Menu options for parent selection
const menuOptions = ref([]);

// Dialog title
const title = ref("");

// Dialog visibility
const open = ref(false);

// Expand all rows
const isExpandAll = ref(false);

// Refresh table trigger
const refreshTable = ref(true);

// Form data
const formData = reactive({
    id: undefined,
    parentId: 0,
    name: undefined,
    icon: undefined,
    routeName: undefined,
    menuType: "M",
    orderNum: 0,
    isFrame: "1",
    isCache: "0",
    visible: "1",
    status: "1"
});

// Form validation rules
const rules = {
    name: [
        { required: true, message: "菜单名称不能为空" }
    ],
    orderNum: [
        { required: true, message: "菜单顺序不能为空" }
    ],
    path: [
        { required: true, message: "路由地址不能为空" }
    ]
};

// Table columns
const columns = [
    { key: "expand", label: "展开", width: 100 },
    { key: "name", label: "菜单名称", width: 160 },
    { key: "icon", label: "图标", width: 100, align: "center" },
    { key: "orderNum", label: "排序", width: 60 },
    { key: "perms", label: "权限标识" },
    { key: "component", label: "组件路径" },
    { key: "status", label: "状态", width: 80 },
    { key: "createTime", label: "创建时间" },
    { key: "actions", label: "操作", width: 200 }
];

// 子菜单表格列定义
const subMenuColumns = [
    // { key: "expand", label: "展开", width: 100 },
    { key: "name", label: "菜单名称", width: 160 },
    { key: "icon", label: "图标", width: 80, align: "center" },
    { key: "orderNum", label: "排序", width: 60 },
    { key: "perms", label: "权限标识" },
    { key: "component", label: "组件路径" },
    { key: "status", label: "状态", width: 80 },
    { key: "createTime", label: "创建时间" },
    { key: "actions", label: "操作", width: 180 }
];

// Icon selector modal
const showIconSelector = ref(false);

// Active tab for VaTabs
const activeTab = ref('basic');

// 图标选择器相关变量
const iconSearch = ref('');
const previewIcon = ref('');
const activeIconCategory = ref('common');

// 快速筛选标签
const quickFilterTags = [
    { label: '菜单', value: 'menu' },
    { label: '首页', value: 'home' },
    { label: '设置', value: 'settings' },
    { label: '用户', value: 'person' },
    { label: '添加', value: 'add' },
    { label: '删除', value: 'delete' },
    { label: '编辑', value: 'edit' },
    { label: '搜索', value: 'search' },
];

// 图标分类
const iconCategories = [
    { name: 'common', label: '常用图标' },
    { name: 'navigation', label: '导航' },
    { name: 'action', label: '操作' },
    { name: 'communication', label: '通信' },
    { name: 'content', label: '内容' },
    { name: 'editor', label: '编辑' },
    { name: 'file', label: '文件' },
    { name: 'device', label: '设备' },
];

// Material Icons 集合
const iconSets = {
    common: [
        'mso-home', 'mso-settings', 'mso-person', 'mso-people', 'mso-menu', 'mso-dashboard', 'mso-list',
        'mso-search', 'mso-notifications', 'mso-mail', 'mso-add', 'mso-edit', 'mso-delete',
        'mso-save', 'mso-refresh', 'mso-check', 'mso-close', 'mso-more_vert', 'mso-more_horiz',
        'mso-expand_more', 'mso-expand_less', 'mso-arrow_back', 'mso-arrow_forward', 'mso-arrow_upward',
        'mso-arrow_downward', 'mso-filter_list', 'mso-sort', 'mso-visibility', 'mso-visibility_off'
    ],
    navigation: [
        'mso-menu', 'mso-arrow_back', 'mso-arrow_forward', 'mso-arrow_upward', 'mso-arrow_downward',
        'mso-chevron_left', 'mso-chevron_right', 'mso-expand_less', 'mso-expand_more', 'mso-first_page',
        'mso-last_page', 'mso-fullscreen', 'mso-fullscreen_exit', 'mso-apps', 'mso-home'
    ],
    action: [
        'mso-add', 'mso-remove', 'mso-create', 'mso-delete', 'mso-edit', 'mso-refresh', 'mso-done',
        'mso-undo', 'mso-redo', 'mso-check', 'mso-close', 'mso-search', 'mso-settings', 'mso-info',
        'mso-help', 'mso-favorite', 'mso-favorite_border', 'mso-star', 'mso-star_border', 'mso-lock',
        'mso-lock_open', 'mso-visibility', 'mso-visibility_off', 'mso-filter_list'
    ],
    communication: [
        'mso-mail', 'mso-chat', 'mso-comment', 'mso-forum', 'mso-call', 'mso-contact_mail', 'mso-contact_phone',
        'mso-contacts', 'mso-message', 'mso-notifications', 'mso-notifications_active', 'mso-notifications_off',
        'mso-notifications_none', 'mso-notifications_paused', 'mso-phone'
    ],
    content: [
        'mso-add_circle', 'mso-add_circle_outline', 'mso-block', 'mso-clear', 'mso-content_copy',
        'mso-content_cut', 'mso-content_paste', 'mso-create', 'mso-drafts', 'mso-file_copy',
        'mso-filter_list', 'mso-flag', 'mso-font_download', 'mso-forward', 'mso-gesture'
    ],
    editor: [
        'mso-attach_file', 'mso-attach_money', 'mso-border_color', 'mso-format_bold', 'mso-format_italic',
        'mso-format_align_center', 'mso-format_align_justify', 'mso-format_align_left', 'mso-format_align_right',
        'mso-format_list_bulleted', 'mso-format_list_numbered', 'mso-insert_chart', 'mso-insert_photo'
    ],
    file: [
        'mso-folder', 'mso-folder_open', 'mso-folder_shared', 'mso-cloud', 'mso-cloud_download', 'mso-cloud_upload',
        'mso-cloud_queue', 'mso-create_new_folder', 'mso-file_download', 'mso-file_upload', 'mso-attachment'
    ],
    device: [
        'mso-devices', 'mso-desktop_mac', 'mso-desktop_windows', 'mso-laptop', 'mso-tablet', 'mso-phone_android', 'mso-phone_iphone', 'mso-keyboard', 'mso-mouse', 'mso-speaker', 'mso-tv', 'mso-watch', 'mso-videogame_asset'
    ]
};
// 根据搜索和分类过滤图标
const filteredIcons = computed(() => {
    let icons = iconSets[activeIconCategory.value] || [];

    if (iconSearch.value) {
        const searchTerm = iconSearch.value.toLowerCase();
        icons = icons.filter(icon => icon.toLowerCase().includes(searchTerm));
    }

    return icons;
});

// Select icon
const selectIcon = (icon) => {
    formData.icon = icon;
};

// 确认图标选择
const confirmIconSelection = () => {
    showIconSelector.value = false;
};

// Form reference
const formRef = ref(null);
const { validate } = useForm('formRef');

// Format timestamp
const formatTime = (time) => {
    if (!time) return "";
    const date = new Date(time);
    return date.toLocaleString();
};

// Get menu list
const getList = () => {
    loading.value = true;
    listMenu(queryParams).then(response => {
        menuList.value = handleTree(response.data, "id", "parentId", "children");
        loading.value = false;
    });
};

// Get menu options for dropdown
const getTreeselect = async () => {
    treeLoading.value = true;
    try {
        let res = await listMenu()
        if (res.code == 200) {
            menuOptions.value = [];
            // 添加顶级菜单选项
            let rootMenu = {};
            // 转换API返回的数据为树形结构
            const treeData = handleTree(res.data, "id");
            // 将树形数据转换为选项格式
            rootMenu = treeData.map(item => {
                return normalizeMenuOption(item);
            });
            rootMenu.forEach(element => {
                menuOptions.value.push(element);
            });
            menuOptions.value.unshift({ id: 0, name: "主类目", label: "主类目", children: [] })
            treeLoading.value = false;
        }
    } catch (error) {
        treeLoading.value = false;
    }

};

// Normalize menu option for dropdown
const normalizeMenuOption = (node) => {
    const result = {
        id: node.id,
        label: node.name
    };

    if (node.children && node.children.length) {
        result.children = node.children.map(child => normalizeMenuOption(child));
    }

    return result;
};

// Search button handler
const handleQuery = () => {
    getList();
};

// Reset search form
const resetQuery = () => {
    queryParams.menuName = undefined;
    queryParams.status = undefined;
    handleQuery();
};

// Add menu button handler
const handleAdd = async (row) => {
    reset();
    await getTreeselect();
    if (row) {
        // 获取正确的ID，无论是直接行对象还是row.rowData
        const id = row.id || (row.rowData && row.rowData.id);
        if (id) {
            formData.parentId = id;
        }
    } else {
        formData.parentId = 0;
    }
    open.value = true;
    title.value = "添加菜单";
};

// Toggle expand all
const toggleExpandAll = () => {
    refreshTable.value = false;
    isExpandAll.value = !isExpandAll.value;
    nextTick(() => {
        refreshTable.value = true;
    });
};

const handleUpdate = async (row) => {
    reset();
    await getTreeselect();
    Object.assign(formData, row.rowData);
    // 等待下一个tick后更新父菜单名称
    nextTick(() => {
        updateParentMenuName(formData.parentId);
    });
    open.value = true;
    title.value = "修改菜单";
};

// Delete menu button handler
const handleDelete = (row) => {
    // 获取正确的ID和名称，无论是直接行对象还是row.rowData
    const id = row.id || (row.rowData && row.rowData.id);
    const name = row.name || (row.rowData && row.rowData.name);

    if (confirm(`确认删除名称为"${name}"的数据项？`)) {
        delMenu(id).then(() => {
            getList();
            toast({
                color: 'success',
                message: '删除成功',
            });
        });
    }
};

// Form submit handler
const submitForm = () => {
    console.log(formData)
    if (validate()) {
        if (formData.id) {
            updateMenu(formData).then(response => {
                toast({
                    color: 'success',
                    message: '修改成功',
                });
                open.value = false;
                getList();
            });
        } else {
            addMenu(formData).then(response => {
                toast({
                    color: 'success',
                    message: '新增成功',
                });
                open.value = false;
                getList();
            });
        }
    }
};

// Cancel form
const cancel = () => {
    open.value = false;
    reset();
};

// Reset form
const reset = () => {
    parentMenuName.value = "主类目"; // 重置父菜单名称
    Object.assign(formData, {
        id: undefined,
        parentId: 0,
        name: undefined,
        icon: undefined,
        menuType: "M",
        orderNum: 0,
        isFrame: "1",
        isCache: "0",
        visible: "0",
        status: "0"
    });
};

// Update parent menu name
const parentMenuName = ref("");
const showParentMenu = ref(false);

const updateParentMenuName = (value) => {
    // 查找选中的菜单名称
    const findMenuName = (options, id) => {
        for (const option of options) {
            if (option.id == id) {
                return option.name || option.label;
            }
            if (option.children && option.children.length > 0) {
                const found = findMenuName(option.children, id);
                if (found) return found;
            }
        }
        return id === 0 ? "主类目" : "";
    };

    // 更新菜单名称
    parentMenuName.value = findMenuName(menuOptions.value, value);
    showParentMenu.value = false;
};

// 在菜单输入框点击时显示树选择器
const toggleParentMenu = () => {
    showParentMenu.value = !showParentMenu.value;
};

// 监听父级ID变化，更新父级菜单名称
watch(() => formData.parentId, (newVal) => {
    updateParentMenuName(newVal);
}, { immediate: true });

// Initialize data
onMounted(() => {
    getList();
    initTransition(); // 初始化过渡动画相关逻辑
});

// 组件卸载时清理事件监听
onUnmounted(() => {
    cleanupTransition(); // 清理过渡动画相关事件监听
});
</script>

<style scoped>
.primary-label {
    color: var(--va-primary);
    font-size: 12px;
    font-weight: 600;
}

.filter-label {
    color: var(--va-primary);
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
}

/* 筛选表单布局 */
.filter-form {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

/* 筛选网格布局 */
.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 12px;
    margin-top: 10px;
}

.filter-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-bottom: 8px;
}

.filter-item-header {
    display: flex;
    align-items: center;
}

.filter-input {
    width: 100%;
}

/* 操作按钮区域 */
.filter-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 10px;
    margin: 8px 0;
}

/* 表单项样式 */
.form-item {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 1rem;
    padding: 0 5px;
    margin-top: 10px;
}

.submenu-container {
    background-color: #f9fafb;
    border-radius: 4px;
    padding: 0.5rem;
}

.nested-table {
    margin-top: 0.5rem;
    border: 1px solid #eaecef;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.nested-table :deep(.va-data-table__head) {
    background-color: #f8fafc;
}

/* 图标选择器样式 */
.icon-grid-container {
    border: 1px solid var(--va-background-border);
    border-radius: 4px;
    padding: 8px;
}

.icon-item {
    transition: all 0.2s;
}

.icon-item:hover {
    background-color: var(--va-background-element);
    transform: scale(1.05);
}

.icon-selected {
    border-color: var(--va-primary);
    background-color: var(--va-primary-opacity-2);
}

.icon-name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
}

/* 响应式样式 */
@media (max-width: 991px) {
    .filter-toggle {
        display: flex;
    }

    .filter-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 2fr));
    }

    .form-item.w-1\/2 {
        width: 100%;
    }

    .icon-grid-container {
        max-height: 300px;
    }
}

@media (min-width: 992px) {
    .filter-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }
}
</style>
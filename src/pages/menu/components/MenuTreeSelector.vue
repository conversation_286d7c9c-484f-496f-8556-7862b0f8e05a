<template>
    <div class="menu-tree-selector">
        <VaTreeView v-model:checked="selectedNode" :nodes="treeNodes" selectable :expand-all="expanded"
            class="menu-tree" @update:selected="selectNode" :selectionType="selectionType" />
    </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits, onMounted } from 'vue';

const props = defineProps({
    menuOptions: {
        type: Array,
        default: () => [],
        required: true
    },
    modelValue: {
        type: [Number, String, Array],
        default: 0
    },
    selectionType: {
        type: String,
        default: 'leaf'
    },
    expanded: {
        type: Boolean,
        default: true
    },
});

const emit = defineEmits(['update:modelValue']);

// 转换为树形结构数据
const treeNodes = ref([]);
const selectedNode = ref([]);

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
    if (newVal !== undefined) {
        if (Array.isArray(newVal)) {
            // 如果是数组（多选模式）
            selectedNode.value = newVal.map(id => String(id));
        } else {
            // 如果是单个值
            selectedNode.value = [String(newVal)];
        }

    }
}, { immediate: true });

// 监听选中节点变化
watch(selectedNode, (newVal) => {

    if (!newVal || newVal.length === 0) {
        emit('update:modelValue', props.selectionType == 'leaf' ? [] : 0);
        return;
    }

    // 转化为数组格式
    const selectedIds = Array.isArray(newVal) ? newVal : Object.values(newVal);

    if (props.selectionType != 'leaf') {
        // 单选模式，返回单个ID
        emit('update:modelValue', selectedIds.length > 0 ? parseInt(selectedIds[0]) : 0);
    } else {
        // 多选模式，返回ID数组
        emit('update:modelValue', selectedIds);
    }
});

// 点击节点直接选中
const selectNode = (node) => {


    if (props.selectionType != 'leaf') {
        // 单选模式
        selectedNode.value = [node.id];
        emit('update:modelValue', parseInt(node.id));
    } else {
        // 多选模式 - 在已选中的基础上添加或移除选中的节点
        const nodeId = node.id;
        const index = selectedNode.value.indexOf(nodeId);

        if (index === -1) {
            // 如果不存在，则添加
            selectedNode.value = [...selectedNode.value, nodeId];
        } else {
            // 如果已存在，则移除
            selectedNode.value = selectedNode.value.filter(id => id !== nodeId);
        }
    }
};

// 转换菜单数据为树形结构
watch(() => props.menuOptions, (options) => {
    if (options && options.length > 0) {
        treeNodes.value = transformMenuOptions(options);
    }
}, { immediate: true });

// 将菜单选项转换为树视图格式
function transformMenuOptions(options) {
    const transform = (items) => {
        return items.map(item => {
            const node = {
                id: String(item.id), // TreeView组件要求id为字符串
                label: item.label || item.name,
            };

            if (item.children && item.children.length > 0) {
                node.children = transform(item.children);
            }

            return node;
        });
    };

    return transform(options);
}
</script>

<style scoped>
.menu-tree-selector {
    width: 100%;
}

.menu-tree {
    width: 100%;
}

:deep(.va-tree-node) {
    margin-bottom: 4px;
}

:deep(.va-tree-node__label) {
    padding: 6px 8px;
    border-radius: 4px;
    transition: all 0.2s;
}

:deep(.va-tree-node__label:hover) {
    background-color: #f0f2f5;
}

:deep(.va-tree-node--selected > .va-tree-node__label) {
    background-color: rgba(var(--va-primary), 0.1);
    color: rgb(var(--va-primary));
}
</style>
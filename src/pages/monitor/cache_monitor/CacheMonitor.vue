<template>
    <div class="cache-monitor">
        <!-- 加载状态 -->
        <div v-if="isLoading && !cacheInfo" class="loading-container">
            <VaProgressCircle indeterminate color="primary" size="large" />
            <p class="loading-text">正在加载缓存监控数据...</p>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
            <VaIcon name="mso-error" color="danger" size="large" />
            <p class="error-text">{{ error.message || '加载数据失败' }}</p>
            <VaButton preset="secondary" @click="refreshData">重试</VaButton>
        </div>

        <!-- 主要内容 -->
        <div v-else-if="cacheInfo" class="content-grid">
            <!-- 基本信息卡片 -->
            <VaCard class="info-card">
                <VaCardTitle class="card-title">
                    <div class="flex items-center gap-2">
                        <VaIcon name="mso-info" color="primary" />
                        <span>基本信息</span>
                    </div>
                </VaCardTitle>
                <VaCardContent>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">Redis 版本</div>
                            <div class="info-value">{{ cacheInfo.info.redis_version }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">运行模式</div>
                            <div class="info-value">{{ cacheInfo.info.redis_mode }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">运行时间</div>
                            <div class="info-value">{{ formatUptime(cacheInfo.info.uptime_in_seconds) }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">连接客户端</div>
                            <div class="info-value">{{ cacheInfo.info.connected_clients }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">数据库大小</div>
                            <div class="info-value">{{ cacheInfo.dbSize }} keys</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">命中率</div>
                            <div class="info-value">{{ calculateHitRate() }}%</div>
                        </div>
                    </div>
                </VaCardContent>
            </VaCard>

            <!-- 内存使用情况 -->
            <VaCard class="memory-card">
                <VaCardTitle class="card-title">
                    <div class="flex items-center gap-2">
                        <VaIcon name="mso-memory" color="success" />
                        <span>内存使用情况</span>
                    </div>
                </VaCardTitle>
                <VaCardContent>
                    <div class="memory-stats">
                        <div class="memory-item">
                            <div class="memory-label">已使用内存</div>
                            <div class="memory-value primary">{{ cacheInfo.info.used_memory_human }}</div>
                        </div>
                        <div class="memory-item">
                            <div class="memory-label">峰值内存</div>
                            <div class="memory-value warning">{{ cacheInfo.info.used_memory_peak_human }}</div>
                        </div>
                        <div class="memory-item">
                            <div class="memory-label">系统总内存</div>
                            <div class="memory-value info">{{ cacheInfo.info.total_system_memory_human }}</div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <VaChart v-if="memoryChartData" :data="memoryChartData" :options="memoryChartOptions"
                            type="doughnut" class="memory-chart" />
                    </div>
                </VaCardContent>
            </VaCard>

            <!-- 命令统计 -->
            <VaCard class="commands-card">
                <VaCardTitle class="card-title">
                    <div class="flex items-center gap-2">
                        <VaIcon name="mso-bar_chart" color="warning" />
                        <span>命令统计 (Top 10)</span>
                    </div>
                </VaCardTitle>
                <VaCardContent>
                    <div class="chart-container">
                        <VaChart v-if="commandsChartData" :data="commandsChartData" :options="commandsChartOptions"
                            type="bar" class="commands-chart" />
                    </div>
                </VaCardContent>
            </VaCard>

            <!-- 性能指标 -->
            <VaCard class="performance-card">
                <VaCardTitle class="card-title">
                    <div class="flex items-center gap-2">
                        <VaIcon name="mso-speed" color="danger" />
                        <span>性能指标</span>
                    </div>
                </VaCardTitle>
                <VaCardContent>
                    <div class="performance-grid">
                        <div class="performance-item">
                            <div class="performance-icon">
                                <VaIcon name="mso-flash_on" color="success" />
                            </div>
                            <div class="performance-content">
                                <div class="performance-label">每秒操作数</div>
                                <div class="performance-value">{{ cacheInfo.info.instantaneous_ops_per_sec }}</div>
                            </div>
                        </div>
                        <div class="performance-item">
                            <div class="performance-icon">
                                <VaIcon name="mso-input" color="primary" />
                            </div>
                            <div class="performance-content">
                                <div class="performance-label">输入速率</div>
                                <div class="performance-value">{{ cacheInfo.info.instantaneous_input_kbps }} KB/s</div>
                            </div>
                        </div>
                        <div class="performance-item">
                            <div class="performance-icon">
                                <VaIcon name="mso-output" color="warning" />
                            </div>
                            <div class="performance-content">
                                <div class="performance-label">输出速率</div>
                                <div class="performance-value">{{ cacheInfo.info.instantaneous_output_kbps }} KB/s</div>
                            </div>
                        </div>
                        <div class="performance-item">
                            <div class="performance-icon">
                                <VaIcon name="mso-access_time" color="info" />
                            </div>
                            <div class="performance-content">
                                <div class="performance-label">事件循环延迟</div>
                                <div class="performance-value">{{ cacheInfo.info.instantaneous_eventloop_duration_usec
                                    }} μs</div>
                            </div>
                        </div>
                    </div>
                </VaCardContent>
            </VaCard>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { getCacheInfo } from '../../../api/monitor/cache'
import VaChart from '../../../components/va-charts/VaChart.vue'
import { useToast } from 'vuestic-ui'

// 注册 Chart.js 组件
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    ArcElement,
    Title,
    Tooltip,
    Legend
} from 'chart.js'

ChartJS.register(
    CategoryScale,
    LinearScale,
    BarElement,
    ArcElement,
    Title,
    Tooltip,
    Legend
)

const { init: notify } = useToast()

// 响应式数据
const cacheInfo = ref(null)
const isLoading = ref(false)
const error = ref(null)

// 获取缓存信息
const fetchCacheInfo = async () => {
    isLoading.value = true
    error.value = null

    try {
        const response = await getCacheInfo()
        if (response && response.data) {
            cacheInfo.value = response.data
        }
    } catch (err) {
        error.value = err instanceof Error ? err : new Error('获取缓存信息失败')
        notify({
            message: '获取缓存信息失败',
            color: 'danger',
            duration: 3000,
        })
    } finally {
        isLoading.value = false
    }
}

// 刷新数据
const refreshData = () => {
    fetchCacheInfo()
}

// 格式化运行时间
const formatUptime = (seconds) => {
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)

    if (days > 0) {
        return `${days}天 ${hours}小时 ${minutes}分钟`
    } else if (hours > 0) {
        return `${hours}小时 ${minutes}分钟`
    } else {
        return `${minutes}分钟`
    }
}

// 计算命中率
const calculateHitRate = () => {
    if (!cacheInfo.value) return '0.00'

    const hits = parseInt(cacheInfo.value.info.keyspace_hits || 0)
    const misses = parseInt(cacheInfo.value.info.keyspace_misses || 0)
    const total = hits + misses

    if (total === 0) return '0.00'

    return ((hits / total) * 100).toFixed(2)
}

// 内存图表数据
const memoryChartData = computed(() => {
    if (!cacheInfo.value) return null

    const usedMemory = parseInt(cacheInfo.value.info.used_memory || 0)
    const totalMemory = parseInt(cacheInfo.value.info.total_system_memory || 0)
    const freeMemory = totalMemory - usedMemory

    return {
        labels: ['已使用内存', '可用内存'],
        datasets: [{
            data: [usedMemory, freeMemory],
            backgroundColor: [
                '#667eea',
                '#e8f4fd'
            ],
            borderColor: [
                '#667eea',
                '#e8f4fd'
            ],
            borderWidth: 2
        }]
    }
})

// 内存图表配置
const memoryChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'bottom',
            labels: {
                padding: 20,
                usePointStyle: true
            }
        },
        tooltip: {
            callbacks: {
                label: function (context) {
                    const label = context.label || ''
                    const value = context.parsed
                    const total = context.dataset.data.reduce((a, b) => a + b, 0)
                    const percentage = ((value / total) * 100).toFixed(1)
                    return `${label}: ${formatBytes(value)} (${percentage}%)`
                }
            }
        }
    }
}

// 命令统计图表数据
const commandsChartData = computed(() => {
    if (!cacheInfo.value || !cacheInfo.value.commandStats) return null

    // 获取前10个命令
    const sortedCommands = cacheInfo.value.commandStats
        .sort((a, b) => parseInt(b.value) - parseInt(a.value))
        .slice(0, 10)

    return {
        labels: sortedCommands.map(cmd => cmd.name),
        datasets: [{
            label: '执行次数',
            data: sortedCommands.map(cmd => parseInt(cmd.value)),
            backgroundColor: [
                '#667eea', '#764ba2', '#f093fb', '#f5576c',
                '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
                '#ffecd2', '#fcb69f'
            ],
            borderColor: [
                '#667eea', '#764ba2', '#f093fb', '#f5576c',
                '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
                '#ffecd2', '#fcb69f'
            ],
            borderWidth: 1
        }]
    }
})

// 命令统计图表配置
const commandsChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            display: false
        },
        tooltip: {
            callbacks: {
                label: function (context) {
                    return `${context.label}: ${context.parsed.y.toLocaleString()} 次`
                }
            }
        }
    },
    scales: {
        y: {
            beginAtZero: true,
            ticks: {
                callback: function (value) {
                    return value.toLocaleString()
                }
            }
        },
        x: {
            ticks: {
                maxRotation: 45
            }
        }
    }
}

// 格式化字节数
const formatBytes = (bytes) => {
    if (bytes === 0) return '0 B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 组件挂载时获取数据
onMounted(() => {
    fetchCacheInfo()
})
</script>

<style scoped>
/* 页面整体样式 */
.cache-monitor {
    padding: 0;
}

.page-header {
    margin-bottom: 2rem;
}

.header-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}



.page-subtitle {
    font-size: 0.875rem;
    color: var(--va-text-secondary);
    margin: 0.25rem 0 0 0;
}

.refresh-btn {
    min-width: 120px;
}

/* 加载和错误状态 */
.loading-container,
.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
}

.loading-text,
.error-text {
    margin-top: 1rem;
    font-size: 1rem;
    color: var(--va-text-secondary);
}

/* 内容网格 */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

/* 卡片样式 */
.card-title {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
    border-bottom: 1px solid var(--va-background-border);
    font-weight: 600;
}

/* 基本信息卡片 */
.info-card {
    grid-column: span 2;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
}

.info-item {
    text-align: center;
    padding: 1rem;
    background: var(--va-background-secondary);
    border-radius: 8px;
    transition: transform 0.2s ease;
}

.info-item:hover {
    transform: translateY(-2px);
}

.info-label {
    font-size: 0.875rem;
    color: var(--va-text-secondary);
    margin-bottom: 0.5rem;
}

.info-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--va-text-primary);
}

/* 内存卡片 */
.memory-stats {
    display: flex;
    justify-content: space-around;
    padding: 1rem 1.5rem;
    background: var(--va-background-secondary);
    margin: 1.5rem;
    border-radius: 8px;
}

.memory-item {
    text-align: center;
}

.memory-label {
    font-size: 0.875rem;
    color: var(--va-text-secondary);
    margin-bottom: 0.5rem;
}

.memory-value {
    font-size: 1.125rem;
    font-weight: 600;
}

.memory-value.primary {
    color: var(--va-primary);
}

.memory-value.warning {
    color: var(--va-warning);
}

.memory-value.info {
    color: var(--va-info);
}

.chart-container {
    padding: 1.5rem;
    height: 300px;
}

.memory-chart,
.commands-chart {
    height: 100%;
}

/* 性能指标卡片 */
.performance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    padding: 1.5rem;
}

.performance-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--va-background-secondary);
    border-radius: 8px;
    transition: transform 0.2s ease;
}

.performance-item:hover {
    transform: translateY(-2px);
}

.performance-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--va-background-element);
}

.performance-content {
    flex: 1;
}

.performance-label {
    font-size: 0.875rem;
    color: var(--va-text-secondary);
    margin-bottom: 0.25rem;
}

.performance-value {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--va-text-primary);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .content-grid {
        grid-template-columns: 1fr;
    }

    .info-card {
        grid-column: span 1;
    }

    .info-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }

    .memory-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .performance-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 640px) {
    .page-header .flex {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .chart-container {
        height: 250px;
    }
}
</style>
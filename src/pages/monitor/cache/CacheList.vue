<template>
    <div class="cache-management">


        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 缓存名称列表 -->
            <VaCard class="cache-names-card">
                <VaCardTitle class="card-title">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center gap-2">
                            <VaIcon name="mso-folder" color="primary" />
                            <span>缓存名称</span>
                            <VaBadge v-if="cacheNamesObject.length > 0" :text="cacheNamesObject.length"
                                color="primary" />
                        </div>
                        <div class="flex gap-2">
                            <VaButton size="small" preset="secondary" icon="mso-refresh" @click="fetchCacheNames"
                                :loading="isLoading" class="refresh-btn">
                                刷新
                            </VaButton>
                            <VaButton size="small" preset="secondary" color="danger" icon="mso-delete_sweep"
                                @click="handleDeleteAllCache" :loading="isLoading" class="delete-all-btn">
                                清空所有
                            </VaButton>
                        </div>
                    </div>
                </VaCardTitle>
                <VaCardContent class="card-content">
                    <div v-if="isLoading" class="loading-state">
                        <VaProgressCircle indeterminate color="primary" />
                        <p class="loading-text">正在加载缓存名称...</p>
                    </div>
                    <div v-else-if="error" class="error-state">
                        <VaIcon name="mso-error" color="danger" size="large" />
                        <p class="error-text">{{ error.message }}</p>
                        <VaButton size="small" @click="fetchCacheNames" class="mt-2">重试</VaButton>
                    </div>
                    <div v-else-if="cacheNamesObject.length === 0" class="empty-state">
                        <VaIcon name="mso-inbox" color="secondary" size="large" />
                        <p class="empty-text">暂无缓存名称</p>
                    </div>
                    <div v-else class="cache-list">
                        <div v-for="(item, index) in cacheNamesObject" :key="item.cacheName" class="cache-item"
                            :class="{ 'cache-item-selected': selectedCacheName === item.cacheName }"
                            @click="handleCacheNameClick(item.cacheName)"
                            :style="{ animationDelay: `${index * 50}ms` }">
                            <div class="cache-item-content">
                                <div class="cache-item-icon">
                                    <VaIcon name="mso-database"
                                        :color="selectedCacheName === item.cacheName ? 'primary' : 'secondary'" />
                                </div>
                                <div class="cache-item-info">
                                    <span class="cache-item-name">{{ item.cacheName }}</span>
                                    <span>{{ item.remark }}</span>
                                </div>
                                <div class="cache-item-action">
                                    <VaButton v-if="selectedCacheName !== item.cacheName" size="small" preset="plain"
                                        color="danger" icon="mso-delete"
                                        @click.stop="handleDeleteCacheName(item.cacheName)" class="delete-btn">
                                    </VaButton>
                                    <VaIcon v-if="selectedCacheName === item.cacheName" name="mso-check_circle"
                                        color="primary" class="selected-icon" />
                                    <VaIcon v-else name="mso-chevron_right" color="secondary" size="small"
                                        class="arrow-icon" />
                                </div>
                            </div>
                        </div>
                    </div>
                </VaCardContent>
            </VaCard>

            <!-- 缓存键名列表 -->
            <VaCard class="cache-keys-card">
                <VaCardTitle class="card-title">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center gap-2">
                            <VaIcon name="mso-vpn_key" color="success" />
                            <span>缓存键名</span>
                            <VaBadge v-if="cacheKeys.length > 0" :text="cacheKeys.length" color="success" />
                        </div>
                        <VaButton v-if="selectedCacheName" size="small" preset="secondary" icon="mso-refresh"
                            @click="fetchCacheKeys(selectedCacheName)" :loading="isLoadingKeys" class="refresh-btn">
                            刷新
                        </VaButton>
                    </div>
                </VaCardTitle>
                <VaCardContent class="card-content">
                    <div v-if="!selectedCacheName" class="guide-state">
                        <VaIcon name="mso-touch_app" color="info" size="large" />
                        <p class="guide-text">请先选择缓存名称</p>
                        <p class="guide-subtext">从左侧列表中选择一个缓存名称</p>
                    </div>
                    <div v-else-if="isLoadingKeys" class="loading-state">
                        <VaProgressCircle indeterminate color="success" />
                        <p class="loading-text">正在加载缓存键名...</p>
                    </div>
                    <div v-else-if="cacheKeys.length === 0" class="empty-state">
                        <VaIcon name="mso-key_off" color="secondary" size="large" />
                        <p class="empty-text">该缓存名称下暂无键名</p>
                    </div>
                    <div v-else class="keys-container">
                        <div class="keys-header">
                            <span class="keys-info">共 {{ cacheKeys.length }} 个键名</span>
                        </div>
                        <div class="keys-list">
                            <div v-for="(cacheKey, index) in cacheKeys" :key="cacheKey" class="key-item"
                                :class="{ 'key-item-selected': selectedCacheKey === cacheKey }"
                                @click="handleCacheKeyClick(cacheKey)" :style="{ animationDelay: `${index * 30}ms` }">
                                <div class="key-item-content">
                                    <div class="key-item-icon">
                                        <VaIcon name="mso-key"
                                            :color="selectedCacheKey === cacheKey ? 'success' : 'secondary'"
                                            size="small" />
                                    </div>
                                    <div class="key-item-info">
                                        <span class="key-item-name">{{ cacheKey }}</span>
                                    </div>
                                    <div class="key-item-action">
                                        <VaButton v-if="selectedCacheKey !== cacheKey" size="small" preset="plain"
                                            color="danger" icon="mso-delete"
                                            @click.stop="handleDeleteCacheKey(cacheKey)" class="delete-btn">
                                        </VaButton>
                                        <VaIcon v-if="selectedCacheKey === cacheKey" name="mso-check_circle"
                                            color="success" class="selected-icon" />
                                        <VaIcon v-else name="mso-chevron_right" color="secondary" size="small"
                                            class="arrow-icon" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </VaCardContent>
            </VaCard>

            <!-- 缓存内容显示 -->
            <VaCard class="cache-content-card">
                <VaCardTitle class="card-title">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center gap-2">
                            <VaIcon name="mso-code" color="warning" />
                            <span>缓存内容</span>
                        </div>
                        <div class="flex gap-2">
                            <VaButton v-if="cacheValue" size="small" preset="secondary" icon="mso-content_copy"
                                @click="copyToClipboard(cacheValue.cacheValue)" class="copy-btn">
                                复制
                            </VaButton>
                            <VaButton v-if="selectedCacheName && selectedCacheKey" size="small" preset="secondary"
                                icon="mso-refresh" @click="fetchCacheValue(selectedCacheName, selectedCacheKey)"
                                :loading="isLoadingValue" class="refresh-btn">
                                刷新
                            </VaButton>
                        </div>
                    </div>
                </VaCardTitle>
                <VaCardContent class="card-content">
                    <div v-if="!selectedCacheName || !selectedCacheKey" class="guide-state">
                        <VaIcon name="mso-preview" color="info" size="large" />
                        <p class="guide-text">请先选择缓存名称和键名</p>
                        <p class="guide-subtext">选择完整路径后查看缓存内容</p>
                    </div>
                    <div v-else-if="isLoadingValue" class="loading-state">
                        <VaProgressCircle indeterminate color="warning" />
                        <p class="loading-text">正在加载缓存内容...</p>
                    </div>
                    <div v-else-if="!cacheValue" class="empty-state">
                        <VaIcon name="mso-data_object" color="secondary" size="large" />
                        <p class="empty-text">暂无缓存内容</p>
                    </div>
                    <div v-else class="content-container">
                        <!-- 缓存基本信息 -->
                        <div class="cache-info-panel">
                            <div class="info-header">
                                <VaIcon name="mso-info" color="primary" size="small" />
                                <span class="info-title">缓存信息</span>
                            </div>
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="info-label">缓存名称:</span>
                                    <VaChip size="small" color="primary">{{ cacheValue.cacheName }}</VaChip>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">缓存键名:</span>
                                    <VaChip size="small" color="success">{{ cacheValue.cacheKey }}</VaChip>
                                </div>
                            </div>
                        </div>

                        <!-- 缓存内容 -->
                        <div class="content-panel">
                            <div class="content-header">
                                <div class="flex items-center gap-2">
                                    <VaIcon name="mso-data_object" color="warning" size="small" />
                                    <span class="content-title">缓存值</span>
                                </div>
                                <div class="content-stats">
                                    <VaChip size="small" color="info">
                                        {{ getContentSize(cacheValue.cacheValue) }}
                                    </VaChip>
                                </div>
                            </div>
                            <div class="code-container">
                                <div class="code-header">
                                    <div class="code-dots">
                                        <span class="dot dot-red"></span>
                                        <span class="dot dot-yellow"></span>
                                        <span class="dot dot-green"></span>
                                    </div>
                                    <span class="code-lang">JSON</span>
                                </div>
                                <div class="code-content">
                                    <pre class="code-pre">{{ formatCacheValue(cacheValue.cacheValue) }}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </VaCardContent>
            </VaCard>
        </div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useCache } from './useCache'
import { useToast } from 'vuestic-ui'

const {
    cacheNamesObject,
    cacheKeys,
    cacheValue,
    isLoading,
    isLoadingKeys,
    isLoadingValue,
    error,
    selectedCacheName,
    selectedCacheKey,
    fetchCacheNames,
    fetchCacheKeys,
    fetchCacheValue,
    deleteCacheName,
    deleteCacheKey,
    deleteAllCache,
    resetCache
} = useCache()

const { init: notify } = useToast()

// 处理缓存名称点击
const handleCacheNameClick = (cacheName) => {
    if (selectedCacheName.value === cacheName) {
        // 如果点击的是已选中的缓存名称，则取消选择
        resetCache()
    } else {
        // 选择新的缓存名称并获取对应的键名列表
        fetchCacheKeys(cacheName)
    }
}

// 处理缓存键名点击
const handleCacheKeyClick = (cacheKey) => {
    if (selectedCacheKey.value === cacheKey) {
        // 如果点击的是已选中的键名，则取消选择
        selectedCacheKey.value = ''
        cacheValue.value = null
    } else {
        // 选择新的键名并获取对应的缓存内容
        fetchCacheValue(selectedCacheName.value, cacheKey)
    }
}

// 格式化缓存值显示
const formatCacheValue = (value) => {
    if (!value) return ''

    try {
        // 尝试解析JSON并格式化
        const parsed = JSON.parse(value)
        return JSON.stringify(parsed, null, 2)
    } catch {
        // 如果不是JSON，直接返回原值
        return value
    }
}

// 复制到剪贴板
const copyToClipboard = async (text) => {
    try {
        await navigator.clipboard.writeText(text)
        notify({
            message: '已复制到剪贴板',
            color: 'success',
            duration: 2000,
        })
    } catch (err) {
        notify({
            message: '复制失败',
            color: 'danger',
            duration: 2000,
        })
    }
}

// 获取内容大小
const getContentSize = (content) => {
    if (!content) return '0 B'

    const bytes = new Blob([content]).size
    const sizes = ['B', 'KB', 'MB', 'GB']

    if (bytes === 0) return '0 B'

    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    const size = (bytes / Math.pow(1024, i)).toFixed(1)

    return `${size} ${sizes[i]}`
}

// 删除缓存名称处理
const handleDeleteCacheName = async (cacheName) => {
    try {
        // 显示确认对话框
        const confirmed = confirm(`确定要删除缓存名称 "${cacheName}" 吗？\n\n此操作将删除该缓存名称下的所有数据，且不可恢复。`)

        if (!confirmed) return

        const success = await deleteCacheName(cacheName)
        if (success) {
            notify({
                message: `缓存名称 "${cacheName}" 删除成功`,
                color: 'success',
                duration: 3000,
            })
        } else {
            notify({
                message: `删除缓存名称 "${cacheName}" 失败`,
                color: 'danger',
                duration: 3000,
            })
        }
    } catch (err) {
        notify({
            message: `删除缓存名称失败: ${err.message}`,
            color: 'danger',
            duration: 3000,
        })
    }
}

// 删除缓存键处理
const handleDeleteCacheKey = async (cacheKey) => {
    try {
        // 显示确认对话框
        const confirmed = confirm(`确定要删除缓存键 "${cacheKey}" 吗？\n\n此操作不可恢复。`)

        if (!confirmed) return

        const success = await deleteCacheKey(cacheKey)
        if (success) {
            notify({
                message: `缓存键 "${cacheKey}" 删除成功`,
                color: 'success',
                duration: 3000,
            })
        } else {
            notify({
                message: `删除缓存键 "${cacheKey}" 失败`,
                color: 'danger',
                duration: 3000,
            })
        }
    } catch (err) {
        notify({
            message: `删除缓存键失败: ${err.message}`,
            color: 'danger',
            duration: 3000,
        })
    }
}

// 删除所有缓存处理
const handleDeleteAllCache = async () => {
    try {
        // 显示确认对话框
        const confirmed = confirm('确定要清空所有缓存吗？\n\n此操作将删除Redis中的所有缓存数据，且不可恢复！')

        if (!confirmed) return

        const success = await deleteAllCache()
        if (success) {
            notify({
                message: '所有缓存清空成功',
                color: 'success',
                duration: 3000,
            })
        } else {
            notify({
                message: '清空所有缓存失败',
                color: 'danger',
                duration: 3000,
            })
        }
    } catch (err) {
        notify({
            message: `清空所有缓存失败: ${err.message}`,
            color: 'danger',
            duration: 3000,
        })
    }
}

// 初始化时获取缓存名称列表
onMounted(() => {
    fetchCacheNames()
})
</script>

<style scoped>
/* 页面整体样式 */
.cache-management {
    padding: 0;
}

.page-header {
    margin-bottom: 2rem;
}

.header-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}



.page-subtitle {
    color: #718096;
    font-size: 0.95rem;
    margin: 0.25rem 0 0 0;
}

/* 卡片样式 */
.cache-names-card,
.cache-keys-card,
.cache-content-card {
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    height: fit-content;
}

.cache-names-card:hover,
.cache-keys-card:hover,
.cache-content-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.card-title {
    padding: 1.5rem 1.5rem 0 1.5rem;
    font-weight: 600;
    color: #2d3748;
}

.card-content {
    padding: 1rem 1.5rem 1.5rem 1.5rem;
}

/* 状态样式 */
.loading-state,
.error-state,
.empty-state,
.guide-state {
    text-align: center;
    padding: 3rem 1rem;
}

.loading-text,
.error-text,
.empty-text,
.guide-text {
    margin-top: 1rem;
    font-weight: 500;
    color: #4a5568;
}

.guide-subtext {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #718096;
}

/* 缓存列表样式 */
.cache-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.cache-item {
    border-radius: 12px;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
    cursor: pointer;
    animation: fadeInUp 0.5s ease forwards;
    opacity: 0;
    transform: translateY(20px);
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.cache-item:hover {
    border-color: #cbd5e0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.cache-item-selected {
    border-color: #3182ce;
    background: linear-gradient(135deg, #ebf8ff 0%, #bee3f8 100%);
    box-shadow: 0 4px 16px rgba(49, 130, 206, 0.2);
}

.cache-item-content {
    display: flex;
    align-items: center;
    padding: 1rem;
    gap: 0.75rem;
}

.cache-item-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f7fafc;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.cache-item-selected .cache-item-icon {
    background: #3182ce;
    color: white;
}

.cache-item-info {
    flex: 1;
    min-width: 0;
}

.cache-item-name {
    font-weight: 600;
    color: #2d3748;
    display: block;
    word-break: break-all;
}

.cache-item-action {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.selected-icon {
    animation: checkmark 0.3s ease;
}

@keyframes checkmark {
    0% {
        transform: scale(0);
    }

    50% {
        transform: scale(1.2);
    }

    100% {
        transform: scale(1);
    }
}

.arrow-icon {
    opacity: 0.6;
    transition: all 0.3s ease;
}

.cache-item:hover .arrow-icon {
    opacity: 1;
    transform: translateX(4px);
}

/* 键名列表样式 */
.keys-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.keys-header {
    padding: 0.75rem 1rem;
    background: #f7fafc;
    border-radius: 8px;
    border-left: 4px solid #38a169;
}

.keys-info {
    font-size: 0.875rem;
    font-weight: 600;
    color: #2d3748;
}

.keys-list {
    max-height: 400px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.key-item {
    border-radius: 10px;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
    cursor: pointer;
    animation: slideInRight 0.4s ease forwards;
    opacity: 0;
    transform: translateX(20px);
}

@keyframes slideInRight {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.key-item:hover {
    border-color: #cbd5e0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.key-item-selected {
    border-color: #38a169;
    background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
    box-shadow: 0 2px 12px rgba(56, 161, 105, 0.2);
}

.key-item-content {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    gap: 0.5rem;
}

.key-item-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f7fafc;
    border-radius: 6px;
}

.key-item-selected .key-item-icon {
    background: #38a169;
    color: white;
}

.key-item-info {
    flex: 1;
    min-width: 0;
}

.key-item-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #2d3748;
    word-break: break-all;
    line-height: 1.4;
}

.key-item-action {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* 内容区域样式 */
.content-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.cache-info-panel {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-radius: 12px;
    padding: 1.25rem;
    border: 1px solid #e2e8f0;
}

.info-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.info-title {
    font-weight: 600;
    color: #2d3748;
    font-size: 0.95rem;
}

.info-grid {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.info-label {
    font-size: 0.875rem;
    color: #4a5568;
    font-weight: 500;
    min-width: 80px;
}

.content-panel {
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.content-header {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 1rem 1.25rem;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.content-title {
    font-weight: 600;
    color: #2d3748;
    font-size: 0.95rem;
}

.content-stats {
    margin-left: auto;
}

.code-container {
    background: #1a202c;
    border-radius: 0 0 12px 12px;
    overflow: hidden;
}

.code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background: #2d3748;
    border-bottom: 1px solid #4a5568;
}

.code-dots {
    display: flex;
    gap: 0.5rem;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.dot-red {
    background: #fc8181;
}

.dot-yellow {
    background: #f6e05e;
}

.dot-green {
    background: #68d391;
}

.code-lang {
    font-size: 0.75rem;
    color: #a0aec0;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.code-content {
    max-height: 500px;
    overflow: auto;
    padding: 1.25rem;
}

.code-pre {
    color: #68d391;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 0;
}

/* 按钮样式 */
.refresh-btn,
.copy-btn,
.delete-all-btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.refresh-btn:hover,
.copy-btn:hover,
.delete-all-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.delete-all-btn {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
    border: none;
}

.delete-all-btn:hover {
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
}

/* 删除按钮样式 */
.delete-btn {
    opacity: 0;
    transition: all 0.3s ease;
    padding: 4px;
    border-radius: 6px;
    background: transparent;
    border: none;
    color: #e53e3e;
}

.cache-item:hover .delete-btn,
.key-item:hover .delete-btn {
    opacity: 1;
}

.delete-btn:hover {
    background: rgba(229, 62, 62, 0.1);
    transform: scale(1.1);
}

/* 滚动条样式 */
.keys-list::-webkit-scrollbar,
.code-content::-webkit-scrollbar {
    width: 6px;
}

.keys-list::-webkit-scrollbar-track,
.code-content::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.keys-list::-webkit-scrollbar-thumb,
.code-content::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 3px;
}

.keys-list::-webkit-scrollbar-thumb:hover,
.code-content::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .grid-cols-1.lg\\:grid-cols-3 {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }


    .header-icon {
        width: 40px;
        height: 40px;
    }

    .cache-item-content,
    .key-item-content {
        padding: 0.75rem;
    }

    .info-grid {
        gap: 0.5rem;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .info-label {
        min-width: auto;
        font-size: 0.8rem;
    }
}

@media (max-width: 640px) {
    .page-header {
        margin-bottom: 1.5rem;
    }

    .card-title {
        padding: 1rem 1rem 0 1rem;
    }

    .card-content {
        padding: 0.75rem 1rem 1rem 1rem;
    }

    .code-content {
        padding: 1rem;
    }

    .code-pre {
        font-size: 0.8rem;
    }
}
</style>
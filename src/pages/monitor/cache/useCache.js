import { ref } from 'vue'
import {
  getCacheNames,
  getCacheKeys,
  getCacheValue,
  delCacheName,
  delCacheKey,
  delAll,
} from '../../../api/monitor/cache'

export function useCache() {
  const cacheNamesObject = ref([])
  const cacheKeys = ref([])
  const cacheValue = ref(null)
  const isLoading = ref(false)
  const isLoadingKeys = ref(false)
  const isLoadingValue = ref(false)
  const error = ref(null)

  // 当前选中的缓存名称和键名
  const selectedCacheName = ref('')
  const selectedCacheKey = ref('')

  // 获取缓存名称列表
  const fetchCacheNames = async () => {
    isLoading.value = true
    error.value = null
    try {
      const response = await getCacheNames()
      if (response && response.data) {
        cacheNamesObject.value = response.data || []
      }
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('获取缓存名称失败')
      cacheNamesObject.value = []
    } finally {
      isLoading.value = false
    }
  }

  // 获取缓存键名列表
  const fetchCacheKeys = async (cacheName) => {
    if (!cacheName) return

    isLoadingKeys.value = true
    error.value = null
    selectedCacheName.value = cacheName
    selectedCacheKey.value = '' // 清空之前选中的键名
    cacheValue.value = null // 清空之前的缓存内容

    try {
      const response = await getCacheKeys(cacheName)
      if (response && response.data) {
        cacheKeys.value = response.data || []
      }
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('获取缓存键名失败')
      cacheKeys.value = []
    } finally {
      isLoadingKeys.value = false
    }
  }

  // 获取缓存内容
  const fetchCacheValue = async (cacheName, cacheKey) => {
    if (!cacheName || !cacheKey) return

    isLoadingValue.value = true
    error.value = null
    selectedCacheKey.value = cacheKey

    try {
      const response = await getCacheValue(cacheName, cacheKey)
      if (response && response.data) {
        cacheValue.value = response.data
      }
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('获取缓存内容失败')
      cacheValue.value = null
    } finally {
      isLoadingValue.value = false
    }
  }

  // 删除缓存名称（模糊删除）
  const deleteCacheName = async (cacheName) => {
    if (!cacheName) return false

    try {
      const response = await delCacheName(cacheName)
      if (response && response.code === 200) {
        // 删除成功后刷新缓存名称列表
        await fetchCacheNames()
        // 如果删除的是当前选中的缓存名称，则重置选择
        if (selectedCacheName.value === cacheName) {
          resetCache()
        }
        return true
      }
      return false
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('删除缓存名称失败')
      return false
    }
  }

  // 删除缓存键（精确删除）
  const deleteCacheKey = async (cacheKey) => {
    if (!cacheKey || !selectedCacheName.value) return false

    try {
      const response = await delCacheKey(cacheKey)
      if (response && response.code === 200) {
        // 删除成功后刷新缓存键列表
        await fetchCacheKeys(selectedCacheName.value)
        // 如果删除的是当前选中的缓存键，则清空选择
        if (selectedCacheKey.value === cacheKey) {
          selectedCacheKey.value = ''
          cacheValue.value = null
        }
        return true
      }
      return false
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('删除缓存键失败')
      return false
    }
  }

  // 删除所有缓存
  const deleteAllCache = async () => {
    try {
      const response = await delAll()
      if (response && response.code === 200) {
        // 删除成功后重置所有数据并刷新
        resetCache()
        await fetchCacheNames()
        return true
      }
      return false
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('清空所有缓存失败')
      return false
    }
  }

  // 重置所有数据
  const resetCache = () => {
    selectedCacheName.value = ''
    selectedCacheKey.value = ''
    cacheKeys.value = []
    cacheValue.value = null
    error.value = null
  }

  return {
    // 响应式数据
    cacheNamesObject,
    cacheKeys,
    cacheValue,
    isLoading,
    isLoadingKeys,
    isLoadingValue,
    error,
    selectedCacheName,
    selectedCacheKey,

    // 方法
    fetchCacheNames,
    fetchCacheKeys,
    fetchCacheValue,
    deleteCacheName,
    deleteCacheKey,
    deleteAllCache,
    resetCache,
  }
}

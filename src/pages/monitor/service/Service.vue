<template>
    <div class="p-0">
        <!-- 页面标题 -->
        <div class="mb-8">
            <div class="flex items-center gap-4">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 m-0">服务器监控</h1>
                    <p class="text-sm text-gray-600 mt-1 mb-0">实时监控服务器CPU、内存、JVM和磁盘使用情况</p>
                </div>
                <div class="ml-auto">
                    <VaButton preset="secondary" icon="mso-refresh" @click="refreshData" :loading="isLoading"
                        class="min-w-[120px]">
                        刷新数据
                    </VaButton>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="isLoading && !serviceInfo" class="flex flex-col items-center justify-center py-16 px-8 text-center">
            <VaProgressCircle indeterminate color="primary" size="large" />
            <p class="mt-4 text-base text-gray-600">正在加载服务器监控数据...</p>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="flex flex-col items-center justify-center py-16 px-8 text-center">
            <VaIcon name="mso-error" color="danger" size="large" />
            <p class="mt-4 text-base text-gray-600">{{ error.message }}</p>
            <VaButton preset="secondary" @click="refreshData" class="mt-4">
                重新加载
            </VaButton>
        </div>

        <!-- 主要内容 -->
        <div v-else-if="serviceInfo" class="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-4">
            <!-- CPU信息卡片 -->
            <VaCard class="mb-4 shadow-lg rounded-xl">
                <VaCardTitle class="px-6 pt-6 pb-4 border-b border-gray-200 font-semibold mb-0">
                    <div class="flex items-center gap-2">
                        <VaIcon name="mso-memory" color="primary" />
                        <span>CPU信息</span>
                    </div>
                </VaCardTitle>
                <VaCardContent>
                    <div class="p-8 px-6">
                        <div class="flex items-center gap-10">
                            <div class="flex-shrink-0 w-30 h-30 flex items-center justify-center">
                                <VaProgressCircle :model-value="serviceInfo.cpu.used"
                                    :color="getCpuStatusColor(serviceInfo.cpu.used)" size="large" :thickness="0.08"
                                    :max="100">
                                    <div class="text-center w-20 flex flex-col items-center justify-center">
                                        <div class="text-lg font-bold text-gray-900 leading-tight whitespace-nowrap">
                                            {{ formatPercentage(serviceInfo.cpu.used) }}</div>
                                        <div class="text-xs text-gray-600 mt-1 whitespace-nowrap">
                                            CPU使用率</div>
                                    </div>
                                </VaProgressCircle>
                            </div>
                            <div class="flex-1 grid grid-cols-2 gap-5">
                                <div
                                    class="text-center p-5 bg-gray-50 rounded-lg transition-transform hover:-translate-y-0.5">
                                    <div class="text-sm text-gray-600 mb-2">核心数</div>
                                    <div class="text-lg font-semibold text-gray-900">{{
                                        serviceInfo.cpu.cpuNum }} 核</div>
                                </div>
                                <div
                                    class="text-center p-5 bg-gray-50 rounded-lg transition-transform hover:-translate-y-0.5">
                                    <div class="text-sm text-gray-600 mb-2">系统使用率</div>
                                    <div class="text-lg font-semibold text-gray-900">{{
                                        formatPercentage(serviceInfo.cpu.sys) }}</div>
                                </div>
                                <div
                                    class="text-center p-5 bg-gray-50 rounded-lg transition-transform hover:-translate-y-0.5">
                                    <div class="text-sm text-gray-600 mb-2">空闲率</div>
                                    <div class="text-lg font-semibold text-gray-900">{{
                                        formatPercentage(serviceInfo.cpu.free) }}</div>
                                </div>
                                <div
                                    class="text-center p-5 bg-gray-50 rounded-lg transition-transform hover:-translate-y-0.5">
                                    <div class="text-sm text-gray-600 mb-2">等待率</div>
                                    <div class="text-lg font-semibold text-gray-900">{{
                                        formatPercentage(serviceInfo.cpu.wait) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </VaCardContent>
            </VaCard>

            <!-- 内存信息卡片 -->
            <VaCard class="mb-4 shadow-lg rounded-xl">
                <VaCardTitle class="px-6 pt-6 pb-4 border-b border-gray-200 font-semibold mb-0">
                    <div class="flex items-center gap-2">
                        <VaIcon name="mso-storage" color="success" />
                        <span>内存信息</span>
                    </div>
                </VaCardTitle>
                <VaCardContent>
                    <div class="p-8 px-6">
                        <div class="flex items-center gap-10">
                            <div class="flex-shrink-0 w-30 h-30 flex items-center justify-center">
                                <VaProgressCircle :model-value="serviceInfo.mem.usage"
                                    :color="getMemoryStatusColor(serviceInfo.mem.usage)" size="large" :thickness="0.08"
                                    :max="100">
                                    <div class="text-center w-20 flex flex-col items-center justify-center">
                                        <div class="text-lg font-bold text-gray-900 leading-tight whitespace-nowrap">
                                            {{ formatPercentage(serviceInfo.mem.usage) }}</div>
                                        <div class="text-xs text-gray-600 mt-1 whitespace-nowrap">
                                            内存使用率</div>
                                    </div>
                                </VaProgressCircle>
                            </div>
                            <div class="flex-1 grid grid-cols-1 gap-5">
                                <div
                                    class="flex justify-between items-center p-5 bg-gray-50 rounded-lg transition-transform hover:-translate-y-0.5">
                                    <div class="text-sm text-gray-600">总内存</div>
                                    <div class="text-lg font-semibold text-gray-900">{{
                                        serviceInfo.mem.total }} GB</div>
                                </div>
                                <div
                                    class="flex justify-between items-center p-5 bg-gray-50 rounded-lg transition-transform hover:-translate-y-0.5">
                                    <div class="text-sm text-gray-600">已使用</div>
                                    <div class="text-lg font-semibold text-gray-900">{{
                                        serviceInfo.mem.used }} GB</div>
                                </div>
                                <div
                                    class="flex justify-between items-center p-5 bg-gray-50 rounded-lg transition-transform hover:-translate-y-0.5">
                                    <div class="text-sm text-gray-600">空闲内存</div>
                                    <div class="text-lg font-semibold text-gray-900">{{
                                        serviceInfo.mem.free }} GB</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </VaCardContent>
            </VaCard>

            <!-- JVM信息卡片 -->
            <VaCard class="mb-4 shadow-lg rounded-xl">
                <VaCardTitle class="px-6 pt-6 pb-4 border-b border-gray-200 font-semibold mb-0">
                    <div class="flex items-center gap-2">
                        <VaIcon name="mso-code" color="warning" />
                        <span>JVM信息</span>
                    </div>
                </VaCardTitle>
                <VaCardContent>
                    <div class="p-8 px-6">
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-5 mb-8">
                            <div
                                class="flex justify-between items-center p-4 bg-gray-50 rounded-lg transition-transform hover:-translate-y-0.5">
                                <div class="text-sm text-gray-600">JVM使用率</div>
                                <div class="text-base font-semibold text-gray-900">{{
                                    formatPercentage(serviceInfo.jvm.usage) }}</div>
                            </div>
                            <div
                                class="flex justify-between items-center p-4 bg-gray-50 rounded-lg transition-transform hover:-translate-y-0.5">
                                <div class="text-sm text-gray-600">最大内存</div>
                                <div class="text-base font-semibold text-gray-900">{{
                                    serviceInfo.jvm.max }} MB</div>
                            </div>
                            <div
                                class="flex justify-between items-center p-4 bg-gray-50 rounded-lg transition-transform hover:-translate-y-0.5">
                                <div class="text-sm text-gray-600">已使用</div>
                                <div class="text-base font-semibold text-gray-900">{{
                                    serviceInfo.jvm.used }} MB</div>
                            </div>
                            <div
                                class="flex justify-between items-center p-4 bg-gray-50 rounded-lg transition-transform hover:-translate-y-0.5">
                                <div class="text-sm text-gray-600">空闲内存</div>
                                <div class="text-base font-semibold text-gray-900">{{
                                    serviceInfo.jvm.free }} MB</div>
                            </div>
                            <div
                                class="flex justify-between items-center p-4 bg-gray-50 rounded-lg transition-transform hover:-translate-y-0.5 sm:col-span-2 lg:col-span-1 xl:col-span-2">
                                <div class="text-sm text-gray-600">运行时间</div>
                                <div class="text-base font-semibold text-gray-900">{{
                                    formatRunTime(serviceInfo.jvm.runTime) }}</div>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-5">
                            <div
                                class="text-center p-5 bg-gray-50 rounded-lg transition-transform hover:-translate-y-0.5">
                                <div class="text-sm text-gray-600 mb-2">Java版本</div>
                                <div class="text-base font-semibold text-gray-900">{{
                                    serviceInfo.jvm.version }}</div>
                            </div>
                            <div
                                class="text-center p-5 bg-gray-50 rounded-lg transition-transform hover:-translate-y-0.5">
                                <div class="text-sm text-gray-600 mb-2">JVM名称</div>
                                <div class="text-base font-semibold text-gray-900">{{
                                    serviceInfo.jvm.name }}</div>
                            </div>
                            <div
                                class="text-center p-5 bg-gray-50 rounded-lg transition-transform hover:-translate-y-0.5">
                                <div class="text-sm text-gray-600 mb-2">Java路径</div>
                                <div class="text-base font-semibold text-gray-900 truncate"
                                    :title="serviceInfo.jvm.home">{{ serviceInfo.jvm.home }}</div>
                            </div>
                            <div
                                class="text-center p-5 bg-gray-50 rounded-lg transition-transform hover:-translate-y-0.5">
                                <div class="text-sm text-gray-600 mb-2">启动时间</div>
                                <div class="text-base font-semibold text-gray-900">{{
                                    formatStartTime(serviceInfo.jvm.startTime) }}</div>
                            </div>
                        </div>
                    </div>
                </VaCardContent>
            </VaCard>

            <!-- 系统信息卡片 -->
            <VaCard class="mb-4 shadow-lg rounded-xl">
                <VaCardTitle class="px-6 pt-6 pb-4 border-b border-gray-200 font-semibold mb-0">
                    <div class="flex items-center gap-2">
                        <VaIcon name="mso-computer" color="info" />
                        <span>系统信息</span>
                    </div>
                </VaCardTitle>
                <VaCardContent>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-5 p-8 px-6">
                        <div class="text-center p-5 bg-gray-50 rounded-lg transition-transform hover:-translate-y-0.5">
                            <div class="text-sm text-gray-600 mb-2">计算机名</div>
                            <div class="text-base font-semibold text-gray-900">{{
                                serviceInfo.sys.computerName }}</div>
                        </div>
                        <div class="text-center p-5 bg-gray-50 rounded-lg transition-transform hover:-translate-y-0.5">
                            <div class="text-sm text-gray-600 mb-2">计算机IP</div>
                            <div class="text-base font-semibold text-gray-900">{{
                                serviceInfo.sys.computerIp }}</div>
                        </div>
                        <div class="text-center p-5 bg-gray-50 rounded-lg transition-transform hover:-translate-y-0.5">
                            <div class="text-sm text-gray-600 mb-2">操作系统</div>
                            <div class="text-base font-semibold text-gray-900">{{ serviceInfo.sys.osName
                                }}</div>
                        </div>
                        <div class="text-center p-5 bg-gray-50 rounded-lg transition-transform hover:-translate-y-0.5">
                            <div class="text-sm text-gray-600 mb-2">系统架构</div>
                            <div class="text-base font-semibold text-gray-900">{{ serviceInfo.sys.osArch
                                }}</div>
                        </div>
                        <div
                            class="text-center p-5 bg-gray-50 rounded-lg transition-transform hover:-translate-y-0.5 sm:col-span-2 lg:col-span-1 xl:col-span-2">
                            <div class="text-sm text-gray-600 mb-2">项目路径</div>
                            <div class="text-base font-semibold text-gray-900 truncate"
                                :title="serviceInfo.sys.userDir">{{ serviceInfo.sys.userDir }}</div>
                        </div>
                    </div>
                </VaCardContent>
            </VaCard>

            <!-- 磁盘信息卡片 -->
            <VaCard class="mb-4 shadow-lg rounded-xl col-span-1 lg:col-span-2">
                <VaCardTitle class="px-6 pt-6 pb-4 border-b border-gray-200 font-semibold mb-0">
                    <div class="flex items-center gap-2">
                        <VaIcon name="mso-storage" color="danger" />
                        <span>磁盘信息</span>
                    </div>
                </VaCardTitle>
                <VaCardContent>
                    <div class="p-8 px-6 flex flex-col gap-7">
                        <div v-for="disk in serviceInfo.sysFiles" :key="disk.dirName"
                            class="p-7 bg-gray-50 rounded-xl transition-transform hover:-translate-y-0.5">
                            <div class="flex justify-between items-center mb-4">
                                <div class="flex items-center gap-3">
                                    <VaIcon name="mso-folder" :color="getDiskStatusColor(disk.usage)" />
                                    <span class="font-semibold text-gray-900">{{ disk.typeName }}</span>
                                    <VaBadge :text="disk.dirName" color="info" />
                                </div>
                                <div class="flex-shrink-0">
                                    <VaBadge :text="formatPercentage(disk.usage)"
                                        :color="getDiskStatusColor(disk.usage)" />
                                </div>
                            </div>
                            <div class="mb-4">
                                <VaProgressBar :model-value="disk.usage" :color="getDiskStatusColor(disk.usage)"
                                    size="small" />
                            </div>
                            <div class="grid grid-cols-2 sm:grid-cols-4 gap-4">
                                <div class="flex flex-col gap-1">
                                    <span class="text-sm text-gray-600">总容量:</span>
                                    <span class="text-base font-semibold text-gray-900">{{ disk.total
                                    }}</span>
                                </div>
                                <div class="flex flex-col gap-1">
                                    <span class="text-sm text-gray-600">已使用:</span>
                                    <span class="text-base font-semibold text-gray-900">{{ disk.used
                                    }}</span>
                                </div>
                                <div class="flex flex-col gap-1">
                                    <span class="text-sm text-gray-600">可用:</span>
                                    <span class="text-base font-semibold text-gray-900">{{ disk.free
                                    }}</span>
                                </div>
                                <div class="flex flex-col gap-1">
                                    <span class="text-sm text-gray-600">文件系统:</span>
                                    <span class="text-base font-semibold text-gray-900">{{
                                        disk.sysTypeName }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </VaCardContent>
            </VaCard>
        </div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useService } from './useService'

const {
    serviceInfo,
    isLoading,
    error,
    fetchServiceInfo,
    formatPercentage,
    formatRunTime,
    formatStartTime,
    getCpuStatusColor,
    getMemoryStatusColor,
    getDiskStatusColor
} = useService()

// 刷新数据
const refreshData = () => {
    fetchServiceInfo()
}

// 组件挂载时获取数据
onMounted(() => {
    fetchServiceInfo()
})
</script>

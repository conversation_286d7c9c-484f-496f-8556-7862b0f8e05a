import { ref } from 'vue'
import { getServiceInfo } from '../../../api/monitor/service'

export function useService() {
  const serviceInfo = ref(null)
  const isLoading = ref(false)
  const error = ref(null)

  // 获取服务器信息
  const fetchServiceInfo = async () => {
    isLoading.value = true
    error.value = null

    try {
      const response = await getServiceInfo()
      if (response && response.data) {
        serviceInfo.value = response.data
      }
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('获取服务器信息失败')
      serviceInfo.value = null
    } finally {
      isLoading.value = false
    }
  }

  // 格式化字节大小
  const formatBytes = (bytes, decimals = 2) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const dm = decimals < 0 ? 0 : decimals
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
  }

  // 格式化百分比
  const formatPercentage = (value) => {
    return `${parseFloat(value).toFixed(2)}%`
  }

  // 格式化运行时间
  const formatRunTime = (runTime) => {
    if (!runTime) return '-'
    return runTime
  }

  // 格式化JVM启动时间
  const formatStartTime = (startTime) => {
    if (!startTime) return '-'
    return startTime
  }

  // 获取CPU使用率状态颜色
  const getCpuStatusColor = (usage) => {
    if (usage < 50) return 'success'
    if (usage < 80) return 'warning'
    return 'danger'
  }

  // 获取内存使用率状态颜色
  const getMemoryStatusColor = (usage) => {
    if (usage < 60) return 'success'
    if (usage < 85) return 'warning'
    return 'danger'
  }

  // 获取磁盘使用率状态颜色
  const getDiskStatusColor = (usage) => {
    if (usage < 70) return 'success'
    if (usage < 90) return 'warning'
    return 'danger'
  }

  return {
    serviceInfo,
    isLoading,
    error,
    fetchServiceInfo,
    formatBytes,
    formatPercentage,
    formatRunTime,
    formatStartTime,
    getCpuStatusColor,
    getMemoryStatusColor,
    getDiskStatusColor,
  }
}

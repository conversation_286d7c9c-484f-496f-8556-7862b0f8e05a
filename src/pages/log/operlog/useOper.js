import { ref } from 'vue'
import { getOperlogPage } from '../../../api/log/operlog'

export function useOper() {
  const operlogList = ref([])
  const isLoading = ref(false)
  const error = ref(null)

  const filters = ref({
    search: '',
    status: '',
    operType: '',
    businessType: '',
  })

  const pagination = ref({
    pageNum: 1,
    pageSize: 20,
    total: 0,
  })

  const sorting = ref({
    sortBy: 'operTime',
    sortingOrder: 'desc',
  })

  // 获取操作日志列表
  const fetchOperlog = async () => {
    isLoading.value = true
    error.value = null
    try {
      const response = await getOperlogPage({
        pageNum: pagination.value.pageNum,
        pageSize: pagination.value.pageSize,
        title: filters.value.search || undefined,
        operType: filters.value.operType || undefined,
        businessType: filters.value.businessType || undefined,
        status: filters.value.status || undefined,
      })

      // 根据实际API响应结构处理数据
      if (response && response.data) {
        operlogList.value = response.data.pageList || []
        pagination.value.total = response.data.total || 0
      }
      console.log(operlogList.value, response.data.total);

    } catch (err) {
      error.value = err instanceof Error ? err : new Error('获取操作日志失败')
      operlogList.value = []
      pagination.value.total = 0
    } finally {
      isLoading.value = false
    }
  }

  return {
    // 响应式数据
    operlogList,
    isLoading,
    error,
    filters,
    pagination,
    sorting,

    // 方法
    fetchOperlog,
  }
}

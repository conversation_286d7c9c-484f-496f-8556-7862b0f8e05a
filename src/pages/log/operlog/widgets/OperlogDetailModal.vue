<script setup>
import { computed } from 'vue'

const props = defineProps({
  operlog: {
    type: Object,
    default: null,
  },
  visible: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['close'])

/**
 * 格式化时间戳
 * @param {number} timestamp - 时间戳
 * @returns {string} 格式化后的时间字符串
 */
const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  return new Date(timestamp * 1000).toLocaleString('zh-CN')
}

/**
 * 格式化业务类型
 * @param {string} businessType - 业务类型代码
 * @returns {string} 业务类型名称
 */
const formatBusinessType = (businessType) => {
  const typeMap = {
    '0': '其它',
    '1': '新增',
    '2': '修改',
    '3': '删除',
    '4': '授权',
    '5': '导出',
    '6': '导入',
    '7': '强退',
    '8': '生成代码',
    '9': '清空数据',
  }
  return typeMap[businessType] || businessType
}

/**
 * 格式化JSON字符串
 * @param {string} jsonStr - JSON字符串
 * @returns {string} 格式化后的JSON字符串
 */
const formatJson = (jsonStr) => {
  if (!jsonStr) return ''
  try {
    return JSON.stringify(JSON.parse(jsonStr), null, 2)
  } catch (e) {
    return jsonStr
  }
}

/**
 * 格式化用户类型
 * @param {number|string} operType - 用户类型代码
 * @returns {string} 用户类型名称
 */
const formatUserType = (operType) => {
  const typeMap = {
    "0": '其它',
    "1": '后台用户',
    "2": '手机端用户',
  }
  return typeMap[operType] || '其它'
}

const operlogData = computed(() => {
  if (!props.operlog) return null

  return [
    { label: '操作ID', value: props.operlog.id },
    { label: '操作模块', value: props.operlog.title },
    { label: '业务类型', value: formatBusinessType(props.operlog.businessType) },
    { label: '操作方法', value: props.operlog.method },
      { label: '请求方式', value: props.operlog.requestMethod },
    { label: '操作人员', value: `${props.operlog.operName || '-'} (${formatUserType(props.operlog.operName)})` },
    { label: '请求URL', value: props.operlog.operUrl },
    { label: '主机地址', value: props.operlog.operIp },
    { label: '操作地点', value: props.operlog.operLocation },
    { label: '操作状态', value: props.operlog.status === 0 ? '成功' : '失败' },
    { label: '创建时间', value: formatTime(props.operlog.createTime) },
    { label: '更新时间', value: formatTime(props.operlog.updateTime) },
    { label: '消耗时间', value: props.operlog.costTime ? `${props.operlog.costTime}ms` : '-' },
    { label: '备注', value: props.operlog.remark },
  ]
})
</script>

<template>
  <div>
    <h1 class="va-h5 mb-4">操作日志详情</h1>

    <div v-if="operlogData" class="space-y-4">
      <!-- 基本信息 -->
      <VaCard>
        <VaCardTitle>基本信息</VaCardTitle>
        <VaCardContent>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div v-for="item in operlogData" :key="item.label" class="flex flex-col">
              <span class="text-sm text-gray-500 mb-1">{{ item.label }}</span>
              <span class="font-medium">{{ item.value || '-' }}</span>
            </div>
          </div>
        </VaCardContent>
      </VaCard>

      <!-- 请求参数 -->
      <VaCard v-if="operlog.operParam">
        <VaCardTitle>请求参数</VaCardTitle>
        <VaCardContent>
          <pre class="bg-gray-50 p-3 rounded text-sm overflow-auto max-h-60">{{ formatJson(operlog.operParam) }}</pre>
        </VaCardContent>
      </VaCard>

      <!-- 返回参数 -->
      <VaCard v-if="operlog.jsonResult">
        <VaCardTitle>返回参数</VaCardTitle>
        <VaCardContent>
          <pre class="bg-gray-50 p-3 rounded text-sm overflow-auto max-h-60">{{ formatJson(operlog.jsonResult) }}</pre>
        </VaCardContent>
      </VaCard>

      <!-- 异常信息 -->
      <VaCard v-if="operlog.errorMsg">
        <VaCardTitle>异常信息</VaCardTitle>
        <VaCardContent>
          <pre class="bg-red-50 p-3 rounded text-sm overflow-auto max-h-60 text-red-700">{{ operlog.errorMsg }}</pre>
        </VaCardContent>
      </VaCard>
    </div>

    <div class="flex justify-end mt-6">
      <VaButton @click="emit('close')">关闭</VaButton>
    </div>
  </div>
</template>

<style scoped>
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>

<script setup>
import { defineVaDataTableColumns } from 'vuestic-ui'
import { computed, watch } from 'vue'
import { useOper } from '../useOper'

const columns = defineVaDataTableColumns([
  { label: '操作ID', key: 'id', sortable: true },
  { label: '操作模块', key: 'title', sortable: true },
  { label: '业务类型', key: 'businessType', sortable: false },
  { label: '操作方法', key: 'method', sortable: false },
  { label: '请求方式', key: 'requestMethod', sortable: false },
  { label: '操作人员', key: 'operName', sortable: true },
  { label: '操作地址', key: 'operIp', sortable: false },
  { label: '操作状态', key: 'status', sortable: false },
  { label: '耗时', key: 'costTime', sortable: true },
  { label: '操作', key: 'actions', align: 'right' },
])

// 使用 useOper 获取数据和状态
const { operlogList, isLoading, pagination, sorting, fetchOperlog } = useOper()

const emit = defineEmits(['view-detail'])

const totalPages = computed(() => Math.ceil(pagination.value.total / pagination.value.pageSize))

// 处理分页变化
const handlePageChange = (pageNum) => {
  pagination.value.pageNum = pageNum
  fetchOperlog()
}

const handlePerPageChange = (pageSize) => {
  pagination.value.pageSize = pageSize
  pagination.value.pageNum = 1
  fetchOperlog()
}

// 监听排序变化
watch([() => sorting.value.sortBy, () => sorting.value.sortingOrder], () => {
  fetchOperlog()
})

// 初始化时加载数据
fetchOperlog()



/**
 * 格式化耗时
 * @param {number} costTime - 耗时（毫秒）
 * @returns {string} 格式化后的耗时字符串
 */
const formatCostTime = (costTime) => {
  if (!costTime && costTime !== 0) return '-'
  return `${costTime}ms`
}

/**
 * 格式化业务类型
 * @param {number|string} businessType - 业务类型代码
 * @returns {string} 业务类型名称
 */
const formatBusinessType = (businessType) => {
  const typeMap = {
    0: '其它',
    1: '新增',
    2: '修改',
    3: '删除',
    4: '授权',
    5: '导出',
    6: '导入',
    7: '强退',
    8: '清空数据',
  }
  return typeMap[businessType] || businessType
}

/**
 * 格式化请求方式
 * @param {string} method - 请求方式
 * @returns {string} 格式化后的请求方式
 */
const formatRequestMethod = (method) => {
  if (!method) return '-'
  return method.toUpperCase()
}

/**
 * 格式化用户类型
 * @param {number|string} operType - 用户类型代码
 * @returns {string} 用户类型名称
 */
const formatUserType = (operType) => {
  const typeMap = {
    "0": '其它',
    "1": '后台用户',
    "2": '手机端用户',
  }
  return typeMap[operType] || '其它'
}
</script>

<template>
  <VaDataTable v-model:sort-by="sorting.sortBy" v-model:sorting-order="sorting.sortingOrder" :items="operlogList"
    :columns="columns" :loading="isLoading">

    <template #cell(businessType)="{ rowData }">
      <VaBadge :text="formatBusinessType(rowData.businessType)"
        :color="rowData.businessType === '3' ? 'danger' : rowData.businessType === '1' ? 'success' : 'info'" />
    </template>

    <template #cell(requestMethod)="{ rowData }">
      <VaBadge :text="formatRequestMethod(rowData.requestMethod)"
        :color="rowData.requestMethod === 'POST' ? 'primary' : rowData.requestMethod === 'DELETE' ? 'danger' : 'info'" />
    </template>

    <template #cell(status)="{ rowData }">
      <VaBadge :text="rowData.status === 0 ? '成功' : '失败'" :color="rowData.status === 0 ? 'success' : 'danger'" />
    </template>

    <template #cell(costTime)="{ rowData }">
      {{ formatCostTime(rowData.costTime) }}
    </template>

    <template #cell(operName)="{ rowData }">
      <div class="flex flex-col">
        <VaBadge :text="formatUserType(rowData.operName)"
          :color="rowData.operName === 1 ? 'primary' : rowData.operName === 2 ? 'info' : 'secondary'" size="small" />
      </div>
    </template>

    <template #cell(method)="{ rowData }">
      <span class="text-sm text-gray-600" :title="rowData.method">
        {{ rowData.method ? rowData.method.split('.').pop() : '-' }}
      </span>
    </template>

    <template #cell(actions)="{ rowData }">
      <div class="flex gap-2 justify-end">
        <VaButton preset="primary" size="small" icon="mso-visibility" aria-label="查看详情"
          @click="$emit('view-detail', rowData)" />
      </div>
    </template>
  </VaDataTable>

  <div class="flex justify-end mt-4">


    <div>
      <b>{{ pagination.total }} 条结果</b>
      每页显示
      <VaSelect v-model="pagination.pageSize" class="!w-20 inline-block" :options="[10, 20, 50, 100]"
        @update:model-value="fetchOperlog" />
    </div>
    <VaPagination v-model="pagination.pageNum" :pages="totalPages" :visible-pages="5" buttons-preset="secondary" gapped
      border-color="primary" class="justify-center sm:justify-start" @update:modelValue="fetchOperlog" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import OperlogTable from './widgets/OperlogTable.vue'
import OperlogDetailModal from './widgets/OperlogDetailModal.vue'
import { useOper } from './useOper'
import { useToast } from 'vuestic-ui'
import { useTransition } from '@/composables/useTransition.js'

const doShowDetailModal = ref(false)
const { filters, fetchOperlog } = useOper()
const operlogToView = ref(null)

// 使用过渡动画功能
const {
    isExpanded: isFilterExpanded,
    contentRef: filterContent,
    isMobileView,
    toggle: toggleFilter,
    getContentStyles,
    init: initTransition,
    cleanup: cleanupTransition
} = useTransition({
    defaultExpanded: true,  // PC端默认展开
    breakpoint: 992,        // 小于992px为移动设备
    animationDuration: 300  // 动画持续时间
})

const showDetailModal = (operlog) => {
    operlogToView.value = operlog
    doShowDetailModal.value = true
}

const resetFilters = () => {
    filters.value.search = ''
    filters.value.status = ''
    filters.value.operType = ''
    filters.value.businessType = ''
    fetchOperlog()
}

// 初始化和清理
onMounted(() => {
    fetchOperlog()
    initTransition() // 初始化过渡动画相关逻辑
})

// 移除事件监听
onUnmounted(() => {
    cleanupTransition() // 清理过渡动画相关事件监听
})
</script>

<template>
    <div>

        <VaCard class="filter-card">
            <!-- 筛选区域标题和控制按钮 -->
            <div class="filter-header flex justify-between items-center pb-2">
                <div class="flex items-center gap-2">
                    <VaIcon name="mso-filter_list" color="primary" />
                    <h2 class="text-lg font-medium">数据筛选</h2>
                </div>
                <div class="flex gap-2">
                    <!-- 仅在移动设备和平板上显示展开/折叠按钮 -->
                    <VaButton v-if="isMobileView" preset="secondary" border-color="primary" size="small"
                        class="filter-toggle" @click="toggleFilter"
                        :icon="isFilterExpanded ? 'mso-expand_less' : 'mso-expand_more'"
                        :aria-label="isFilterExpanded ? '收起筛选' : '展开筛选'">
                        {{ isFilterExpanded ? '收起' : '展开' }}
                    </VaButton>
                </div>
            </div>

            <!-- 筛选区域内容 - 使用JS动画 -->
            <div ref="filterContent" class="filter-content" :style="getContentStyles()">
                <!-- 筛选表单 -->
                <div class="filter-form" v-show="isFilterExpanded">
                    <!-- 筛选条件网格 -->
                    <div class="filter-grid">
                        <!-- 操作状态筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">操作状态</label>
                            </div>
                            <VaSelect v-model="filters.status" placeholder="操作状态" :options="[
                                { text: '成功', value: '0' },
                                { text: '失败', value: '1' },
                            ]" track-by="value" @update:modelValue="fetchOperlog" :text-by="(option) => option.text"
                                :value-by="(option) => option.value" class="filter-input" />
                        </div>

                        <!-- 业务类型筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">业务类型</label>
                            </div>
                            <VaSelect v-model="filters.businessType" placeholder="业务类型" :options="[
                                { text: '其它', value: '0' },
                                { text: '新增', value: '1' },
                                { text: '修改', value: '2' },
                                { text: '删除', value: '3' },
                                { text: '授权', value: '4' },
                                { text: '导出', value: '5' },
                                { text: '导入', value: '6' },
                                { text: '强退', value: '7' },
                                { text: '生成代码', value: '8' },
                                { text: '清空数据', value: '9' },
                            ]" track-by="value" @update:modelValue="fetchOperlog" :text-by="(option) => option.text"
                                :value-by="(option) => option.value" class="filter-input" />
                        </div>

                        <!-- 搜索操作模块 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">操作模块</label>
                            </div>
                            <VaInput v-model="filters.search" placeholder="搜索操作模块" class="filter-input">
                                <template #prependInner>
                                    <VaIcon name="search" color="secondary" size="small" />
                                </template>
                            </VaInput>
                        </div>
                    </div>

                    <!-- 操作按钮区域 -->
                    <div class="filter-actions">
                        <VaButton color="primary" icon="mso-search" @click="fetchOperlog">搜索</VaButton>
                        <VaButton color="primary" icon="mso-refresh" @click="resetFilters">重置</VaButton>
                    </div>
                </div>
            </div>
        </VaCard>

        <VaCard>
            <OperlogTable @view-detail="showDetailModal" />
        </VaCard>

        <!-- 操作日志详情弹窗 -->
        <VaModal v-slot="{ cancel }" v-model="doShowDetailModal" size="large" mobile-fullscreen close-button
            hide-default-actions>
            <OperlogDetailModal v-if="operlogToView" :operlog="operlogToView" :visible="doShowDetailModal"
                @close="cancel" />
        </VaModal>

    </div>
</template>

<style scoped>
/* 基础样式 */
.filter-label {
    color: var(--va-primary);
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
}

/* 筛选表单布局 */
.filter-form {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

/* 筛选网格布局 */
.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 12px;
    margin-top: 10px;
}

.filter-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-bottom: 8px;
}

.filter-item-header {
    display: flex;
    align-items: center;
}

.filter-input {
    width: 100%;
}

/* 操作按钮区域 */
.filter-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    gap: 10px;
    margin: 8px 0;
}

/* 响应式样式 */
@media (max-width: 991px) {
    .filter-toggle {
        display: flex;
    }

    .filter-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 2fr));
    }
}

@media (min-width: 992px) {
    .filter-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }
}
</style>
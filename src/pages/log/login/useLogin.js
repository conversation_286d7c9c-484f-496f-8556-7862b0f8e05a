import { ref } from 'vue'
import { getLoginlogList } from '../../../api/log/loginlog'

export function useLogin() {
  const loginlogList = ref([])
  const isLoading = ref(false)
  const error = ref(null)

  const filters = ref({
    search: '',
    status: '',
    loginLocation: '',
  })

  const pagination = ref({
    pageNum: 1,
    pageSize: 20,
    total: 0,
  })

  const sorting = ref({
    sortBy: 'loginTime',
    sortingOrder: 'desc',
  })

  // 获取登录日志列表
  const fetchLoginlog = async () => {
    isLoading.value = true
    error.value = null
    try {
      const response = await getLoginlogList({
        pageNum: pagination.value.pageNum,
        pageSize: pagination.value.pageSize,
        userName: filters.value.search || undefined,
        status: filters.value.status || undefined,
        loginLocation: filters.value.loginLocation || undefined,
      })

      // 根据实际API响应结构处理数据
      if (response && response.data) {
        loginlogList.value = response.data.pageList || []
        pagination.value.total = response.data.total || 0
      }
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('获取登录日志失败')
      loginlogList.value = []
      pagination.value.total = 0
    } finally {
      isLoading.value = false
    }
  }

  return {
    // 响应式数据
    loginlogList,
    isLoading,
    error,
    filters,
    pagination,
    sorting,

    // 方法
    fetchLoginlog,
  }
}

<script setup>
import { defineVaDataTableColumns } from 'vuestic-ui'
import { computed, watch } from 'vue'
import { useLogin } from '../useLogin'

const columns = defineVaDataTableColumns([
  { label: '访问ID', key: 'infoId', sortable: true },
  { label: '用户名称', key: 'userName', sortable: true },
  { label: '登录地址', key: 'ipaddr', sortable: false },
  { label: '登录地点', key: 'loginLocation', sortable: false },
  { label: '浏览器', key: 'browser', sortable: false },
  { label: '操作系统', key: 'os', sortable: false },
  { label: '登录状态', key: 'status', sortable: false },
  { label: '操作信息', key: 'msg', sortable: false },
  { label: '登录时间', key: 'loginTime', sortable: true },
])

// 使用 useLogin 获取数据和状态
const { loginlogList, isLoading, pagination, sorting, fetchLoginlog } = useLogin()

const totalPages = computed(() => Math.ceil(pagination.value.total / pagination.value.pageSize))

// 处理分页变化
const handlePageNumChange = (page) => {
  pagination.value.pageNum = page
  fetchLoginlog()
}

const handlePageSizeChange = (perPage) => {
  pagination.value.pageSize = perPage
  pagination.value.pageNum = 1
  fetchLoginlog()
}

// 监听排序变化
watch([() => sorting.value.sortBy, () => sorting.value.sortingOrder], () => {
  fetchLoginlog()
})

// 初始化时加载数据
fetchLoginlog()

/**
 * 格式化时间戳
 * @param {number} timestamp - 时间戳
 * @returns {string} 格式化后的时间字符串
 */
const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  // 如果是秒级时间戳，需要乘以1000转换为毫秒级
  return new Date(timestamp * 1000).toLocaleString('zh-CN')
}

/**
 * 格式化登录状态
 * @param {number|string} status - 登录状态代码
 * @returns {string} 登录状态名称
 */
const formatLoginStatus = (status) => {
  return status === '0' || status === 0 ? '成功' : '失败'
}

/**
 * 获取登录状态颜色
 * @param {number|string} status - 登录状态代码
 * @returns {string} 颜色名称
 */
const getStatusColor = (status) => {
  return status === '0' || status === 0 ? 'success' : 'danger'
}
</script>

<template>
  <VaDataTable v-model:sort-by="sorting.sortBy" v-model:sorting-order="sorting.sortingOrder" :items="loginlogList"
    :columns="columns" :loading="isLoading" :per-page="pagination.pageSize" :current-page="pagination.pageNum">

    <template #cell(status)="{ rowData }">
      <VaBadge :text="formatLoginStatus(rowData.status)" :color="getStatusColor(rowData.status)" />
    </template>

    <template #cell(loginTime)="{ rowData }">
      {{ formatTime(rowData.loginTime) }}
    </template>

    <template #cell(msg)="{ rowData }">
      <span class="text-sm" :title="rowData.msg">
        {{ rowData.msg || '-' }}
      </span>
    </template>

    <template #cell(browser)="{ rowData }">
      <span class="text-sm" :title="rowData.browser">
        {{ rowData.browser || '-' }}
      </span>
    </template>

    <template #cell(os)="{ rowData }">
      <span class="text-sm" :title="rowData.os">
        {{ rowData.os || '-' }}
      </span>
    </template>
  </VaDataTable>

  <div class="flex flex-col-reverse md:flex-row gap-2 justify-end items-center py-2">

    <div>
      <b>{{ pagination.total }} 条结果</b>
      每页显示
      <VaSelect v-model="pagination.pageSize" class="!w-20 inline-block" :options="[10, 20, 50, 100]"
        @update:model-value="handlePageSizeChange" />
    </div>
    <div v-if="totalPages > 1" class="flex">
      <VaPagination :model-value="pagination.pageNum" @update:model-value="handlePageNumChange" class="justify-center"
        :pages="totalPages" :visible-pages="5" :boundary-links="false" :direction-links="false" />
    </div>
  </div>
</template>

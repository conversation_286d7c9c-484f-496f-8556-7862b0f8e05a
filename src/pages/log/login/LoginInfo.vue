<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import LoginlogTable from './widgets/LoginlogTable.vue'
import { useLogin } from './useLogin'
import { useToast } from 'vuestic-ui'
import { useTransition } from '@/composables/useTransition.js'

const { filters, fetchLoginlog } = useLogin()

const { init: notify } = useToast()

// 使用过渡动画功能
const {
    isExpanded: isFilterExpanded,
    contentRef: filterContent,
    isMobileView,
    toggle: toggleFilter,
    getContentStyles,
    init: initTransition,
    cleanup: cleanupTransition
} = useTransition({
    defaultExpanded: true,  // PC端默认展开
    breakpoint: 992,        // 小于992px为移动设备
    animationDuration: 300  // 动画持续时间
})

const resetFilters = () => {
    filters.value.search = ''
    filters.value.status = ''
    filters.value.loginLocation = ''
    fetchLoginlog()
}

// 初始化和清理
onMounted(() => {
    fetchLoginlog()
    initTransition() // 初始化过渡动画相关逻辑
})

// 移除事件监听
onUnmounted(() => {
    cleanupTransition() // 清理过渡动画相关事件监听
})
</script>

<template>
    <div>
        <VaCard>
            <VaCardContent>
                <div class="flex flex-col md:flex-row gap-2 mb-2 justify-between">
                    <div class="flex flex-col md:flex-row gap-2 justify-start">
                        <VaSelect v-model="filters.status" class="w-40" placeholder="登录状态" size="middle" :options="[
                            { text: '成功', value: '0' },
                            { text: '失败', value: '1' },
                        ]" track-by="value" @update:modelValue="fetchLoginlog" :text-by="(option) => option.text"
                            :value-by="(option) => option.value" />

                        <VaInput v-model="filters.loginLocation" placeholder="登录地点" class="w-40">
                            <template #prependInner>
                                <VaIcon name="location_on" color="secondary" size="small" />
                            </template>
                        </VaInput>

                        <VaInput v-model="filters.search" placeholder="搜索用户名称">
                            <template #prependInner>
                                <VaIcon name="search" color="secondary" size="small" />
                            </template>
                        </VaInput>
                    </div>
                    <div class="flex gap-2">
                        <VaButton color="primary" icon="mso-search" @click="fetchLoginlog">搜索</VaButton>
                        <VaButton color="primary" icon="mso-refresh" @click="resetFilters">重置</VaButton>
                    </div>
                </div>

                <LoginlogTable />
            </VaCardContent>
        </VaCard>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import RolesTable from './widgets/RolesTable.vue'
import EditRoleForm from './widgets/EditRoleForm.vue'
import UserRoleAssignModal from './widgets/UserRoleAssignModal.vue'
import { useRole } from './useRole'
import { useModal, useToast } from 'vuestic-ui'
import { useI18n } from 'vue-i18n'

// 使用 i18n
const { t } = useI18n()

const doShowEditRoleModal = ref(false)
const doShowUserRoleAssignModal = ref(false)
const { roles, isLoading, filters, sorting, pagination, fetchRoles, getTreeselect, menuOptions, ...rolesApi } = useRole()
const roleToEdit = ref(null)
import { useTransition } from '@/composables/useTransition.js'

// 使用过渡动画功能
const {
  isExpanded: isFilterExpanded,
  contentRef: filterContent,
  isMobileView,
  toggle: toggleFilter,
  getContentStyles,
  init: initTransition,
  cleanup: cleanupTransition
} = useTransition({
  defaultExpanded: true,  // PC端默认展开
  breakpoint: 992,        // 小于992px为移动设备
  animationDuration: 300  // 动画持续时间
})
const showEditRoleModal = (role) => {

  roleToEdit.value = role
  doShowEditRoleModal.value = true
}

/**
 * 显示添加角色弹窗
 */
const showAddRoleModal = () => {
  roleToEdit.value = null
  doShowEditRoleModal.value = true
}

const showUserAssignModal = (role) => {
  roleToEdit.value = role
  doShowUserRoleAssignModal.value = true
}

const { init: notify } = useToast()
const { confirm } = useModal()

const onRoleDelete = async (role) => {
  try {
    await rolesApi.deleteRole(role.id)
    notify({
      message: t('adminRole.messages.roleDeleted', { name: role.name }),
      color: 'success',
    })
  } catch (error) {
    notify({
      message: t('adminRole.messages.deleteFailed', { error }),
      color: 'danger',
    })
  }
}

const onRoleSaved = async (role) => {
  if (role.menuIds.length === 0 && role.menus.length > 0) {
    role.menuIds = role.menus
  }

  try {
    if (roleToEdit.value?.id) {
      await rolesApi.updateRoleData({ ...role, id: roleToEdit.value.id, })
      notify({
        message: t('adminRole.messages.roleUpdated', { name: role.name }),
        color: 'success',
      })
    } else {
      await rolesApi.createRole(role)
      notify({
        message: t('adminRole.messages.roleCreated', { name: role.name }),
        color: 'success',
      })
    }
  } catch (error) {

  }
}

/**
 * 用户角色分配成功处理函数
 */
const onUserRoleAssignSuccess = () => {
  // 可以在这里添加额外的逻辑，比如刷新数据等
  notify({
    message: t('adminRole.userAssign.messages.assignSuccess', { count: 'N/A', name: roleToEdit.value?.name || '' }),
    color: 'success',
  })
}

const resetFilters = () => {
  filters.value.search = ''
  filters.value.status = ''
  fetchRoles()
}

const editFormRef = ref()

const beforeEditFormModalClose = async (hide) => {
  hide()
}
// 生命周期钩子
onMounted(() => {
  initTransition() // 初始化过渡动画相关逻辑
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  cleanupTransition() // 清理过渡动画相关事件监听
})
// 初始化时加载数据
fetchRoles()
// 加载所有菜单的数据
getTreeselect()
</script>

<template>
  <div>


    <VaCard class="filter-card">
      <!-- 筛选区域标题和控制按钮 -->
      <div class="filter-header flex justify-between items-center pb-2">
        <div class="flex items-center gap-2">
          <VaIcon name="mso-filter_list" color="primary" />
          <h2 class="text-lg font-medium">{{ t('adminRole.filter.title') }}</h2>
        </div>
        <div class="flex gap-2">
          <!-- 仅在移动设备和平板上显示展开/折叠按钮 -->
          <VaButton v-if="isMobileView" preset="secondary" border-color="primary" size="small" class="filter-toggle"
            @click="toggleFilter" :icon="isFilterExpanded ? 'mso-expand_less' : 'mso-expand_more'"
            :aria-label="isFilterExpanded ? t('adminRole.filter.collapseFilter') : t('adminRole.filter.expandFilter')">
            {{ isFilterExpanded ? t('adminRole.filter.collapse') : t('adminRole.filter.expand') }}
          </VaButton>
        </div>
      </div>

      <!-- 筛选区域内容 - 使用JS动画 -->
      <div ref="filterContent" class="filter-content" :style="{
        ...getContentStyles(),
      }">
        <!-- 筛选表单 -->
        <div class="filter-form" v-show="isFilterExpanded">
          <!-- 筛选条件网格 -->
          <div class="filter-grid">
            <!-- 状态筛选 -->
            <div class="filter-item">
              <div class="filter-item-header">
                <label class="filter-label">{{ t('adminRole.filter.status') }}</label>
              </div>
              <VaSelect v-model="filters.status" :placeholder="t('adminRole.filter.statusPlaceholder')" :options="[
                { text: t('adminRole.status.enabled'), value: '1', textBy: 'first', valueBy: '1' },
                { text: t('adminRole.status.disabled'), value: '0', textBy: 'first', valueBy: '0' },
              ]" track-by="value" :text-by="(option) => option.text" :value-by="(option) => option.value"
                class="filter-input" />
            </div>

            <!-- 搜索筛选 -->
            <div class="filter-item">
              <div class="filter-item-header">
                <label class="filter-label">{{ t('adminRole.filter.search') }}</label>
              </div>
              <VaInput v-model="filters.search" :placeholder="t('adminRole.filter.searchPlaceholder')" class="filter-input">
                <template #prependInner>
                  <VaIcon name="search" color="secondary" size="small" />
                </template>
              </VaInput>
            </div>
          </div>

          <!-- 操作按钮区域 -->
          <div class="filter-actions mt-4">
            <VaButton color="primary" icon="mso-add" @click="showAddRoleModal">{{ t('adminRole.buttons.add') }}</VaButton>
            <VaButton color="primary" icon="mso-search" @click="fetchRoles">{{ t('adminRole.buttons.search') }}</VaButton>
            <VaButton preset="secondary" border-color="primary" icon="mso-refresh" @click="resetFilters">{{ t('adminRole.buttons.reset') }}</VaButton>
          </div>
        </div>
      </div>
    </VaCard>

    <VaCard>
      <RolesTable v-model:sort-by="sorting.sortBy" v-model:sorting-order="sorting.sortingOrder" :roles="roles"
        :loading="isLoading" :pagination="pagination" @edit-role="showEditRoleModal" @delete-role="onRoleDelete"
        @assign-users="showUserAssignModal" />
    </VaCard>

    <!-- 添加/编辑角色弹窗 -->
    <VaModal v-slot="{ cancel, ok }" v-model="doShowEditRoleModal" size="small" mobile-fullscreen close-button
      hide-default-actions :before-cancel="beforeEditFormModalClose">
      <h1 class="va-h5">{{ roleToEdit ? t('adminRole.form.editRole') : t('adminRole.form.addRole') }}</h1>
      <EditRoleForm ref="editFormRef" :role="roleToEdit" :menu-options="menuOptions"
        :save-button-label="roleToEdit ? t('adminRole.buttons.save') : t('adminRole.buttons.add')" @close="cancel" @save="
          (role) => {
            onRoleSaved(role)
            ok()
          }
        " />
    </VaModal>

    <!-- 用户角色分配弹窗 -->
    <!-- <VaModal v-slot="{ cancel }" v-model="doShowUserRoleAssignModal" size="large" mobile-fullscreen close-button
      hide-default-actions>
      <UserRoleAssignModal v-if="roleToEdit" :visible="doShowUserRoleAssignModal" :selected-role="roleToEdit"
        @close="cancel" @success="onUserRoleAssignSuccess" />
    </VaModal> -->

  </div>
</template>

<style scoped>
.primary-label {
  color: var(--va-primary);
  font-size: 12px;
  font-weight: 600;
}

.filter-label {
  color: var(--va-primary);
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
}

/* 筛选表单布局 */
.filter-form {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* 筛选网格布局 */
.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 12px;
  margin-top: 10px;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 8px;
}

.filter-item-header {
  display: flex;
  align-items: center;
}

.filter-input {
  width: 100%;
}

/* 操作按钮区域 */
.filter-actions {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 10px;
  margin: 8px 0;
}

/* 响应式样式 */
@media (max-width: 991px) {
  .filter-toggle {
    display: flex;
  }

  .filter-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 2fr));
  }
}

@media (min-width: 992px) {
  .filter-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }
}
</style>

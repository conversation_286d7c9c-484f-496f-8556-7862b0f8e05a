import { ref, reactive } from 'vue'
import {
  listRole,
  getRole,
  addRole,
  updateRole,
  delRole,
  batchAuthUserRole,
  batchCancelAuthUser,
} from '@/api/role/role'
import { handleTree } from '@/utils/subdigi';
import { listMenu } from "@/api/menu/menu";

export function useRole() {
  const roles = ref([])
  const isLoading = ref(false)
  const error = ref(null)
  const defaultMenuId = ref(0)
  const selectedRole = ref(null)
  const menuOptions = ref([])
  const filters = ref({
    search: '',
    status: '',
  })

  const pagination = ref({
    page: 1,
    perPage: 20,
    total: 0,
  })

  const sorting = ref({
    sortBy: 'name',
    sortingOrder: 'asc',
  })

  const queryParams = reactive({
    pageNum: 1,
    pageSize: 20,
    name: '',
    roleKey: '',
    status: '',
  })

  // 获取角色列表
  const fetchRoles = async () => {
    isLoading.value = true
    error.value = null
    try {
      const response = await listRole({
        pageNum: pagination.value.page,
        pageSize: pagination.value.perPage,
        name: filters.value.search || undefined,
        status: filters.value.status || undefined,
      })

      if (response.code === 200) {
        roles.value = response.data.pageList || []
        pagination.value.total = response.data.total || 0
      } else {
        error.value = new Error(response.msg || '获取角色列表失败')
      }
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('获取角色列表失败')
    } finally {
      isLoading.value = false
    }
  }

  // Get menu options for dropdown
  // Get menu options for dropdown
  const getTreeselect = () => {
    listMenu().then(response => {
      menuOptions.value = [];
      // 添加顶级菜单选项
      let rootMenu = {};

      // 转换API返回的数据为树形结构
      const treeData = handleTree(response.data, "id");

      // 将树形数据转换为选项格式
      rootMenu = treeData.map(item => {
        return normalizeMenuOption(item);
      });
      rootMenu.forEach(element => {
        menuOptions.value.push(element);
      });

      console.log(menuOptions.value);

    });
  };

  // Normalize menu option for dropdown
  const normalizeMenuOption = (node) => {
    const result = {
      id: node.id,
      label: node.name
    };

    if (node.children && node.children.length) {
      result.children = node.children.map(child => normalizeMenuOption(child));
    }

    return result;
  };
  /**
   * 获取角色详情
   *
   * @param {string} roleId - 角色ID
   * @returns {Promise<Object>} 角色详情数据
   */
  const getRoleDetail = async (roleId) => {
    try {
      const response = await getRole(roleId)
      selectedRole.value = response.data
      return response
    } catch (error) {
      console.error('获取角色详情失败:', error)
      throw error
    }
  }

  /**
   * 新增角色
   * 创建新角色后自动刷新角色列表
   *
   * @param {Object} roleData - 角色数据
   * @returns {Promise<Object>} 创建结果
   */
  const createRole = async (roleData) => {
    try {
      const response = await addRole(roleData)
      await fetchRoles()
      return response
    } catch (error) {
      console.error('新增角色失败:', error)
      throw error
    }
  }

  /**
   * 更新角色
   * 更新角色信息后自动刷新角色列表
   *
   * @param {Object} roleData - 角色数据
   * @returns {Promise<Object>} 更新结果
   */
  const updateRoleData = async (roleData) => {
    try {
      const response = await updateRole(roleData)
      await fetchRoles()
      return response
    } catch (error) {
      console.error('更新角色失败:', error)
      throw error
    }
  }

  /**
   * 删除角色
   * 删除角色后自动刷新角色列表
   *
   * @param {string} roleId - 角色ID
   * @returns {Promise<Object>} 删除结果
   */
  const deleteRole = async (roleId) => {
    try {
      const response = await delRole(roleId)
      await fetchRoles()
      return response
    } catch (error) {
      console.error('删除角色失败:', error)
      throw error
    }
  }

  // ==================== 权限管理方法 ====================

  // 取消用户授权
  const cancelUserAuth = async (data) => {
    try {
      const response = await batchCancelAuthUser(data)
      return response
    } catch (error) {
      console.error('取消用户授权失败:', error)
      throw error
    }
  }

  // 批量取消用户授权
  const cancelAllUserAuth = async (data) => {
    try {
      const response = await batchCancelAuthUser(data)
      return response
    } catch (error) {
      console.error('批量取消用户授权失败:', error)
      throw error
    }
  }

  // 批量授权用户
  const selectAllUserAuth = async (data) => {
    try {
      const response = await authUserSelectAll(data)
      return response
    } catch (error) {
      console.error('批量授权用户失败:', error)
      throw error
    }
  }

  // 重置查询参数
  const resetQueryParams = () => {
    Object.assign(queryParams, {
      pageNum: 1,
      pageSize: 10,
      name: '',
      roleKey: '',
      status: '',
    })
  }

  /**
   * 设置选中的角色
   *
   * @param {Object|null} role - 角色对象或null
   */
  const setSelectedRole = (role) => {
    selectedRole.value = role
  }

  return {
    // 响应式数据
    roles,
    isLoading,
    error,
    filters,
    pagination,
    sorting,
    queryParams,
    selectedRole,
    menuOptions,
    defaultMenuId,
    // 方法
    fetchRoles,
    getRoleDetail,
    createRole,
    updateRoleData,
    deleteRole,
    cancelUserAuth,
    cancelAllUserAuth,
    selectAllUserAuth,
    resetQueryParams,
    setSelectedRole,
    getTreeselect,
  }
}

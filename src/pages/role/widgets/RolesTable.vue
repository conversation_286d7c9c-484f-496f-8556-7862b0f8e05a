<script setup>
import { defineVaDataTableColumns, useModal } from 'vuestic-ui'
import { computed } from 'vue'
import { useVModel } from '@vueuse/core'
import { getRole } from '@/api/role/role'
import { treeselect } from '@/api/menu/menu'
import { useI18n } from 'vue-i18n'

// 使用 i18n
const { t } = useI18n()

const getRoleData = async (id) => {
  const res = await getRole(id)
  return res.data
}
const columns = defineVaDataTableColumns([
  { label: t('adminRole.table.id'), key: 'id', sortable: true },
  { label: t('adminRole.table.roleName'), key: 'name', sortable: true },
  { label: t('adminRole.table.roleKey'), key: 'roleKey', sortable: true },
  { label: t('adminRole.table.roleSort'), key: 'roleSort', sortable: true },
  { label: t('adminRole.table.status'), key: 'status', sortable: false },
  { label: t('adminRole.table.createTime'), key: 'createTime', sortable: true },
  { label: t('adminRole.table.actions'), key: 'actions', align: 'right' },
])

/**
 * 组件属性定义
 */
const props = defineProps({
  roles: {
    type: Array,
    required: true,
  },
  loading: { type: Boolean, default: false },
  pagination: { type: Object, required: true },
  sortBy: { type: String, default: '' },
  sortingOrder: { type: String, default: 'asc' },
})

const emit = defineEmits(['edit-role',
  'delete-role',
  'assign-users',
  'status-change',
  'update:sortBy',
  'update:sortingOrder'])

const sortByVModel = useVModel(props, 'sortBy', emit)
const sortingOrderVModel = useVModel(props, 'sortingOrder', emit)

const totalPages = computed(() => Math.ceil(props.pagination.total / props.pagination.perPage))

const { confirm } = useModal()

const onRoleDelete = async (role) => {
  const agreed = await confirm({
    title: t('adminRole.messages.deleteConfirmTitle'),
    message: t('adminRole.messages.deleteConfirmMessage', { name: role.name }),
    okText: t('adminRole.buttons.delete'),
    cancelText: t('adminRole.buttons.cancel'),
    size: 'small'
  })
  if (agreed) {
    emit('delete-role', role)
  }
}
const onRoleEdit = async (role) => {
  const res = await treeselect(role.id)
  emit('edit-role', {
    ...role,
    menus: res.data.checkedKeys
  })
}

/**
 * 格式化时间戳
 * @param {number} timestamp - 时间戳
 * @returns {string} 格式化后的时间字符串
 */
const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  // 如果是秒级时间戳，需要乘以1000转换为毫秒级
  return new Date(timestamp * 1000).toLocaleString('zh-CN')
}
</script>

<template>
  <VaDataTable v-model:sort-by="sortByVModel" v-model:sorting-order="sortingOrderVModel" :items="roles"
    :columns="columns" :loading="loading" :per-page="pagination.perPage" :current-page="pagination.page">
    <template #cell(status)="{ rowData }">
      <VaBadge :text="rowData.status === 1 ? t('adminRole.status.enabled') : t('adminRole.status.disabled')" :color="rowData.status === 1 ? 'success' : 'danger'" />
    </template>
    <template #cell(createTime)="{ rowData }">
      {{ formatTime(rowData.createTime) }}
    </template>
    <template #cell(actions)="{ rowData }">
      <div class="flex gap-2 justify-start">
        <VaButton preset="primary" size="small" icon="mso-edit" :aria-label="t('adminRole.buttons.edit')" @click="onRoleEdit(rowData)" />
        <!-- <VaButton preset="primary" size="small" icon="mso-person" color="info" aria-label="分配用户"
          @click="$emit('assign-users', rowData)" /> -->
        <VaButton preset="primary" size="small" icon="mso-delete" color="danger" :aria-label="t('adminRole.buttons.delete')"
          @click="onRoleDelete(rowData)" />
      </div>
    </template>
  </VaDataTable>

  <div class="flex  md:flex-row gap-2 justify-end items-center py-2">
    <div>
      <b>{{ pagination.total }} {{ t('adminRole.table.results') }}</b>
      {{ t('adminRole.table.perPage') }}
      <VaSelect v-model="pagination.perPage" class="!w-20" :options="[10, 20, 50, 100]" />
    </div>
    <div v-if="totalPages > 1" class="flex">
      <VaPagination v-model="pagination.page" class="justify-center" :pages="totalPages" :visible-pages="5"
        :boundary-links="false" :direction-links="false" />
    </div>
  </div>
</template>

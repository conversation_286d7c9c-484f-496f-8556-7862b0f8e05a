<script setup>
import { computed, ref, watch } from 'vue'
import { useForm } from 'vuestic-ui'
import { validators } from '../../../utils'
import MenuTreeSelector from '../../menu/components/MenuTreeSelector.vue'
import { useI18n } from 'vue-i18n'

// 使用 i18n
const { t } = useI18n()

/**
 * 组件属性定义
 */
const props = defineProps({
  role: {
    type: Object,
    default: null,
  },
  saveButtonLabel: {
    type: String,
    default: '保存',
  },
  menuOptions: {
    type: Array,
    default: () => [],
  },
})

const defaultNewRole = {

  name: '',
  roleKey: '',
  roleSort: '0',
  status: '',
  remark: '',
  enable: 1,
  menuCheckStrictly: false,
  dataScope: '1',
  menuIds: [],
  permissions: [],
}

const newRole = ref({ ...defaultNewRole })
const showParentMenu = ref(false)
/**
 * 检查表单是否有未保存的更改
 */
const isFormHasUnsavedChanges = computed(() => {
  return Object.keys(newRole.value).some((key) => {
    if (key === 'menuIds' || key === 'permissions') {
      return false
    }
    return (
      newRole.value[key] !== (props.role ?? defaultNewRole)?.[key]
    )
  })
})

defineExpose({
  isFormHasUnsavedChanges,
})

watch(
  [() => props.role],
  () => {
    if (!props.role) {
      return
    }

    newRole.value = {
      ...props.role,
      name: props.role.name || '',
      roleKey: props.role.roleKey || '',
      roleSort: props.role.roleSort || '0',
      status: props.role.status || '1',
      remark: props.role.remark || '',
      enable: props.role.enable ?? 1,
    }
  },
  { immediate: true },
)

const form = useForm('edit-role-form')

const emit = defineEmits(['close', 'save'])

const onSave = () => {
  if (form.validate()) {
    emit('save', newRole.value)
  }
}
const changeMenu = (menuIds) => {
  console.log(menuIds);

  // 确保menuIds是数组形式
  newRole.value.menuIds = Array.isArray(menuIds) ? menuIds : [menuIds].filter(id => id !== 0)
}
// 验证规则
const nameRules = [validators.required]
const roleKeyRules = [
  validators.required,
  (v) => /^[a-zA-Z0-9_]+$/.test(v) || t('adminRole.validation.roleKeyFormat')
]
const roleSortRules = [
  (v) => !v || /^\d+$/.test(v) || t('adminRole.validation.roleSortNumber')
]
const toggleParentMenu = () => {
  showParentMenu.value = !showParentMenu.value
}
</script>

<template>
  <VaForm v-slot="{ isValid }" ref="edit-role-form" class="flex-col justify-start items-start gap-4 inline-flex w-full">
    <div class="self-stretch flex-col justify-start items-start gap-4 flex">

      <div class="flex gap-4 flex-col sm:flex-row w-full">
        <VaInput v-model="newRole.name" :label="t('adminRole.form.roleName')" class="w-full sm:w-1/2" :rules="nameRules" name="name"
          :placeholder="t('adminRole.form.roleNamePlaceholder')" />
        <VaInput v-model="newRole.roleKey" :label="t('adminRole.form.roleKey')" class="w-full sm:w-1/2" :rules="roleKeyRules" name="roleKey"
          :placeholder="t('adminRole.form.roleKeyPlaceholder')" />
      </div>

      <div class="flex gap-4 flex-col sm:flex-row w-full">
        <VaInput v-model="newRole.roleSort" :label="t('adminRole.form.roleSort')" class="w-full sm:w-1/2" :rules="roleSortRules" name="roleSort"
          type="number" :placeholder="t('adminRole.form.roleSortPlaceholder')" />
        <div class="flex items-end w-full h-[36px]">
          <VaCheckbox v-model="newRole.status" :true-value="1" :false-value="0" :label="t('adminRole.form.enableRole')" class="w-full"
            name="enable" />
        </div>
      </div>
      <MenuTreeSelector :menu-options="menuOptions" @update:modelValue="changeMenu" :model-value="newRole.menus"
        :expanded="false" selection-type="leaf" />
      <VaTextarea v-model="newRole.remark" :label="t('adminRole.form.remark')" class="w-full" name="remark" :placeholder="t('adminRole.form.remarkPlaceholder')" rows="3" />
      <div class="flex gap-2 flex-col-reverse items-stretch justify-end w-full sm:flex-row sm:items-center">
        <VaButton preset="secondary" color="secondary" @click="$emit('close')">{{ t('adminRole.buttons.cancel') }}</VaButton>
        <VaButton :disabled="!isValid" @click="onSave">{{ saveButtonLabel }}</VaButton>
      </div>
    </div>
  </VaForm>
</template>

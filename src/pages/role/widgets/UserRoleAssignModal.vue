<script setup>
import { ref, computed, watch } from 'vue'
import { useToast } from 'vuestic-ui'
import { listUser } from '../../../api/system/user'
import { batchAuthUserRole, batchCancelAuthUser } from '../../../api/role/role'
import { useI18n } from 'vue-i18n'

// 使用 i18n
const { t } = useI18n()

/**
 * 组件属性定义
 */
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  selectedRole: {
    type: Object,
    default: null,
  },
})

/**
 * 组件事件定义
 */
const emit = defineEmits(['close', 'success'])

const { init: notify } = useToast()

// ==================== 响应式状态 ====================

const loading = ref(false)
const users = ref([])
const selectedUsers = ref([])
const usersTotal = ref(0)

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  account: '',
  enable: '',
})

// 表格列定义
const columns = [
  { key: 'account', label: '账号', sortable: true },
  { key: 'name', label: '姓名', sortable: true },
  { key: 'userType', label: '用户类型', sortable: false },
  { key: 'enable', label: '启用状态', sortable: false },
  { key: 'admin', label: '管理员', sortable: false },
  { key: 'creator', label: '创建者', sortable: false },
  { key: 'createTime', label: '创建时间', sortable: true },
]

// ==================== 计算属性 ====================

const totalPages = computed(() =>
  Math.ceil(usersTotal.value / queryParams.value.pageSize)
)

const hasSelectedUsers = computed(() => selectedUsers.value.length > 0)

// ==================== 业务方法 ====================

/**
 * 格式化时间戳
 * @param {number} timestamp - 时间戳
 * @returns {string} 格式化后的时间字符串
 */
const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  return new Date(timestamp * 1000).toLocaleString('zh-CN')
}

/**
 * 格式化用户类型
 * @param {string} userType - 用户类型代码
 * @returns {string} 用户类型描述
 */
const formatUserType = (userType) => {
  const typeMap = {
    '00': t('adminRole.userAssign.userType.system'),
    '01': t('adminRole.userAssign.userType.normal'),
  }
  return typeMap[userType] || t('adminRole.userAssign.userType.unknown')
}

/**
 * 加载用户列表
 */
const loadUsers = async () => {
  if (!props.visible) return

  loading.value = true
  try {
    const response = await listUser(queryParams.value)

    if (response.code === 200) {
      users.value = response.data.pageList || []
      usersTotal.value = response.data.total || 0
    } else {
      notify({
        message: response.msg || '获取用户列表失败',
        color: 'danger',
      })
    }
  } catch (error) {
    notify({
      message: '获取用户列表失败',
      color: 'danger',
    })
  } finally {
    loading.value = false
  }
}

/**
 * 搜索用户
 */
const searchUsers = () => {
  queryParams.value.pageNum = 1
  loadUsers()
}

/**
 * 重置搜索条件
 */
const resetSearch = () => {
  queryParams.value.account = ''
  queryParams.value.enable = ''
  queryParams.value.pageNum = 1
  loadUsers()
}

/**
 * 分配角色给选中的用户
 */
const assignRole = async () => {
  if (!hasSelectedUsers.value) {
    notify({
      message: t('adminRole.userAssign.messages.selectUsers'),
      color: 'warning',
    })
    return
  }

  if (!props.selectedRole?.id) {
    notify({
      message: t('adminRole.userAssign.messages.invalidRole'),
      color: 'danger',
    })
    return
  }

  try {
    loading.value = true

    // 提取用户ID列表
    const userIds = selectedUsers.value.map(user => user.id)
    await batchAuthUserRole(props.selectedRole.id, userIds)

    notify({
      message: t('adminRole.userAssign.messages.assignSuccess', { count: selectedUsers.value.length, name: props.selectedRole.name }),
      color: 'success',
    })

    selectedUsers.value = []
    await loadUsers()
    emit('success')

  } catch (error) {
    notify({
      message: t('adminRole.userAssign.messages.assignFailed'),
      color: 'danger',
    })
  } finally {
    loading.value = false
  }
}

/**
 * 取消选中用户的角色授权
 */
const cancelAuth = async () => {
  if (!hasSelectedUsers.value) {
    notify({
      message: t('adminRole.userAssign.messages.selectUsersForCancel'),
      color: 'warning',
    })
    return
  }

  try {
    loading.value = true

    // 提取用户ID列表
    const userIds = selectedUsers.value.map(user => user.id)
    await batchCancelAuthUser(userIds)

    notify({
      message: t('adminRole.userAssign.messages.cancelSuccess', { count: selectedUsers.value.length }),
      color: 'success',
    })

    selectedUsers.value = []
    await loadUsers()
    emit('success')

  } catch (error) {
    notify({
      message: t('adminRole.userAssign.messages.cancelFailed'),
      color: 'danger',
    })
  } finally {
    loading.value = false
  }
}

/**
 * 关闭模态框
 */
const handleClose = () => {
  selectedUsers.value = []
  queryParams.value.account = ''
  queryParams.value.enable = ''
  queryParams.value.pageNum = 1
  emit('close')
}

// ==================== 监听器 ====================

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    loadUsers()
  }
}, { immediate: true })

// 监听分页变化
watch(() => queryParams.value.pageNum, () => {
  loadUsers()
})
</script>

<template>
  <div v-if="selectedRole" class="user-role-assign-modal">
    <h1 class="va-h5 mb-4">{{ t('adminRole.userAssign.titleWithName', { name: selectedRole.name }) }}</h1>

    <!-- 搜索区域 -->
    <div class="flex gap-2 mb-4">
      <VaInput v-model="queryParams.account" :placeholder="t('adminRole.userAssign.searchAccount')" class="flex-1" @keyup.enter="searchUsers">
        <template #prependInner>
          <VaIcon name="search" color="secondary" size="small" />
        </template>
      </VaInput>

      <VaSelect v-model="queryParams.enable" :placeholder="t('adminRole.userAssign.enableStatus')" class="w-32" :options="[
        { text: t('adminRole.status.enabled'), value: '1' },
        { text: t('adminRole.status.disabled'), value: '0' },
      ]" text-by="text" value-by="value" />

      <VaButton color="primary" @click="searchUsers">{{ t('adminRole.buttons.search') }}</VaButton>
      <VaButton color="secondary" @click="resetSearch">{{ t('adminRole.buttons.reset') }}</VaButton>
    </div>

    <!-- 操作按钮区域 -->
    <div class="flex gap-2 mb-4">
      <VaButton color="success" :disabled="!hasSelectedUsers || loading" @click="assignRole">
        {{ t('adminRole.userAssign.assignRole') }} ({{ selectedUsers.length }})
      </VaButton>

      <VaButton color="danger" :disabled="!hasSelectedUsers || loading" @click="cancelAuth">
        {{ t('adminRole.userAssign.cancelAuth') }} ({{ selectedUsers.length }})
      </VaButton>
    </div>

    <!-- 用户列表表格 -->
    <VaDataTable v-model="selectedUsers" :items="users" :columns="columns" :loading="loading" selectable
      select-mode="multiple" track-by="id">
      <template #cell(userType)="{ rowData }">
        <VaBadge :text="formatUserType(rowData.userType)"
          :color="rowData.userType === '00' ? 'primary' : 'secondary'" />
      </template>

      <template #cell(enable)="{ rowData }">
        <VaBadge :text="rowData.enable === 1 ? t('adminRole.status.enabled') : t('adminRole.status.disabled')" :color="rowData.enable === 1 ? 'success' : 'danger'" />
      </template>

      <template #cell(admin)="{ rowData }">
        <VaBadge :text="rowData.admin ? t('adminRole.userAssign.admin.yes') : t('adminRole.userAssign.admin.no')" :color="rowData.admin ? 'warning' : 'info'" />
      </template>

      <template #cell(createTime)="{ rowData }">
        {{ formatTime(rowData.createTime) }}
      </template>
    </VaDataTable>

    <!-- 分页 -->
    <div class="flex justify-end items-center mt-4">
      <div>
        <b>{{ usersTotal }} {{ t('adminRole.table.results') }}</b>
        {{ t('adminRole.table.perPage') }}
        <VaSelect v-model="queryParams.pageSize" class="!w-20 inline-block" :options="[10, 20, 50, 100]"
          @update:model-value="loadUsers" />
      </div>
      <VaPagination v-if="totalPages > 1" v-model="queryParams.pageNum" :pages="totalPages" />
    </div>

    <!-- 底部按钮 -->
    <div class="flex gap-2 justify-end mt-6">
      <VaButton preset="secondary" @click="handleClose">{{ t('adminRole.userAssign.close') }}</VaButton>
    </div>
  </div>
</template>

<style scoped>
.user-role-assign-modal {
  min-height: 500px;
}
</style>

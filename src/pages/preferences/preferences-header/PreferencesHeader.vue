<template>
  <div class="user-info">
    <div
      style="width: 60px;height: 60px;font-size: 24px;border-radius: 50%;background: linear-gradient(45deg, var(--va-primary) 0%, #FAD0C4 100%);display: flex;align-items: center;justify-content: center;color: white;font-weight: bold;">
      {{ store.userName.charAt(0) }}</div>

  </div>
  <div class="flex flex-col justify-center">
    <h2 class="text-[28px] md:text-[32px] leading-10 font-bold">{{ store.userName }}</h2>
    <div class="flex space-x-1 text-[13px] leading-4">
      <p>{{ $t('user.memberSince') }}</p>
      <p>{{ store.memberSince }}</p>
    </div>
  </div>
</template>
<script setup>
import { useUserStore } from '../../../stores/user-store'

const store = useUserStore()
</script>

<template>
  <VaButtonToggle v-model="theme" color="background-element" border-color="background-element" :options="options" />
</template>
<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useColors } from 'vuestic-ui'
const { applyPreset, currentPresetName } = useColors()
import { useUserStore } from '../../../stores/user-store'

const userStore = useUserStore()
const { t, locale } = useI18n()

const theme = computed({
  get() {
    return currentPresetName.value
  },
  set(value) {
    applyPreset(value)
    userStore.setTheme(value)
  },
})

// 将options改为计算属性，这样它会随着locale的变化而自动更新
const options = computed(() => [
  { label: t('buttonSelect.dark'), value: 'dark' },
  { label: t('buttonSelect.light'), value: 'light' },
])
</script>

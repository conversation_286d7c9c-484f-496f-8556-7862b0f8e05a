<template>
  <VaModal v-model="isOpen" :title="modalTitle" size="small" :hide-default-actions="true"
    :max-width="store.is2FAEnabled ? '380px' : '530px'" close-button>
    <SubLoading :loading="loading">
      <div class="qrcode-container">
        <template v-if="!store.is2FAEnabled">
          <QrcodeVue v-if="qrCode" :value="qrCode" :size="200" level="H"></QrcodeVue>
          <p class="qrcode-tip">{{ $t('preferences.scanQRCodeToBindGoogleAuthenticator') }}</p>
          <VaInput v-model="authName" :disabled="true" class="mt-4 w-full" />
          <VaInput v-model="authSecret" :disabled="true" class="mt-2 w-full" />
        </template>
        <template v-else>
          <div class="text-center mb-4">
            <VaIcon name="security" size="large" color="primary" />
            <p class="mt-2">{{ $t('preferences.confirmDisableTwoFactor') }}</p>
          </div>
        </template>
        <VaInput v-model="code" :placeholder="$t('preferences.enterVerificationCode')" class="mt-2 w-full" />
      </div>
    </SubLoading>
    <template #footer>
      <VaButton preset="secondary" :disabled="loading" @click="closeModal">{{ $t('preferences.cancel') }} </VaButton>
      <VaButton v-if="!store.is2FAEnabled" preset="secondary" :disabled="loading" @click="copyAuthSecret">{{
        $t('preferences.copySecret')
      }}</VaButton>
      <VaButton preset="primary" :color="store.is2FAEnabled ? 'danger' : 'primary'" :disabled="!code || loading"
        @click="store.is2FAEnabled ? disableTwoFa() : bindTwoFa()">
        {{ store.is2FAEnabled ? $t('preferences.disable') : $t('preferences.bind') }}
      </VaButton>
    </template>
  </VaModal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useToast } from 'vuestic-ui'
import QrcodeVue from 'qrcode.vue'
import { getTwoFaAuth, setTwoFaAuth, closeTwoFa } from '../../../api/auth/index.js'
import { useUserStore } from '../../../stores/user-store.js'
import SubLoading from '../../../components/SubLoading.vue'

const { t } = useI18n()
const { init: showToast } = useToast()
const store = useUserStore()

const isOpen = defineModel('modelValue', { default: false })
const emits = defineEmits(['update:modelValue', 'success'])

const qrCode = ref('')
const authName = ref('')
const authSecret = ref('')
const code = ref('')
const loading = ref(false)
const modalTitle = computed(() => {
  return store.is2FAEnabled
    ? t('preferences.disableTwoFactorAuthentication')
    : t('preferences.enableTwoFactorAuthentication')
})

const closeModal = () => {
  isOpen.value = false
  resetForm()
}

const resetForm = () => {
  qrCode.value = ''
  authName.value = ''
  authSecret.value = ''
  code.value = ''
}

const copyAuthSecret = () => {
  if (!authSecret.value || !authName.value) {
    showToast({ message: t('preferences.copyFailed'), color: 'danger' })
    return
  }

  const textToCopy = authSecret.value + '\n' + authName.value
  navigator.clipboard
    .writeText(textToCopy)
    .then(() => {
      showToast({ message: t('preferences.copySuccess'), color: 'success' })
    })
    .catch(() => {
      showToast({ message: t('preferences.copyFailed'), color: 'danger' })
    })
}

const getTwoFa = async () => {
  try {
    loading.value = true
    const response = await getTwoFaAuth()
    qrCode.value = response.data.content
    authName.value = response.data.content.split('//')[1].split('/')[1].split('?')[0]
    authSecret.value = response.data.content.split('=')[1]
    loading.value = false
  } catch (error) {
    // 这里不做提示，因为通用请求都做完了
    loading.value = false

  }
}

const bindTwoFa = async () => {
  try {
    await setTwoFaAuth({ code: code.value })
    store.openTwoFa()
    showToast({ message: t('preferences.twoFactorAuthenticationEnabledToast'), color: 'success' })
    emits('success')
    closeModal()
  } catch (error) {

  }
}

const disableTwoFa = async () => {
  try {
    await closeTwoFa({ userName: store.account })
    store.closeTwoFa() // Update the store state
    showToast({ message: t('preferences.twoFactorAuthenticationDisabledToast'), color: 'success' })
    emits('success')
    closeModal()
  } catch (error) {
    showToast({ message: t('preferences.disableTwoFaFailed'), color: 'danger' })
  }
}

// Watch for modal opening to fetch 2FA data if needed
const handleModalOpen = () => {
  if (isOpen.value && !store.is2FAEnabled) {
    getTwoFa()
  }
}

// Watch for changes in isOpen
watch(isOpen, (newVal) => {
  if (newVal) {
    handleModalOpen()
  }
})
</script>

<style scoped>
.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.qrcode-tip {
  margin-top: 15px;
  color: var(--va-text-secondary);
}
</style>

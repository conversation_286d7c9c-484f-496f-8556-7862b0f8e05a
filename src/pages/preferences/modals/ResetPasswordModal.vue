<template>
  <VaModal max-width="530px" :mobile-fullscreen="false" hide-default-actions model-value close-button
    @update:modelValue="emits('cancel')">
    <h1 class="va-h5 mb-4">{{ $t('preferences.resetPassword') }}</h1>
    <VaForm ref="form" class="space-y-6" @submit.prevent="submit">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <VaInput v-model="oldPassowrd"
          :rules="[(v) => { v?.length >= 0 || $t('preferences.passwordRules.oldPasswordRequired') }]"
          :label="$t('preferences.oldPassword')" :placeholder="$t('preferences.oldPassword')" required-mark
          type="password" />
        <VaInput v-model="newPassword" :rules="newPasswordRules" :label="$t('preferences.newPassword')"
          :placeholder="$t('preferences.newPassword')" required-mark type="password" />
        <VaInput v-model="repeatNewPassword" :rules="repeatNewPasswordRules"
          :label="$t('preferences.repeatNewPassword')" :placeholder="$t('preferences.repeatNewPassword')" required-mark
          type="password" />
      </div>
      <div class="flex flex-col space-y-2">
        <div class="flex space-x-2 items-center">
          <div>
            <VaIcon :name="newPassword?.length >= 6 ? 'mso-check' : 'mso-close'" color="secondary" size="20px" />
          </div>
          <p>{{ $t('preferences.passwordRules.atLeast8Chars') }}</p>
        </div>
      </div>
      <div class="flex flex-col-reverse md:justify-end md:flex-row md:space-x-4">
        <VaButton :style="buttonStyles" preset="secondary" color="secondary" @click="emits('cancel')">{{
          $t('preferences.cancel') }}</VaButton>
        <VaButton :style="buttonStyles" class="mb-4 md:mb-0" type="submit" @click="submit">{{
          $t('preferences.updatePassword') }}</VaButton>
      </div>
    </VaForm>
  </VaModal>
</template>
<script setup>
import { ref } from 'vue'
import { useForm } from 'vuestic-ui'
import { useI18n } from 'vue-i18n'
import { buttonStyles } from '../styles.js'

const { t } = useI18n()
const oldPassowrd = ref('')
const newPassword = ref()
const repeatNewPassword = ref()

const { validate } = useForm('form')

const emits = defineEmits(['cancel', 'submit'])

const submit = () => {
  if (validate()) {
    emits('submit', {
      newPassword: newPassword.value,
      oldPassword: oldPassowrd.value,
    })
  }
}

const newPasswordRules = [
  (v) => !!v || t('preferences.passwordRules.newPasswordRequired'),
  (v) => v?.length >= 6 || t('preferences.passwordRules.atLeast8Chars'),
  (v) => new Set(v).size >= 6 || t('preferences.passwordRules.atLeast6UniqueChars'),
  (v) => v !== oldPassowrd.value || t('preferences.passwordRules.newPasswordNotSame'),
]

const repeatNewPasswordRules = [
  (v) => !!v || t('preferences.passwordRules.repeatPasswordRequired'),
  (v) => v === newPassword.value || t('preferences.passwordRules.passwordsDoNotMatch'),
]
</script>

<style lang="scss">
// TODO temporary before https://github.com/epicmaxco/vuestic-ui/issues/4020 fix
.va-modal__inner {
  min-width: 326px;
}
</style>

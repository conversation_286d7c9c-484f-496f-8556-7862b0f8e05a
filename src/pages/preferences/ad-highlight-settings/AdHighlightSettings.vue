<template>
  <div class="ad-highlight-settings">
    <!-- 广告高亮设置标题 -->
    <div class="settings-header">
      <div class="header-content">
        <div class="header-icon">
          <VaIcon name="mso-format_color_fill" color="primary" size="large" />
        </div>
        <div class="header-text">
          <h2 class="header-title">
            {{ $t('preferences.adHighlight.title') }}
            <VaPopover :message="$t('preferences.adHighlight.tooltip')" placement="top" class="ml-auto">
              <VaIcon name="mso-help_outline" size="16px" class="text-gray-400 cursor-help" />
            </VaPopover>
          </h2>
          <p class="header-subtitle">{{ $t('preferences.adHighlight.subtitle') }}</p>
        </div>
      </div>
    </div>

    <!-- 设置卡片 -->
    <div class="settings-grid">
      <VaForm ref="form" tag="form">
        <VaCard class="setting-card">
          <VaCardContent class="card-content">
            <!-- 设置项列表 -->
            <div class="settings-list">
              <div v-for="field in fieldConfigs" :key="field.key" class="setting-item p-2">
                <div class="setting-info">
                  <div class="setting-icon">
                    <VaIcon :name="getFieldIcon(field.key)" color="info" size="medium" />
                  </div>
                  <div class="setting-text">
                    <h4 class="setting-title">{{ field.label }}</h4>
                    <p class="setting-description">{{ field.description }}</p>
                  </div>
                </div>

                <div class="setting-controls">
                  <!-- 阈值设置 -->
                  <div class="control-item">
                    <div class="threshold-input-group">
                      <div class="label-input">
                        <label class="primary-label">{{ $t('preferences.adHighlight.threshold') + '(%)' }}</label>
                        <VaInput v-model="thresholds[field.key]" :rules="[thresholdValidator]"
                          type="number" step="0.1" min="0" max="100" class="threshold-input" />
                      </div>
                      <div class="label-input">
                        <label class="primary-label">{{ $t('preferences.adHighlight.color') }}</label>
                        <VaColorInput v-model="colors[field.key]" mode="input" class="color-input" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </VaCardContent>
        </VaCard>
      </VaForm>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <VaButton preset="secondary" border-color="primary" @click="resetToDefault">
        {{ $t('preferences.adHighlight.actions.reset') }}
      </VaButton>
      <VaButton color="primary" :loading="isSaving" @click="saveSettings">
        {{ $t('preferences.adHighlight.actions.save') }}
      </VaButton>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useAdHighlight } from '@/composables/useAdHighlight'
import { useToast, useForm } from 'vuestic-ui'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const { init: toast } = useToast()
const { validate } = useForm('form')
const emit = defineEmits(['refresh'])

// 使用广告高亮Hook
const {
  config,
  configLoaded,
  getFieldConfigs,
  updateFieldConfig,
  saveConfig,
  resetConfig
} = useAdHighlight()

// 表单数据状态
const thresholds = reactive({})
const colors = reactive({})
const isSaving = ref(false)

// 字段配置列表
const fieldConfigs = ref([])

// 初始化字段配置
const initFieldConfigs = () => {
  fieldConfigs.value = getFieldConfigs()
  
  // 初始化阈值和颜色
  fieldConfigs.value.forEach(field => {
    thresholds[field.key] = field.threshold
    colors[field.key] = field.color
  })
}

// 获取字段图标
const getFieldIcon = (key) => {
  const iconMap = {
    'clickRate': 'mso-touch_app',
    'impressionRate': 'mso-visibility',
    'matchRate': 'mso-link',
    'activeViewRate': 'mso-remove_red_eye'
  }
  return iconMap[key] || 'mso-settings'
}

// 阈值验证器
const thresholdValidator = (v) => {
  const num = parseFloat(v)
  return (num >= 0 && num <= 100) || t('preferences.adHighlight.validation.thresholdRange')
}

// 保存设置
const saveSettings = async () => {
  if (validate()) {
    isSaving.value = true
    try {
      // 更新配置
      Object.keys(thresholds).forEach(key => {
        updateFieldConfig(key, thresholds[key], colors[key])
      })
      
      // 保存到本地存储
      const result = saveConfig()
      
      if (result) {
        toast({
          color: 'success',
          message: t('preferences.adHighlight.messages.settingsSaved')
        })
        emit('refresh')
      } else {
        toast({
          color: 'danger',
          message: t('preferences.adHighlight.messages.settingsSaveFailed')
        })
      }
    } catch (error) {
      console.error('保存广告高亮设置失败:', error)
      toast({
        color: 'danger',
        message: t('preferences.adHighlight.messages.settingsSaveFailed')
      })
    } finally {
      isSaving.value = false
    }
  }
}

// 重置为默认设置
const resetToDefault = () => {
  resetConfig()
  initFieldConfigs()
  toast({
    color: 'info',
    message: t('preferences.adHighlight.messages.resetSuccess')
  })
}

// 组件挂载时初始化
onMounted(() => {
  // 等待配置加载完成
  const checkConfigLoaded = () => {
    if (configLoaded.value) {
      initFieldConfigs()
    } else {
      setTimeout(checkConfigLoaded, 100)
    }
  }
  checkConfigLoaded()
})
</script>

<style scoped>
/* 主容器 */
.ad-highlight-settings {
  max-width: 1280px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.header-content {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 1rem;
  border: 1px solid var(--va-background-border);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background: var(--va-background-secondary);
}

.header-icon {
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  background: rgba(var(--va-primary-rgb), 0.1);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-text {
  flex: 1;
}

.header-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--va-text-primary);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-subtitle {
  color: var(--va-text-primary);
  opacity: 0.7;
  font-size: 1rem;
}

/* 设置网格 */
.settings-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 设置卡片 */
.setting-card {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: none;
  overflow: hidden;
  transition: all 0.3s ease;
  --va-card-border-radius: 1rem;
  margin-bottom: 1rem;
}

.setting-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-2px);
}

/* 卡片内容 */
.card-content {
  padding: 1.5rem;
}

/* 设置项列表 */
.settings-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  border-radius: 0.75rem;
  border: 1px solid var(--va-background-border);
  transition: all 0.2s ease;
  padding: 1rem;
}

.setting-item:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.setting-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
  min-width: 0;
}

.setting-icon {
  flex-shrink: 0;
  background: rgba(var(--va-info-rgb), 0.1);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
}

.setting-text {
  flex: 1;
  min-width: 0;
}

.setting-title {
  font-weight: 600;
  color: var(--va-text-primary);
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.setting-description {
  font-size: 0.875rem;
  color: var(--va-text-primary);
  opacity: 0.7;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-clamp: 2;
}

/* 设置控制区域 */
.setting-controls {
  display: flex;
  gap: 0.75rem;
  flex: 1;
}

.control-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
  background: var(--va-background-primary);
  border-radius: 0.5rem;
  min-width: 0;
}

.threshold-input-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
}

/* 标签和输入框组 */
.label-input {
  display: flex;
  gap: 0.75rem;
  width: 100%;
  align-items: center;
}

.primary-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--va-text-primary);
  white-space: nowrap;
  min-width: 80px;
}

.threshold-input, .color-input {
  --va-input-wrapper-border-radius: 0.5rem;
  flex: 1;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

/* 响应式优化 */
@media (min-width: 640px) {
  .setting-item {
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
  }

  .setting-controls {
    flex-direction: row;
    gap: 1rem;
  }

  .threshold-input-group {
    flex-direction: row;
    align-items: center;
    gap: 1rem;
  }
}

/* 平板样式 */
@media (min-width: 640px) and (max-width: 1023px) {
  .setting-controls {
    max-width: 70%;
  }
  
  .threshold-input-group {
    flex-direction: column;
  }
}

/* 手机端样式 */
@media (max-width: 639px) {
  .ad-highlight-settings {
    gap: 1rem;
  }

  .header-content {
    padding: 1rem;
  }

  .header-title {
    font-size: 1.25rem;
  }

  .card-content {
    padding: 1rem;
  }

  .setting-item {
    padding: 1rem;
  }

  .control-item {
    padding: 0.5rem;
  }
  
  .label-input {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .primary-label {
    margin-bottom: 0.5rem;
  }
  
  .threshold-input, .color-input {
    width: 100%;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}

/* 小屏幕手机样式 */
@media (max-width: 480px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .setting-info {
    gap: 0.75rem;
  }

  .setting-controls {
    gap: 0.5rem;
    flex-direction: column;
    width: 100%;
  }
}

/* VuesticUI 组件自定义 */
.va-input {
  --va-input-wrapper-border-radius: 0.5rem;
}

.va-card {
  --va-card-border-radius: 1rem;
}
</style> 
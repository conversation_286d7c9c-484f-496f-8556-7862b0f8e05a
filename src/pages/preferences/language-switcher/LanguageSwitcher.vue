<template>
  <div class="language-switcher">
    <div class="flex items-center justify-between">
      <div class="w-40">
        <VaSelect v-model="model" :options="options">
          <template #appendInner>
            <VaIcon name="mso-language" />
          </template>
        </VaSelect>
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed } from 'vue'
import { useUserStore } from '../../../stores/user-store.js'
import { useI18n } from 'vue-i18n'

const { locale } = useI18n()
const languages = {
  english: 'English',
  simplified_chinese: '简体中文',
}
const languageCodes = {
  gb: languages.english,
  cn: languages.simplified_chinese,
}
const languageName = Object.fromEntries(Object.entries(languageCodes).map(([key, value]) => [value, key]))
const options = Object.values(languageCodes)
const userStore = useUserStore()
const model = computed({
  get() {
    return languageCodes[locale.value]
  },
  set(value) {
    const langCode = languageName[value]
    userStore.setLanguage(langCode)
    locale.value = langCode
  },
})
</script>

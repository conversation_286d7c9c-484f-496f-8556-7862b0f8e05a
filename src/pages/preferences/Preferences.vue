<template>
  <div class="flex flex-col space-y-10 bg-backgroundSecondary rounded-lg max-w-[1280px] mx-auto">
    <div class="flex space-x-5  max-w-[1280px]">
      <PreferencesHeader />
    </div>
    <div class="space-y-4 md:space-y-6">
      <Settings @openNameModal="isEditNameModalOpen = true" @openResetPasswordModal="isResetPasswordModalOpen = true" />
    </div>
  </div>
  <EditNameModal v-if="isEditNameModalOpen" @cancel="isEditNameModalOpen = false" />
  <ResetPasswordModal v-if="isResetPasswordModalOpen" @cancel="isResetPasswordModalOpen = false" @submit="submit" />
</template>
<script setup>
import { ref } from 'vue'
import { updateUserPwd } from '@/api/auth'
import PreferencesHeader from './preferences-header/PreferencesHeader.vue'
import Settings from './settings/Settings.vue'
import EditNameModal from './modals/EditNameModal.vue'
import ResetPasswordModal from './modals/ResetPasswordModal.vue'
import { useToast } from 'vuestic-ui'
import { useI18n } from 'vue-i18n'

const { init } = useToast()
const { t } = useI18n()

const isEditNameModalOpen = ref(false)
const isResetPasswordModalOpen = ref(false)

const submit = async (pwdObj) => {
  let res = await updateUserPwd({
    oldPassword: pwdObj.oldPassword,
    password: pwdObj.newPassword,
  })
  if (res.code === 200) {
    isResetPasswordModalOpen.value = false
    init({ message: t('preferences.passwordChangedSuccess'), color: 'success' })
  }
}
</script>

<template>
  <VaButtonToggle v-model="enableNavigation" color="background-element" border-color="background-element"
    :options="options" />
</template>
<script setup>
import { useI18n } from 'vue-i18n'
import { computed } from 'vue'
import { useUserStore } from '../../../stores/user-store'
const userStore = useUserStore()
const { t } = useI18n()
const options = [
  { label: t('preferences.breadcrumbOption1'), value: true },
  { label: t('preferences.breadcrumbOption2'), value: false },
]
const enableNavigation = computed({
  get() {
    return userStore.enableNavigation
  },
  set(value) {
    userStore.setEnableNavigation(value)
  },
})
</script>

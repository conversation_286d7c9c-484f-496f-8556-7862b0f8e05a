<template>
  <div class="notification-settings">
    <!-- 广告通知设置标题 -->
    <div class="settings-header">
      <div class="header-content">
        <div class="header-icon">
          <VaIcon name="mso-notifications" color="primary" size="large" />
        </div>
        <div class="header-text">
          <h2 class="header-title">{{ $t('preferences.notifications.title') }}
            <!-- NEW: Tooltip Icon -->
            <!-- <VaPopover :message="$t('preferences.notifications.tooltip')" placement="top" class="ml-auto">
              <VaIcon name="mso-help_outline" size="16px" class="text-gray-400 cursor-help" />
            </VaPopover> -->
          </h2>
          <p class="header-subtitle">{{ $t('preferences.notifications.subtitle') }}</p>
        </div>
      </div>
    </div>

    <!-- 通知设置卡片 -->
    <div class="settings-grid">
      <VaForm ref="form" tag="form">
        <VaCard class="setting-card">
          <VaCardContent class="card-content">
            <!-- 设置项列表 -->
            <div class="settings-list">
              <div v-for="setting in store.userSetting" :key="setting.id" class="setting-item p-2">
                <div class="setting-info">
                  <div class="setting-icon">
                    <VaIcon :name="getSettingIcon(setting.key)" color="info" size="medium" />
                  </div>
                  <div class="setting-text">
                    <h4 class="setting-title">{{ getSettingTitle(setting.key) }}</h4>
                    <p class="setting-description">{{ getSettingDescription(setting.key, setting.value) }}</p>
                  </div>
                </div>

                <div class="setting-controls">
                  <!-- 阈值设置 -->
                  <div class="control-item">
                    <div class="threshold-input-group">
                      <div class="label-input">
                        <label class="primary-label">{{ $t(`preferences.notifications.threshold`) + '(%)' }}</label>
                        <VaInput :model-value="getDisplayValue(setting.value)"
                          @update:model-value="updateSettingValue(setting, $event)" :rules="[thresholdValidator]"
                          type="number" step="1" min="0" max="100" class="threshold-input" />
                      </div>
                      <div class="save-button-container">
                        <VaButton preset="primary" size="small" icon="mso-save"
                          :loading="settingStates[setting.id]?.isLoading" @click="saveSingleSetting(setting)">
                          {{ $t('preferences.notifications.actions.save') }}
                        </VaButton>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </VaCardContent>
        </VaCard>
      </VaForm>
    </div>

    <!-- 空状态 -->
    <VaCard v-if="!store.userSetting || store.userSetting.length === 0" class="empty-state">
      <div class="empty-state-content">
        <div class="empty-state-icon">
          <VaIcon name="mso-notifications_off" size="large" color="secondary" />
        </div>
        <h3 class="empty-state-title">{{ $t('preferences.notifications.emptyStateTitle') }}</h3>
        <p class="empty-state-description">{{ $t('preferences.notifications.emptyStateDescription') }}</p>
      </div>
    </VaCard>
  </div>
</template>

<script setup>
import { computed, onMounted, ref, reactive, defineEmits } from 'vue'
import { useUserStore } from '../../../stores/user-store.js'
import { updateUserSetting } from '@/api/auth/index.js'
import { useToast, useForm } from 'vuestic-ui'
import { useI18n } from 'vue-i18n'
const store = useUserStore()
const { init: toast } = useToast()
const { t } = useI18n()
const { validate } = useForm('form')
const emit = defineEmits(['refresh'])

// 表单数据状态
const formData = reactive({})
const isLoading = ref(false)
// 跟踪每个设置项的编辑状态和加载状态
const settingStates = reactive({})

// 初始化设置项状态
const initSettingStates = () => {
  if (!store.userSetting || store.userSetting.length === 0) return

  store.userSetting.forEach(setting => {
    settingStates[setting.id] = {
      isEditing: false,
      isLoading: false,
      originalValue: setting.value
    }
  })
}

// 自定义验证器
const thresholdValidator = (v) => {
  const num = parseFloat(v)
  return (num >= 0 && num <= 100) || t('preferences.notifications.validation.thresholdRange')
}

const daysValidator = (v) => {
  const num = parseInt(v)
  return (num >= 1 && num <= 30) || t('preferences.notifications.validation.daysRange')
}

// 如果没有用户设置数据，使用测试数据
onMounted(() => {
  if (!store.userSetting || store.userSetting.length === 0) {
    return;
  }
  // 初始化设置项状态
  initSettingStates()
})

// 获取设置图标
const getSettingIcon = (key) => {
  const iconMap = {
    'adsRequest': 'mso-trending_down',
    'revenue': 'mso-attach_money',
    'match': 'mso-link',
    'matchRate': 'mso-link',
    'clickRate': 'mso-touch_app'
  }
  return iconMap[key] || 'mso-settings'
}

// 获取设置标题
const getSettingTitle = (key) => {
    return t(`preferences.notifications.settings.${key}`)
}

// 获取设置描述
const getSettingDescription = (key, value) => {
  return (key.includes('rate') || key.includes('Rate'))
    ? t(`preferences.notifications.descriptions.rate`, { threshold: getDisplayValue(value) })
    : t(`preferences.notifications.descriptions.adsRequest`, { threshold: getDisplayValue(value) })
}

// 获取显示值（小数转百分比）
const getDisplayValue = (value) => {
  if (value === undefined || value === null || value === '') {
    return 0
  }
  const num = parseFloat(value)
  // 如果值已经是百分比形式（大于等于1），则直接返回
  if (num >= 1 && num <= 100) {
    return Math.round(num)
  }
  // 否则将小数转换为百分比
  return Math.round(num * 100)
}

// 获取提交值（百分比转小数）
const getSubmitValue = (value) => {
  if (value === undefined || value === null || value === '') {
    return '0.00'
  }
  const num = parseFloat(value)
  // 如果值已经是小数形式（小于1），则直接返回格式化后的值
  if (num < 1 && num >= 0) {
    return num.toFixed(2)
  }
  // 否则将百分比转换为小数
  return (num / 100).toFixed(2)
}

// 更新设置值
const updateSettingValue = (setting, value) => {
  const index = store.userSetting.findIndex(s => s.id === setting.id)
  if (index !== -1) {
    // 直接保存百分比值，在提交时再转换为小数
    store.userSetting[index].value = value
    // 如果设置项正在编辑中，更新其值
    if (settingStates[setting.id]?.isEditing) {
      settingStates[setting.id].value = value
    }
  }
}

// 保存单个设置项
const saveSingleSetting = async (setting) => {
  if (validate()) {
    settingStates[setting.id].isLoading = true
    try {
      const submitData = {
        ...setting,
        value: getSubmitValue(setting.value)
      }
      const res = await updateUserSetting(submitData)
      if (res.code === 200) {
        settingStates[setting.id].isEditing = false
        settingStates[setting.id].isLoading = false
        settingStates[setting.id].originalValue = settingStates[setting.id].value
        toast({
          color: 'success',
          message: t('preferences.notifications.messages.settingSaved')
        })
        emit('refresh')
      } else {
        toast({
          color: 'danger',
          message: t('preferences.notifications.messages.settingSaveFailed')
        })
      }
    } catch (error) {
      console.error('保存设置失败:', error)
      toast({
        color: 'danger',
        message: t('preferences.notifications.messages.settingSaveFailed')
      })
    } finally {
      settingStates[setting.id].isLoading = false
    }
  }
}
</script>

<style scoped>
/* 主容器 */
.notification-settings {
  max-width: 1280px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.header-content {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 1rem;
  border: 1px solid var(--va-background-border);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background: var(--va-background-secondary);
}

.header-icon {
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  background: rgba(var(--va-primary-rgb), 0.1);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-text {
  flex: 1;
}

.header-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--va-text-primary);
  margin-bottom: 0.5rem;
}

.header-subtitle {
  color: var(--va-text-primary);
  opacity: 0.7;
  font-size: 1rem;
}

/* 设置网格 */
.settings-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 设置卡片 */
.setting-card {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: none;
  overflow: hidden;
  transition: all 0.3s ease;
  --va-card-border-radius: 1rem;
  margin-bottom: 1rem;
}

.setting-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-2px);
}

/* 卡片内容 */
.card-content {
  padding: 1.5rem;
}

/* 设置项列表 */
.settings-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  border-radius: 0.75rem;
  border: 1px solid var(--va-background-border);
  transition: all 0.2s ease;
  padding: 1rem;
}

.setting-item:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.setting-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
  min-width: 0;
}

.setting-icon {
  flex-shrink: 0;
  background: rgba(var(--va-info-rgb), 0.1);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
}

.setting-text {
  flex: 1;
  min-width: 0;
}

.setting-title {
  font-weight: 600;
  color: var(--va-text-primary);
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.setting-description {
  font-size: 0.875rem;
  color: var(--va-text-primary);
  opacity: 0.7;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-clamp: 2;
}

/* 设置控制区域 */
.setting-controls {
  display: flex;
  gap: 0.75rem;
  flex: 1;
}

.control-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
  background: var(--va-background-primary);
  border-radius: 0.5rem;
  min-width: 0;
}

.threshold-input-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
}

/* 标签和输入框组 */
.label-input {
  display: flex;
  gap: 0.75rem;
  width: 100%;
  align-items: center;
}

.primary-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--va-text-primary);
  white-space: nowrap;
  min-width: 80px;
}

.threshold-input {
  --va-input-wrapper-border-radius: 0.5rem;
  flex: 1;
}

/* 保存按钮容器 */
.save-button-container {
  display: flex;
  justify-content: flex-end;
}

/* 空状态 */
.empty-state {
  border: 2px dashed var(--va-background-border);
  --va-card-border-radius: 1rem;
}

.empty-state-content {
  text-align: center;
  padding: 3rem 1.5rem;
}

.empty-state-icon {
  margin-bottom: 1rem;
}

.empty-state-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: var(--va-text-primary);
  opacity: 0.7;
  margin-bottom: 0.5rem;
}

.empty-state-description {
  color: var(--va-text-primary);
  opacity: 0.6;
}

/* 响应式优化 */
@media (min-width: 640px) {
  .setting-item {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  .setting-controls {
    flex-direction: row;
    gap: 1rem;
    max-width: 60%;
  }

  .control-item {
    flex-direction: row;
    align-items: center;
  }

  .threshold-input-group {
    flex-direction: row;
    align-items: center;
    gap: 1rem;
  }
  
  .label-input {
    flex: 1;
  }
  
  .save-button-container {
    min-width: 100px;
  }
}

/* 平板样式 */
@media (min-width: 640px) and (max-width: 1023px) {
  .setting-controls {
    max-width: 70%;
  }
}

/* 手机端样式 */
@media (max-width: 639px) {
  .notification-settings {
    gap: 1rem;
  }

  .header-content {
    padding: 1rem;
  }

  .header-title {
    font-size: 1.25rem;
  }

  .card-content {
    padding: 1rem;
  }

  .setting-item {
    padding: 1rem;
  }

  .control-item {
    padding: 0.5rem;
  }
  
  .label-input {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .primary-label {
    margin-bottom: 0.5rem;
  }
  
  .threshold-input {
    width: 100%;
  }
  
  .save-button-container {
    width: 100%;
    margin-top: 0.5rem;
  }
}

/* 小屏幕手机样式 */
@media (max-width: 480px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .setting-info {
    gap: 0.75rem;
  }

  .setting-controls {
    gap: 0.5rem;
    flex-direction: column;
    width: 100%;
  }

  .threshold-input-group {
    flex-direction: column;
    align-items: stretch;
  }
}

/* VuesticUI 组件自定义 */
.va-input {
  --va-input-wrapper-border-radius: 0.5rem;
}

.va-card {
  --va-card-border-radius: 1rem;
}
</style>

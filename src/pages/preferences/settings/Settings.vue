<template>
  <div class="space-y-6 p-4">
    <!-- 用户名 -->
    <div class="flex flex-col md:flex-row md:items-center gap-4">
      <p class="min-w-[120px] md:w-[200px] font-semibold   dark:text-gray-300">
        {{ $t('preferences.name') }}
      </p>
      <div class="flex-1">
        <div class="max-w-3xl truncate">
          {{ store.userName }}
        </div>
      </div>
    </div>

    <!-- 账户 -->
    <div class="flex flex-col md:flex-row md:items-center gap-4">
      <p class="min-w-[120px] md:w-[200px] font-semibold   dark:text-gray-300">
        {{ $t('preferences.account') }}
      </p>
      <div class="flex-1">
        <div class="max-w-3xl truncate">
          {{ store.account }}
        </div>
      </div>
    </div>

    <!-- 密码 -->
    <div class="flex flex-col md:flex-row md:items-center gap-4">
      <p class="min-w-[120px] md:w-[200px] font-semibold   dark:text-gray-300">
        {{ $t('preferences.password') }}
      </p>
      <div class="flex-1">
        <div class="max-w-3xl">•••••••••••••</div>
        <!-- <p class="mt-1 text-xs text-primary cursor-pointer hover:underline" @click="showResetPasswordModal">
          {{ $t('preferences.forgetPasswordDesc') }}
        </p> -->
      </div>
      <VaButton class="w-full md:w-auto mt-2 md:mt-0" preset="primary" @click="emits('openResetPasswordModal')">
        {{ $t('preferences.updatePasswordBtn') }}
      </VaButton>
    </div>

    <!-- 双重认证 -->
    <div class="flex flex-col md:flex-row md:items-center gap-4">
      <p class="min-w-[120px] md:w-[200px] font-semibold   dark:text-gray-300">
        {{ $t('preferences.twoFactorAuthentication') }}
      </p>
      <div class="flex-1">
        <div class="max-w-3xl">
          {{ twoFA.content }}
        </div>
      </div>
      <VaButton class="w-full md:w-auto mt-2 md:mt-0" :color="twoFA.color" @click="openTwoFaModal">
        {{ twoFA.button }}
      </VaButton>
    </div>

    <!-- 主题 -->
    <!-- <div class="flex flex-col md:flex-row md:items-center gap-4">
      <p class="min-w-[120px] md:w-[200px] font-semibold   dark:text-gray-300">
        {{ $t('preferences.theme') }}
      </p>
      <div class="flex-1">
        <div class="max-w-3xl">
          <ThemeSwitcher />
        </div>
      </div>
    </div>
    <div class="flex flex-col md:flex-row md:items-center gap-4">
      <p class="min-w-[120px] md:w-[200px] font-semibold   dark:text-gray-300">
        {{ $t('preferences.language') }}

      </p>
      <div class="flex-1">
        <div class="max-w-3xl">
          <LanguageSwitcher />
          <span class="text-xs text-gray-500 mt-2">注意：目前国际化只有部分页面完成，后续会继续完善</span>
        </div>
      </div>
    </div> -->
    <!-- 广告通知设置 -->
    <div class="mt-8">
      <NotificationSettings @refresh="getUserInfoFn" v-if="isPermission" />
    </div>

    <!-- 广告高亮标记设置 -->
    <div class="mt-8">
      <AdHighlightSettings v-if="isPermission" />
    </div>

    <!-- 双重认证模态框 -->
    <TwoFaModal v-model="isTwoFaModalOpen" />
  </div>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue'
import { getUserInfo } from '@/api/auth/index.js'
import ThemeSwitcher from '../theme-switcher/ThemeSwitcher.vue'
import LanguageSwitcher from '../language-switcher/LanguageSwitcher.vue'
import NotificationSettings from '../notification-settings/NotificationSettings.vue'
import AdHighlightSettings from '../ad-highlight-settings/AdHighlightSettings.vue'
import { useUserStore } from '../../../stores/user-store.js'
import { useI18n } from 'vue-i18n'
import TwoFaModal from '../modals/TwoFaModal.vue'
import { resetUserPwd } from '@/api/system/user'
const store = useUserStore()
const isPermission = ref(false)
import { useToast, useModal } from 'vuestic-ui'
const { init: toast } = useToast();
const { confirm } = useModal()
const { t } = useI18n()
const isLoading = ref(false)
const twoFA = computed(() => {
  if (!store.is2FAEnabled) {
    return {
      color: 'primary',
      button: t('preferences.twoFactorAuthenticationDisabledButton'),
      content: t('preferences.twoFactorAuthenticationDisabledContent'),
    }
  } else {
    return {
      color: 'danger',
      button: t('preferences.disableTwoFactorAuthentication'),
      content: t('preferences.twoFactorAuthenticationContent'),
    }
  }
})

const isTwoFaModalOpen = ref(false)

const openTwoFaModal = () => {
  isTwoFaModalOpen.value = true
}

onMounted(async () => {
  isPermission.value = store.roleInfo.name != '广告渠道'
  await getUserInfoFn();

})

const getUserInfoFn = async () => {
  const res = await getUserInfo()
  store.setUserInfoFromAPI(res.data)
}

const showResetPasswordModal = async () => {
  try {
    const agreed = await confirm({
      title: '重置密码',
      message: `确定要重置密码吗？重置后，密码将被设置为123456`,
      okText: '确定',
      cancelText: '取消',
      size: 'small'
    })

    if (agreed) {
      await initPassword();
    }
  } catch (err) {
    // 错误处理
  }
}

const initPassword = async () => {
  isLoading.value = true
  try {
    const response = await resetUserPwd()
    if (response.code == 200) {
      toast({
        color: 'success',
        message: '密码重置成功'
      })
    }
  } catch (error) {
    console.error('域名分配删除失败:', error)
  } finally {
    isLoading.value = false
  }
}

const emits = defineEmits(['openNameModal', 'openResetPasswordModal'])
</script>
import { ref } from 'vue'
import { listUser, addUser, updateUser, delUser, resetUserPwd } from '@/api/system/user'
import { useToast } from 'vuestic-ui'

export function useUsers() {
  const { init: toast } = useToast();

  const users = ref([])
  const isLoading = ref(false)
  const error = ref(null)

  const filters = ref({
    search: '',
    enable: '',
  })

  const pagination = ref({
    page: 1,
    perPage: 20,
    total: 0,
  })

  const sorting = ref({
    sortBy: 'name',
    sortingOrder: 'asc',
  })

  const fetchUsers = async () => {
    isLoading.value = true
    error.value = null
    try {
      const response = await listUser({
        pageNum: pagination.value.page,
        pageSize: pagination.value.perPage,
        name: filters.value.search || undefined,
        enable: filters.value.enable || undefined,
      })

      if (response.code === 200) {
        console.log('response', response)
        users.value = response.data.pageList.map((item) => ({
          ...item,
          fullname: item.name,
          username: item.account,
          active: item.enable === 1,
          projects: item.projects || [],
          role: item.admin ? 'admin' : 'user',
        }))
        pagination.value.total = response.total || users.value.length
      } else {
        error.value = new Error(response.msg || '获取用户列表失败')
      }
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('获取用户列表失败')
    } finally {
      isLoading.value = false
    }
  }

  const add = async (user) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await addUser({
        name: user.fullname || user.name,
        account: user.username || user.account,
        password: user.password,
        enable: user.active ? 1 : 0,
        roleId: user.roleId || '2', // 默认普通用户角色
        remark: user.notes || user.remark,
        admin: user.role === 'admin',
      })

      if (response.code === 200) {
        await fetchUsers()
      }
    } catch (err) {

    } finally {
      isLoading.value = false
    }
  }

  const update = async (user) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await updateUser({
        id: user.id,
        name: user.name,
        account: user.account,
        enable: user.active ? 1 : 0,
        roleId: user.roleId || (user.role === 'admin' ? '1' : '2'),
        remark: user.notes || user.remark,
        admin: user.role === 'admin',
      })

      if (response.code == 200) {
        await fetchUsers()
        return true
      } else {
        error.value = new Error(response.msg || '更新用户失败')
        return false
      }
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('更新用户失败')
      return false
    } finally {
      isLoading.value = false
    }
  }

  const remove = async (user) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await delUser([user.id])

      if (response.code === 200) {
        await fetchUsers()
      } else {
        error.value = new Error(response.msg || '删除用户失败')
      }
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('删除用户失败')
    } finally {
      isLoading.value = false
    }
  }

  const resetPassword = async (user, newPassword) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await updateUser({
        id: user.id,
        password: newPassword,
      })

      if (response.code === 200) {
        return true
      } else {
        error.value = new Error(response.msg || '重置密码失败')
        return false
      }
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('重置密码失败')
      return false
    } finally {
      isLoading.value = false
    }
  }

  const initPassword = async (user) => {
    isLoading.value = true
    error.value = null
    try {
      const response = await updateUser({
        id: user.id,
        password: user.password,
      })

      if (response.code == 200) {
        await fetchUsers()
        return true
      }
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('更新用户失败')
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 头像上传功能
  const uploadAvatar = async (blob) => {
    // 实际项目中应该调用上传API
    // 这里仅作为示例，返回一个本地URL
    try {
      // 创建一个临时URL作为演示
      const publicUrl = URL.createObjectURL(blob)
      return { publicUrl }
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('头像上传失败')
      return { publicUrl: '' }
    }
  }

  // 初始加载用户列表
  fetchUsers()

  return {
    users,
    isLoading,
    filters,
    sorting,
    pagination,
    error,
    fetchUsers,
    add,
    update,
    remove,
    initPassword,
    resetPassword,
    uploadAvatar,

  }
}

<script setup>
import { ref, watchEffect, onMounted, onUnmounted } from 'vue'
import UsersTable from './widgets/UsersTable.vue'
import EditUserForm from './widgets/EditUserForm.vue'
import ResetPasswordModal from './widgets/ResetPasswordModal.vue'
import { useUsers } from './composables/useUsers'
import { useModal, useToast } from 'vuestic-ui'
import { useTransition } from '@/composables/useTransition.js'
import { useI18n } from 'vue-i18n'

// 使用 i18n
const { t } = useI18n()

// 使用过渡动画功能
const {
  isExpanded: isFilterExpanded,
  contentRef: filterContent,
  isMobileView,
  toggle: toggleFilter,
  getContentStyles,
  init: initTransition,
  cleanup: cleanupTransition
} = useTransition({
  defaultExpanded: true,  // PC端默认展开
  breakpoint: 992,        // 小于992px为移动设备
  animationDuration: 300  // 动画持续时间
})

const doShowEditUserModal = ref(false)
const doShowResetPasswordModal = ref(false)
const { users, isLoading, filters, sorting, pagination, error, fetchUsers, ...usersApi } = useUsers()
const userToEdit = ref(null)

const { confirm } = useModal()
const showEditUserModal = (user) => {
  userToEdit.value = user
  doShowEditUserModal.value = true
}

const showAddUserModal = () => {
  userToEdit.value = null
  doShowEditUserModal.value = true
}

const showResetPasswordModal = async (user) => {
  userToEdit.value = user
  doShowResetPasswordModal.value = true
}

const { init: notify } = useToast()

watchEffect(() => {
  if (error.value) {
    notify({
      message: error.value.message,
      color: 'danger',
    })
  }
})

const onUserSaved = async (user) => {
  if (user.avatar && user.avatar.startsWith('blob:')) {
    const blob = await fetch(user.avatar).then((r) => r.blob())
    const { publicUrl } = await usersApi.uploadAvatar(blob)
    user.avatar = publicUrl
  }
  //
  if (userToEdit.value) {
    await usersApi.update(user)
    if (!error.value) {
      notify({
        message: t('adminUser.messages.userUpdated', { name: user.fullname || user.name }),
        color: 'success',
      })
    }
  } else {
    await usersApi.add(user)

    if (!error.value) {
      notify({
        message: t('adminUser.messages.userCreated', { name: user.fullname || user.name }),
        color: 'success',
      })
    }
  }
}

const onUserDelete = async (user) => {
  await usersApi.remove(user)
  notify({
    message: t('adminUser.messages.userDeleted', { name: user.fullname || user.name }),
    color: 'success',
  })
}

const onPasswordReset = async (password) => {
  let res = await usersApi.initPassword({
    id: userToEdit.value.id,
    password,
  })
  if (res) {
    doShowResetPasswordModal.value = false
    notify({
      message: t('adminUser.messages.passwordResetSuccess'),
      color: 'success',
    })
  }
}

const editFormRef = ref()


const beforeEditFormModalClose = async (hide) => {
  hide()
}

const onChangeEnable = (value) => {
  fetchUsers()
}

const resetFilters = () => {
  filters.value.search = ''
  filters.value.enable = ''
  fetchUsers()
}

// 生命周期钩子
onMounted(() => {
  initTransition() // 初始化过渡动画相关逻辑
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  cleanupTransition() // 清理过渡动画相关事件监听
})
</script>

<template>


  <VaCard class="filter-card">
    <!-- 筛选区域标题和控制按钮 -->
    <div class="filter-header flex justify-between items-center pb-2">
      <div class="flex items-center gap-2">
        <VaIcon name="mso-filter_list" color="primary" />
        <h2 class="text-lg font-medium">{{ t('adminUser.filter.title') }}</h2>
      </div>
      <div class="flex gap-2">
        <!-- 仅在移动设备和平板上显示展开/折叠按钮 -->
        <VaButton v-if="isMobileView" preset="secondary" border-color="primary" size="small" class="filter-toggle"
          @click="toggleFilter" :icon="isFilterExpanded ? 'mso-expand_less' : 'mso-expand_more'"
          :aria-label="isFilterExpanded ? t('adminUser.filter.collapseFilter') : t('adminUser.filter.expandFilter')">
          {{ isFilterExpanded ? t('adminUser.filter.collapse') : t('adminUser.filter.expand') }}
        </VaButton>
      </div>
    </div>

    <!-- 筛选区域内容 - 使用JS动画 -->
    <div ref="filterContent" class="filter-content" :style="{
      ...getContentStyles(),
    }">
      <!-- 筛选表单 -->
      <div class="filter-form" v-show="isFilterExpanded">
        <!-- 筛选条件网格 -->
        <div class="filter-grid">
          <!-- 状态筛选 -->
          <div class="filter-item">
            <div class="filter-item-header">
              <label class="filter-label">{{ t('adminUser.filter.status') }}</label>
            </div>
            <VaSelect v-model="filters.enable" :placeholder="t('adminUser.filter.statusPlaceholder')" :options="[
              { text: t('adminUser.status.enabled'), value: '1' },
              { text: t('adminUser.status.disabled'), value: '0' },
            ]" track-by="value" :text-by="(option) => option.text" :value-by="(option) => option.value"
              @update:modelValue="onChangeEnable" class="filter-input" />
          </div>

          <!-- 搜索筛选 -->
          <div class="filter-item">
            <div class="filter-item-header">
              <label class="filter-label">{{ t('adminUser.filter.search') }}</label>
            </div>
            <VaInput v-model="filters.search" :placeholder="t('adminUser.filter.searchPlaceholder')" class="filter-input">
              <template #prependInner>
                <VaIcon name="search" color="secondary" size="small" />
              </template>
            </VaInput>
          </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="filter-actions mt-4">
          <VaButton color="primary" icon="mso-add" @click="showAddUserModal">{{ t('adminUser.buttons.add') }}</VaButton>
          <VaButton color="primary" icon="mso-search" @click="fetchUsers">{{ t('adminUser.buttons.search') }}</VaButton>
          <VaButton preset="secondary" border-color="primary" icon="mso-refresh" @click="resetFilters">{{ t('adminUser.buttons.reset') }}</VaButton>
        </div>
      </div>
    </div>
  </VaCard>

  <VaCard>
    <UsersTable v-model:sort-by="sorting.sortBy" v-model:sorting-order="sorting.sortingOrder" :users="users"
      :loading="isLoading" :pagination="pagination" @editUser="showEditUserModal" @deleteUser="onUserDelete"
      @resetPassword="showResetPasswordModal" />
  </VaCard>

  <VaModal v-slot="{ cancel, ok }" v-model="doShowEditUserModal" size="small" close-button hide-default-actions
    :before-cancel="beforeEditFormModalClose">
    <h1 class="va-h5">{{ userToEdit ? t('adminUser.form.editUser') : t('adminUser.form.addUser') }}</h1>
    <EditUserForm ref="editFormRef" :user="userToEdit" :save-button-label="userToEdit ? t('adminUser.buttons.save') : t('adminUser.buttons.add')" @close="cancel"
      @save="
        (user) => {
          onUserSaved(user)
          ok()
        }
      " />
  </VaModal>

  <VaModal v-slot="{ cancel }" v-model="doShowResetPasswordModal" size="small" max-width="400px" close-button
    hide-default-actions>
    <ResetPasswordModal v-if="userToEdit" :user="userToEdit" @close="cancel" @save="onPasswordReset" />
  </VaModal>
</template>

<style>
.primary-label {
  color: var(--va-primary);
  font-size: 12px;
  font-weight: 600;
}

.filter-label {
  color: var(--va-primary);
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
}

/* 筛选表单布局 */
.filter-form {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* 筛选网格布局 */
.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 12px;
  margin-top: 10px;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 8px;
}

.filter-item-header {
  display: flex;
  align-items: center;
}

.filter-input {
  width: 100%;
}

/* 操作按钮区域 */
.filter-actions {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 10px;
  margin: 8px 0;
}

/* 响应式样式 */
@media (max-width: 991px) {
  .filter-toggle {
    display: flex;
  }

  .filter-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 2fr));
  }
}

@media (min-width: 992px) {
  .filter-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }
}
</style>

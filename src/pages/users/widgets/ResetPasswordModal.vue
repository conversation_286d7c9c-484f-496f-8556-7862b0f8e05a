<script setup>
import { ref } from 'vue'
import { useForm } from 'vuestic-ui'
import { useI18n } from 'vue-i18n'

// 使用 i18n
const { t } = useI18n()

const props = defineProps({
  user: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(['close', 'save'])

const form = useForm('reset-password-form')
const oldPassword = ref('')
const newPassword = ref('')
const confirmPassword = ref('')

const passwordRules = [
  (v) => !!v || t('adminUser.resetPassword.passwordRequired'), 
  (v) => v.length >= 6 || t('adminUser.resetPassword.passwordLength')
]

const confirmPasswordRules = [
  (v) => !!v || t('adminUser.resetPassword.confirmPasswordRequired'),
  (v) => v === newPassword.value || t('adminUser.resetPassword.passwordMismatch'),
]

const onSave = () => {
  if (form.validate()) {
    emit('save', newPassword.value)
  }
}
</script>

<template>
  <VaForm ref="reset-password-form" class="flex-col justify-start items-start gap-4 inline-flex w-full">
    <h1 class="va-h5">{{ t('adminUser.resetPassword.titleWithName', { name: props.user.fullname || props.user.name }) }}</h1>
    <div class="self-stretch flex-col justify-start items-start gap-4 flex">
      <VaInput v-model="newPassword" :label="t('adminUser.resetPassword.newPassword')" class="w-full" :rules="passwordRules" name="password"
        type="password" />
      <!-- <VaInput v-model="oldPassword" label="旧密码" class="w-full" :rules="passwordRules" name="password"
        type="password" /> -->
      <VaInput v-model="confirmPassword" :label="t('adminUser.resetPassword.confirmPassword')" class="w-full" :rules="confirmPasswordRules"
        name="confirmPassword" type="password" />

      <div class="flex gap-2 flex-col-reverse items-stretch justify-end w-full sm:flex-row sm:items-center">
        <VaButton preset="secondary" color="secondary" @click="$emit('close')">{{ t('adminUser.buttons.cancel') }}</VaButton>
        <VaButton :disabled="!newPassword || !confirmPassword || newPassword !== confirmPassword" @click="onSave">{{ t('adminUser.buttons.confirm') }}
        </VaButton>
      </div>
    </div>
  </VaForm>
</template>

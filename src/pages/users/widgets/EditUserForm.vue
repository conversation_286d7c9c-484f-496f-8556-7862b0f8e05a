<script setup>
import { computed, ref, watch, onMounted } from 'vue'
import { useForm } from 'vuestic-ui'
import { validators } from '../../../utils'
import { getUser } from '../../../api/system/user'
import { useI18n } from 'vue-i18n'

// 使用 i18n
const { t } = useI18n()

const roleSelectOptions = ref([])
const props = defineProps({
  user: {
    type: Object,
    default: null,
  },
  saveButtonLabel: {
    type: String,
    default: 'Save',
  },
})

onMounted(async () => {
  const res = await getUser()
  roleSelectOptions.value = res.data.map((item) => ({
    text: item.name,
    value: item.id,
  }))
})

const defaultNewUser = {
  avatar: '',
  fullname: '',
  name: '',
  account: '',
  role: 'user',
  username: '',
  notes: '',
  email: '',
  active: true,
  enable: 1,
  userType: '00',
  roleId: '2',
  admin: false,
  projects: [],
  creator: null,
  createTime: null,
  updator: null,
  updateTime: null,
  remark: null,
  userName: null,
  motto: null,
  lastOnlineTime: null,
  createIp: null,
  ipAddress: null,
  password: '',
  verifyTwoSecret: null,
  permissions: null,
  roles: null,
}

const newUser = ref({ ...defaultNewUser })

const isFormHasUnsavedChanges = computed(() => {
  return Object.keys(newUser.value).some((key) => {
    if (key === 'avatar' || key === 'projects') {
      return false
    }
    return (
      newUser.value[key] !== (props.user ?? defaultNewUser)?.[key]
    )
  })
})

defineExpose({
  isFormHasUnsavedChanges,
})

watch(
  [() => props.user],
  () => {
    if (!props.user) {
      return
    }

    newUser.value = {
      ...props.user,
      fullname: props.user.name || props.user.fullname || '',
      username: props.user.account || props.user.username || '',
      active: props.user.enable === 1 || !!props.user.active,
      projects: Array.isArray(props.user.projects) ? props.user.projects : [],
      avatar: props.user.avatar || '',
      notes: props.user.remark || props.user.notes || '',
    }
  },
  { immediate: true },
)

const avatar = ref()

const makeAvatarBlobUrl = (avatar) => {
  return URL.createObjectURL(avatar)
}

watch(avatar, (newAvatar) => {
  newUser.value.avatar = newAvatar ? makeAvatarBlobUrl(newAvatar) : ''
})

const form = useForm('add-user-form')

const emit = defineEmits(['close', 'save'])

const onSave = () => {
  if (form.validate()) {
    emit('save', newUser.value)
  }
}
</script>

<template>
  <VaForm v-slot="{ isValid }" ref="add-user-form" class="flex-col justify-start items-start gap-4 inline-flex w-full">
    <div class="self-stretch flex-col justify-start items-start gap-4 flex">
      <div class="flex gap-4 flex-col sm:flex-row w-full">
        <VaInput v-model="newUser.name" :label="t('adminUser.form.nickname')" class="w-full sm:w-1/2" :rules="[validators.required]"
          name="fullName" />
        <VaInput v-model="newUser.account" :label="t('adminUser.form.loginAccount')" class="w-full sm:w-1/2" :rules="[validators.required]"
          name="account" />

      </div>

      <div class="flex gap-4 w-full">
        <div class="w-1/2">
          <VaSelect v-model="newUser.roleId" :label="t('adminUser.form.role')" class="w-full" :options="roleSelectOptions"
            :rules="[validators.required]" name="role" value-by="value" />
        </div>
        <div class="flex items-center w-1/2" v-if="!newUser.id">
          <VaValue v-slot="isPasswordVisible" :default-value="false">
            <VaInput v-model="newUser.password" :rules="[validators.required]"
              :type="isPasswordVisible.value ? 'text' : 'password'" class="mb-4" :placeholder="t('adminUser.form.passwordPlaceholder')"
              @clickAppendInner.stop="isPasswordVisible.value = !isPasswordVisible.value" :label="t('adminUser.form.initialPassword')"
              autocomplete="new-password">
              <template #appendInner>
                <VaIcon :name="isPasswordVisible.value ? 'mso-visibility_off' : 'mso-visibility'" class="cursor-pointer"
                  color="secondary" />
              </template>
            </VaInput>
          </VaValue>
        </div>
      </div>

      <VaCheckbox v-model="newUser.active" :label="t('adminUser.form.active')" class="w-full" name="active" />
      <div class="flex gap-2 flex-col-reverse items-stretch justify-end w-full sm:flex-row sm:items-center">
        <VaButton preset="secondary" color="secondary" @click="$emit('close')">{{ t('adminUser.buttons.cancel') }}</VaButton>
        <VaButton :disabled="!isValid" @click="onSave">{{ props.saveButtonLabel }}</VaButton>
      </div>
    </div>
  </VaForm>
</template>

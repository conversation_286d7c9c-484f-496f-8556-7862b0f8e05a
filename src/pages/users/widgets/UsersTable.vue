<script setup>
import { defineVaDataTableColumns, useModal } from 'vuestic-ui'
import { computed, toRef } from 'vue'
import { useVModel } from '@vueuse/core'
import { useI18n } from 'vue-i18n'

// 使用 i18n
const { t } = useI18n()

const columns = defineVaDataTableColumns([
  { label: t('adminUser.table.nickname'), key: 'name', sortable: false },
  { label: t('adminUser.table.role'), key: 'roleName', sortable: false },
  { label: t('adminUser.table.loginAccount'), key: 'account', sortable: false },
  { label: t('adminUser.table.role'), key: 'role', sortable: false },
  { label: t('adminUser.table.twoFactorAuth'), key: 'verifyTwoSecret', sortable: false },
  { label: t('adminUser.table.status'), key: 'enable', sortable: false },
  { label: t('adminUser.table.actions'), key: 'actions', align: 'center' },
])

const props = defineProps({
  users: {
    type: Array,
    required: true,
  },
  loading: { type: Boolean, default: false },
  pagination: { type: Object, required: true },
  sortBy: { type: String, required: true },
  sortingOrder: { type: String, default: null },
})

const emit = defineEmits([
  'edit-user',
  'delete-user',
  'reset-password',
  'update:sortBy',
  'update:sortingOrder'
])

const users = toRef(props, 'users')
const sortByVModel = useVModel(props, 'sortBy', emit)
const sortingOrderVModel = useVModel(props, 'sortingOrder', emit)
const totalPages = computed(() => Math.ceil(props.pagination.total / props.pagination.perPage))
const { confirm } = useModal()
const onUserDelete = async (user) => {
  const agreed = await confirm({
    title: t('adminUser.messages.deleteConfirmTitle'),
    message: t('adminUser.messages.deleteConfirmMessage', { name: user.fullname || user.name }),
    okText: t('adminUser.buttons.delete'),
    cancelText: t('adminUser.buttons.cancel'),
    size: 'small',
    maxWidth: '380px',
  })

  if (agreed) {
    emit('delete-user', user)
  }
}
</script>
<template>
  <VaDataTable v-model:sort-by="sortByVModel" v-model:sorting-order="sortingOrderVModel" :columns="columns"
    :items="users" :loading="$props.loading">
    <template #cell(fullname)="{ rowData }">
      <div class="flex items-center gap-2 max-w-[230px] ellipsis">
        <!-- <UserAvatar :user="rowData" size="small" /> -->
        {{ rowData.fullname || rowData.name }}
      </div>
    </template>

    <template #cell(username)="{ rowData }">
      <div class="max-w-[120px] ellipsis">
        {{ rowData.username || rowData.account }}
      </div>
    </template>

    <template #cell(email)="{ rowData }">
      <div class="ellipsis max-w-[230px]">
        {{ rowData.email }}
      </div>
    </template>
    <template #cell(verifyTwoSecret)="{ rowData }">
      <VaBadge :text="rowData.verifyTwoSecret ? t('adminUser.status.enabled') : t('adminUser.status.disabled')" :color="rowData.verifyTwoSecret ? 'success' : 'danger'" />
    </template>
    <template #cell(role)="{ rowData }">
      <VaBadge :text="rowData.admin ? 'admin' : 'user'" :color="rowData.admin ? 'danger' : 'background-element'" />
    </template>
    <template #cell(enable)="{ rowData }">
      <VaBadge :text="rowData.enable == 1 ? t('adminUser.status.enabled') : t('adminUser.status.disabled')" :color="rowData.enable ? 'success' : 'danger'" />
    </template>

    <template #cell(actions)="{ rowData }">
      <div class="flex gap-2 justify-start">
        <VaButton preset="primary" size="small" icon="mso-edit" :aria-label="t('adminUser.buttons.edit')"
          @click="$emit('edit-user', rowData)" />
        <VaButton preset="primary" size="small" icon="mso-key" color="info" :aria-label="t('adminUser.buttons.resetPassword')"
          @click="$emit('reset-password', rowData)" />
        <VaButton preset="primary" size="small" icon="mso-delete" color="danger" :aria-label="t('adminUser.buttons.delete')"
          @click="onUserDelete(rowData)" />
      </div>
    </template>
  </VaDataTable>
  <div class="flex  md:flex-row gap-2 justify-end items-center py-2">
    <div>
      <b>{{ $props.pagination.total }} {{ t('adminUser.table.results') }}</b>
      {{ t('adminUser.table.perPage') }}
      <VaSelect v-model="$props.pagination.perPage" class="!w-20" :options="[10, 20, 50, 100]" />
    </div>
    <div v-if="totalPages > 1" class="flex">
      <VaButton preset="secondary" icon="va-arrow-left" :aria-label="t('adminUser.buttons.prevPage')" :disabled="$props.pagination.page === 1"
        @click="$props.pagination.page--" />
      <VaButton class="mr-2" preset="secondary" icon="va-arrow-right" :aria-label="t('adminUser.buttons.nextPage')"
        :disabled="$props.pagination.page === totalPages" @click="$props.pagination.page++" />
      <VaPagination v-model="$props.pagination.page" buttons-preset="secondary" :pages="totalPages" :visible-pages="5"
        :boundary-links="false" :direction-links="false" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.va-data-table {
  ::v-deep(.va-data-table__table-tr) {
    border-bottom: 1px solid var(--va-background-border);
  }
}
</style>

<script setup>
import { defineVaDataTableColumns } from 'vuestic-ui'
import { computed, watch, onMounted } from 'vue'
import { useGame } from '../useGame'
import { useToast } from 'vuestic-ui'
import { useI18n } from 'vue-i18n' // 导入 i18n

// 使用 i18n
const { t } = useI18n()

const columns = defineVaDataTableColumns([
  { label: t('games.table.id'), key: 'id', sortable: false },
  { label: t('games.table.name'), key: 'gameName', sortable: false },
  { label: t('games.table.icon'), key: 'gameIcon', sortable: false },
  { label: t('games.table.category'), key: 'gameCategory', sortable: false },
  { label: t('games.table.hot'), key: 'gameHot', sortable: false },
  // { label: t('games.table.featured'), key: 'featured', sortable: false },
  { label: t('games.table.enable'), key: 'enable', sortable: false },
  { label: t('games.table.create_time'), key: 'createTime', sortable: false },
  { label: t('games.table.actions'), key: 'actions', align: 'right' },
])

// 使用 useGame 获取数据和状态
const { gameList, isLoading, pagination, sorting, fetchGameList, toggleGameEnable, toggleGameFeatured } = useGame()

const emit = defineEmits(['edit-game'])

const { init: notify } = useToast()

// 处理分页变化
const handlePageChange = (pageNum) => {
  pagination.value.pageNum = pageNum
  fetchGameList()
}

const handlePerPageChange = (pageSize) => {
  pagination.value.pageSize = pageSize
  pagination.value.pageNum = 1
  fetchGameList()
}

// 监听排序变化
watch([() => sorting.value.sortBy, () => sorting.value.sortingOrder], () => {
  fetchGameList()
})

// 监听 gameList 变化，确保表格数据更新
watch(() => gameList.value, (newVal) => {
}, { deep: true })

// 初始化时加载数据
onMounted(() => {
  fetchGameList()
})

/**
 * 格式化时间戳
 * @param {number} timestamp - 时间戳
 * @returns {string} 格式化后的时间字符串
 */
const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  return new Date(timestamp * 1000).toLocaleString('zh-CN')
}

/**
 * 处理启用状态切换
 */
const handleToggleEnable = async (game) => {
  const result = await toggleGameEnable(game)
  if (result.success) {
    notify({
      message: result.message,
      color: 'success',
    })
  } else {
    notify({
      message: result.message,
      color: 'danger',
    })
  }
}

/**
 * 处理精选状态切换
 */
const handleToggleFeatured = async (game) => {
  const result = await toggleGameFeatured(game)
  if (result.success) {
    notify({
      message: result.message,
      color: 'success',
    })
  } else {
    notify({
      message: result.message,
      color: 'danger',
    })
  }
}
</script>

<template>
  <VaDataTable v-model:sort-by="sorting.sortBy" v-model:sorting-order="sorting.sortingOrder" :items="gameList"
    :columns="columns" :loading="isLoading">

    <template #cell(gameIcon)="{ rowData }">
      <div class="flex items-center">
        <VaImage class="w-full md:w-1/2 lg:w-1/3" :src="`https://14game.top` + rowData.gameIcon"
          v-if="rowData.gameIcon" />
        <span v-else class="text-gray-400">{{ t('games.table.no_icon') }}</span>
      </div>
    </template>

    <template #cell(gameHot)="{ rowData }">
      <VaBadge :text="rowData.gameHot || 0" color="info" />
    </template>

    <template #cell(featured)="{ rowData }">
      <VaSwitch :model-value="rowData.featured === 1" size="small"
        @update:model-value="handleToggleFeatured(rowData)" />
    </template>

    <template #cell(enable)="{ rowData }">
      <VaSwitch :model-value="rowData.enable === 1" size="small" @update:model-value="handleToggleEnable(rowData)" />
    </template>

    <template #cell(createTime)="{ rowData }">
      {{ formatTime(rowData.createTime) }}
    </template>

    <template #cell(actions)="{ rowData }">
      <div class="flex gap-2">
        <VaButton preset="primary" size="small" icon="mso-edit" :aria-label="t('games.buttons.edit')"
          @click="$emit('edit-game', rowData)" />
      </div>
    </template>
  </VaDataTable>

  <div class="flex justify-end mt-4 gap-2 filter-actions1">
    <div>
      <b>{{ pagination.total }} {{ t('games.table.results') }}</b>
      {{ t('games.table.per_page') }}
      <VaSelect v-model="pagination.pageSize" class="!w-20 inline-block" :options="[10, 20, 50, 100]"
        @update:model-value="fetchGameList" />
    </div>
    <VaPagination v-model="pagination.pageNum" :pages="pagination.totalPage" :visible-pages="5"
      buttons-preset="secondary" gapped border-color="primary" class="justify-center sm:justify-start"
      @update:modelValue="fetchGameList" />
  </div>
</template>

<style scoped>
.w-8 {
  width: 2rem;
}

.filter-actions1 {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  gap: 10px;
  margin: 8px 0;
}

.h-8 {
  height: 2rem;
}

.rounded {
  border-radius: 0.25rem;
}

.object-cover {
  object-fit: cover;
}
</style>

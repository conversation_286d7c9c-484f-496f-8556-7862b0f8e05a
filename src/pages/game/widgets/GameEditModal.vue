<script setup>
import { ref, computed, watch } from 'vue'
import { useGame } from '../useGame'
import { useToast } from 'vuestic-ui'
import { useI18n } from 'vue-i18n' // 导入 i18n

// 使用 i18n
const { t } = useI18n()

const props = defineProps({
  game: {
    type: Object,
    required: true
  },
  visible: {
    type: Boolean,
    default: false
  },
  categoryList: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['close', 'updated'])

const { updateGameInfo } = useGame()
const { init: notify } = useToast()

const isLoading = ref(false)

// 表单数据
const formData = ref({
  id: '',
  gameName: '',
  gameUri: '',
  gameIcon: '',
  gamePreview: '',
  gameType: '',
  gameHot: 0,
  featured: 0,
  gameCategory: '',
  gameDesc: '',
  enable: 1,
})

// 游戏类型选项
const gameTypeOptions = [
  { text: t('games.game_type.h5'), value: 'H5' },
  { text: t('games.game_type.mini'), value: 'MINI' },
  { text: t('games.game_type.web'), value: 'WEB' },
]

// 监听 props.game 变化，更新表单数据
watch(() => props.game, (newGame) => {
  if (newGame) {
    formData.value = {
      id: newGame.id || '',
      gameName: newGame.gameName || '',
      gameUri: newGame.gameUri || '',
      gameIcon: newGame.gameIcon || '',
      gamePreview: newGame.gamePreview || '',
      gameType: newGame.gameType || '',
      gameHot: newGame.gameHot || 0,
      featured: newGame.featured || 0,
      gameCategory: newGame.gameCategory || '',
      gameDesc: newGame.gameDesc || '',
      enable: newGame.enable !== undefined ? newGame.enable : 1,
    }
  }
}, { immediate: true })


// 分类选项
const categoryOptions = computed(() => {
  return props.categoryList.map(category => ({
    text: category,
    value: category
  }))
})

// 提交表单
const handleSubmit = async () => {
  isLoading.value = true
  try {
    const result = await updateGameInfo(formData.value)
    if (result.success) {
      notify({
        message: t('games.messages.update_success'),
        color: 'success',
      })
      emit('updated')
      emit('close')
    } else {
      notify({
        message: result.message || t('games.messages.update_failed'),
        color: 'danger',
      })
    }
  } catch (error) {
    notify({
      message: t('games.messages.update_failed'),
      color: 'danger',
    })
  } finally {
    isLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('close')
}
</script>

<template>
  <div>
    <h1 class="va-h5 mb-4">{{ t('games.edit_modal.title') }}</h1>

    <VaForm @submit.prevent="handleSubmit">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- 游戏名称 -->
        <div class="flex flex-col gap-2">
          <VaInput v-model="formData.gameName" :label="t('games.edit_modal.name')" :placeholder="t('games.edit_modal.name_placeholder')" required />
        </div>

        <!-- 游戏URI -->
        <!-- <VaInput v-model="formData.gameUri" :label="t('games.edit_modal.uri')" :placeholder="t('games.edit_modal.uri_placeholder')" required /> -->

        <!-- 游戏类别 -->
        <VaSelect v-model="formData.gameCategory" :label="t('games.edit_modal.category')" :placeholder="t('games.edit_modal.category_placeholder')" :options="categoryOptions"
          text-by="text" value-by="value" />

        <!-- 热度 -->
        <!-- <VaInput v-model.number="formData.gameHot" :label="t('games.edit_modal.hot')" type="number" :placeholder="t('games.edit_modal.hot_placeholder')" min="0" /> -->

        <!-- 是否启用 -->

      </div>


      <!-- 游戏描述 -->
      <div class="mt-4 w-full">
        <VaTextarea v-model="formData.gameDesc" :label="t('games.edit_modal.description')" :placeholder="t('games.edit_modal.description_placeholder')" :max-rows="6" class="w-full" />
      </div>
      <div class="flex items-center gap-2 mt-4">
        <VaCheckbox v-model="formData.enable" :model-value="formData.enable === 1"
          @update:model-value="formData.enable = $event ? 1 : 0" :label="t('games.edit_modal.enable')" />
      </div>
      <!-- 操作按钮 -->
      <div class="flex gap-2 justify-end mt-6">
        <VaButton preset="secondary" @click="handleCancel">{{ t('games.buttons.cancel') }}</VaButton>
        <VaButton type="submit" :loading="isLoading">{{ t('games.buttons.save') }}</VaButton>
      </div>
    </VaForm>
  </div>
</template>

<style scoped>
.w-16 {
  width: 4rem;
}

.h-16 {
  height: 4rem;
}

.w-32 {
  width: 8rem;
}

.h-20 {
  height: 5rem;
}

.rounded {
  border-radius: 0.25rem;
}

.object-cover {
  object-fit: cover;
}
</style>

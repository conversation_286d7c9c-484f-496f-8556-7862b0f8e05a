import { ref } from 'vue'
import { getGameList, getGameCategoryList, updateGame } from '../../api/game/game'
import { useI18n } from 'vue-i18n' // 导入 i18n

export function useGame() {
  // 使用 i18n
  const { t } = useI18n()
  
  const gameList = ref([])
  const categoryList = ref([])
  const isLoading = ref(false)
  const error = ref(null)

  const filters = ref({
    search: '',
    gameCategory: '',
    enable: '',
    featured: '',
  })

  const pagination = ref({
    pageNum: 1,
    pageSize: 20,
    total: 0,
    totalPage: 0,
  })

  const sorting = ref({
    sortBy: 'createTime',
    sortingOrder: 'desc',
  })
  // 获取游戏列表
  const fetchGameList = async () => {
    isLoading.value = true
    error.value = null
    try {
      const response = await getGameList({
        pageNum: pagination.value.pageNum,
        pageSize: pagination.value.pageSize,
        gameName: filters.value.search || undefined,
        gameCategory: filters.value.gameCategory || undefined,
        enable: filters.value.enable || undefined,
        featured: filters.value.featured || undefined,
      })

      // 根据实际API响应结构处理数据
      if (response && response.data) {
        gameList.value = response.data.pageList || []
        pagination.value.total = response.data.total || 0
        pagination.value.totalPage = response.data.sumPage
        console.log('gameList', gameList.value)
      }
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(t('games.messages.get_list_error'))
      gameList.value = []
      pagination.value.total = 0
    } finally {
      isLoading.value = false
    }
  }

  // 获取游戏分类列表
  const fetchCategoryList = async () => {
    try {
      const response = await getGameCategoryList()
      console.log('categoryList', response)
      if (response && response.data) {
        categoryList.value = response.data || []
      }
    } catch (err) {
      console.error(t('games.messages.get_category_error'), err)
      categoryList.value = []
    }
  }

  // 更新游戏信息
  const updateGameInfo = async (gameData) => {
    try {
      const response = await updateGame(gameData)
      if (response && response.code === 200) {
        // 更新成功后直接更新本地数据，避免重新请求
        const index = gameList.value.findIndex((game) => game.id === gameData.id)
        if (index !== -1) {
          gameList.value[index] = { ...gameList.value[index], ...gameData }
        }
        return { success: true, message: t('games.messages.update_success') }
      } else {
        return { success: false, message: response.msg || t('games.messages.update_failed') }
      }
    } catch (err) {
      console.error(t('games.messages.update_failed'), err)
      return { success: false, message: err.message || t('games.messages.update_failed') }
    }
  }

  // 切换游戏启用状态
  const toggleGameEnable = async (game) => {
    const updatedGame = {
      ...game,
      enable: game.enable === 1 ? 0 : 1,
    }
    return await updateGameInfo(updatedGame)
  }

  // 切换游戏精选状态
  const toggleGameFeatured = async (game) => {
    const updatedGame = {
      ...game,
      featured: game.featured === 1 ? 0 : 1,
    }
    return await updateGameInfo(updatedGame)
  }

  return {
    // 响应式数据
    gameList,
    categoryList,
    isLoading,
    error,
    filters,
    pagination,
    sorting,

    // 方法
    fetchGameList,
    fetchCategoryList,
    updateGameInfo,
    toggleGameEnable,
    toggleGameFeatured,
  }
}

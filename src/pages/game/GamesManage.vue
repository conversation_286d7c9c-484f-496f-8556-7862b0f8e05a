<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import GameTable from './widgets/GameTable.vue'
import GameEditModal from './widgets/GameEditModal.vue'
import { useGame } from './useGame'
import { useTransition } from '@/composables/useTransition.js'
import { useI18n } from 'vue-i18n' // 导入 i18n
// import { useToast } from 'vuestic-ui'

// 使用 i18n
const { t } = useI18n()

const doShowEditModal = ref(false)
const { filters, fetchGameList, fetchCategoryList, categoryList } = useGame()
const gameToEdit = ref(null)

// 使用过渡动画功能
const {
    isExpanded: isFilterExpanded,
    contentRef: filterContent,
    isMobileView,
    toggle: toggleFilter,
    getContentStyles,
    init: initTransition,
    cleanup: cleanupTransition
} = useTransition({
    defaultExpanded: true,  // PC端默认展开
    breakpoint: 992,        // 小于992px为移动设备
    animationDuration: 300  // 动画持续时间
})

const showEditModal = (game) => {
    gameToEdit.value = game
    doShowEditModal.value = true
}

// const { init: notify } = useToast()

const resetFilters = () => {
    filters.value.search = ''
    filters.value.gameCategory = ''
    filters.value.enable = ''
    filters.value.featured = ''
    fetchGameList()
}

// 启用状态选项
const enableOptions = [
    { text: t('games.status.enabled'), value: '1' },
    { text: t('games.status.disabled'), value: '0' },
]

// 精选状态选项
const featuredOptions = [
    { text: t('games.status.featured'), value: '1' },
    { text: t('games.status.not_featured'), value: '0' },
]

// 初始化时获取分类列表
onMounted(() => {
    fetchCategoryList()
    initTransition() // 初始化过渡动画相关逻辑
})

// 处理编辑完成
const handleGameUpdated = () => {
    fetchGameList()
}

// 移除事件监听
onUnmounted(() => {
    cleanupTransition() // 清理过渡动画相关事件监听
})
</script>

<template>
    <div>
        <VaCard class="filter-card">
            <!-- 筛选区域标题和控制按钮 -->
            <div class="filter-header flex justify-between items-center pb-2">
                <div class="flex items-center gap-2">
                    <VaIcon name="mso-filter_list" color="primary" />
                    <h2 class="text-lg font-medium">{{ t('games.filter.title') }}</h2>
                </div>
                <div class="flex gap-2">
                    <!-- 仅在移动设备和平板上显示展开/折叠按钮 -->
                    <VaButton v-if="isMobileView" preset="secondary" border-color="primary" size="small"
                        class="filter-toggle" @click="toggleFilter"
                        :icon="isFilterExpanded ? 'mso-expand_less' : 'mso-expand_more'"
                        :aria-label="isFilterExpanded ? t('games.filter.collapse') : t('games.filter.expand')">
                        {{ isFilterExpanded ? t('games.filter.collapse_btn') : t('games.filter.expand_btn') }}
                    </VaButton>
                </div>
            </div>

            <!-- 筛选区域内容 - 使用JS动画 -->
            <div ref="filterContent" class="filter-content" :style="{
                ...getContentStyles(),
            }">
                <!-- 筛选表单 -->
                <div class="filter-form" v-show="isFilterExpanded">
                    <!-- 筛选条件网格 -->
                    <div class="filter-grid">
                        <!-- 名称筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">{{ t('games.search.name') }}</label>
                            </div>
                            <VaInput v-model="filters.search" :placeholder="t('games.search.name_placeholder')" class="filter-input">
                                <template #prependInner>
                                    <VaIcon name="search" color="secondary" size="small" />
                                </template>
                            </VaInput>
                        </div>

                        <!-- 类别筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">{{ t('games.search.category') }}</label>
                            </div>
                            <VaSelect v-model="filters.gameCategory" :placeholder="t('games.search.category_placeholder')"
                                :options="categoryList.map(cat => ({ text: cat.name || cat, value: cat.value || cat }))"
                                track-by="value" :text-by="(option) => option.text" :value-by="(option) => option.value"
                                class="filter-input" />
                        </div>

                        <!-- 启用状态筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">{{ t('games.search.enable') }}</label>
                            </div>
                            <VaSelect v-model="filters.enable" :placeholder="t('games.search.enable_placeholder')" :options="enableOptions"
                                track-by="value" :text-by="(option) => option.text" :value-by="(option) => option.value"
                                class="filter-input" />
                        </div>

                        <!-- 精选状态筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">{{ t('games.search.featured') }}</label>
                            </div>
                            <VaSelect v-model="filters.featured" :placeholder="t('games.search.featured_placeholder')" :options="featuredOptions"
                                track-by="value" :text-by="(option) => option.text" :value-by="(option) => option.value"
                                class="filter-input" />
                        </div>
                    </div>

                    <!-- 操作按钮区域 -->
                    <div class="filter-actions mt-4">
                        <VaButton color="primary" icon="mso-search" @click="fetchGameList">{{ t('games.buttons.search') }}</VaButton>
                        <VaButton preset="secondary" border-color="primary" icon="mso-refresh" @click="resetFilters">{{ t('games.buttons.reset') }}
                        </VaButton>
                    </div>
                </div>
            </div>
        </VaCard>

        <VaCard>
            <GameTable @edit-game="showEditModal" />
        </VaCard>

        <!-- 游戏编辑弹窗 -->
        <VaModal v-slot="{ cancel }" v-model="doShowEditModal" max-width="700px" size="large" mobile-fullscreen
            close-button hide-default-actions>
            <GameEditModal v-if="gameToEdit" :game="gameToEdit" :visible="doShowEditModal" @close="cancel"
                @updated="handleGameUpdated" :categoryList="categoryList" />
        </VaModal>

    </div>
</template>

<style scoped>
/* 基础样式 */
.primary-label {
    color: var(--va-primary);
    font-size: 12px;
    font-weight: 600;
}

.filter-label {
    color: var(--va-primary);
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
}

/* 筛选表单布局 */
.filter-form {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

/* 筛选网格布局 */
.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 12px;
    margin-top: 10px;
}

.filter-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-bottom: 8px;
}

.filter-item-header {
    display: flex;
    align-items: center;
}

.filter-input {
    width: 100%;
}

/* 操作按钮区域 */
.filter-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 10px;
    margin: 8px 0;
}

.filter-actions1 {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    gap: 10px;
    margin: 8px 0;
}

/* 响应式样式 */
@media (max-width: 991px) {
    .filter-toggle {
        display: flex;
    }

    .filter-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 2fr));
    }
}

@media (min-width: 992px) {
    .filter-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }
}
</style>
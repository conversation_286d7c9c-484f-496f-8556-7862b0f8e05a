<template>
    <div>
        <h1>asyncDate</h1>
        <VaButton :loading="syncLoading" @click="handleRefresh" preset="secondary" border-color="primary" size="medium">
            <div class="flex items-center gap-1">
                <VaIcon name="mso-sync" />
                <span>同步数据</span>
            </div>
        </VaButton>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import { execTask } from '@/utils/exec'
import { dataAsyncList, dataAsyncExec } from '@/api/ads/dataAsync.js'
const syncLoading = ref(false);

// 处理数据同步
const handleRefresh = async () => {
    if (syncLoading.value) return // 防止重复点击

    syncLoading.value = true
    syncResult.value = { status: 'info', message: '正在获取同步策略列表...' }
    syncProgress.value = { current: 0, total: 0, currentStrategy: '' }

    try {
        // 获取当前日期并格式化为 YYYY-MM-DD
        const today = new Date()
        const yesterday = new Date(today)
        yesterday.setDate(yesterday.getDate() - 1)

        const formatDate = (date) => {
            const year = date.getFullYear()
            const month = String(date.getMonth() + 1).padStart(2, '0')
            const day = String(date.getDate()).padStart(2, '0')
            return `${year}-${month}-${day}`
        }

        const startTime = formatDate(yesterday)
        const endTime = formatDate(today)

        // 获取需要同步的策略列表
        const listResponse = await dataAsyncList()

        if (listResponse.code !== 200 || !listResponse.data || !listResponse.data.length) {
            syncResult.value = { status: 'error', message: '获取同步策略列表失败' }
            return
        }

        const strategies = listResponse.data
        syncProgress.value.total = strategies.length

        // 定义数据格式化函数，为每个策略准备参数
        const dataFormatter = (strategy) => ({
            strategyName: strategy,
            startTime: startTime,
            endTime: endTime
        })

        // 定义进度回调函数
        const onProgress = (current, total, currentStrategy) => {
            syncProgress.value.current = current
            syncProgress.value.currentStrategy = currentStrategy
            syncResult.value = {
                status: 'info',
                message: `正在同步 ${currentStrategy}... (${current}/${total})`
            }
        }
        // 使用 execTask 执行任务
        const results = await execTask(dataAsyncExec, strategies, dataFormatter, { onProgress })
        // 统计成功和失败的任务
        const successCount = results.filter(result => result.success).length
        const failCount = results.length - successCount
        // 所有策略同步完成
        syncResult.value = {
            status: 'success',
            message: `同步完成: ${successCount}成功, ${failCount}失败 (共${strategies.length}个策略)`
        }
        // 刷新数据
        getList()
    } catch (error) {
        console.error('数据同步过程出错:', error)
        syncResult.value = { status: 'error', message: `同步失败: ${error.message}` }
    } finally {
        syncLoading.value = false
        // 1秒后清除结果消息
        setTimeout(() => {
            syncResult.value = { status: '', message: '' }
        }, 3000)
    }
}
</script>

<style scoped></style>
<template>
  <div class="domain-template">
    <!-- 筛选区域 -->
    <VaCard class="filter-card">
      <!-- 筛选区域标题和控制按钮 -->
      <div class="filter-header flex justify-between items-center pb-2">
        <div class="flex items-center gap-2">
          <VaIcon name="mso-filter_list" color="primary" />
          <h2 class="text-lg font-medium">{{ t('template.filter.title') }}</h2>
        </div>
        <div class="flex gap-2">
          <!-- 仅在移动设备和平板上显示展开/折叠按钮 -->
          <VaButton v-if="isMobileView" preset="secondary" border-color="primary" size="small" class="filter-toggle"
            @click="toggleFilter" :icon="isFilterExpanded ? 'mso-expand_less' : 'mso-expand_more'"
            :aria-label="isFilterExpanded ? t('template.filter.collapse') : t('template.filter.expand')">
            {{ isFilterExpanded ? t('template.filter.collapse_btn') : t('template.filter.expand_btn') }}
          </VaButton>
        </div>
      </div>

      <!-- 筛选区域内容 - 使用JS动画 -->
      <div ref="filterContent" class="filter-content" :style="{
        ...getContentStyles(),
      }">
        <!-- 筛选表单 -->
        <div class="filter-form" v-show="isFilterExpanded">
          <!-- 筛选条件网格 -->
          <div class="filter-grid">
            <!-- 名称筛选 -->
            <div class="filter-item">
              <div class="filter-item-header">
                <label class="filter-label">{{ t('template.name.label') }}</label>
              </div>
              <VaInput v-model="queryParams.name" :placeholder="t('template.name.placeholder')" class="filter-input" />
            </div>
          </div>

          <!-- 操作按钮区域 -->
          <div class="filter-actions mt-4">
            <VaButton color="primary" icon="mso-search" @click="handleQuery">{{ t('template.buttons.search') }}</VaButton>
            <VaButton preset="secondary" border-color="primary" icon="mso-refresh" @click="resetQuery">{{ t('template.buttons.reset') }}</VaButton>
            <VaButton preset="secondary" border-color="primary" icon="mso-add" @click="handleAdd">{{ t('template.buttons.add') }}</VaButton>
          </div>
        </div>
      </div>
    </VaCard>

    <!-- 数据表格 -->
    <VaDataTable :items="listData" :columns="columns" :loading="loading" hoverable striped>
      <!-- 创建时间列 -->
      <template #cell(createTime)="{ rowData }">
        <span>{{ parseTime(rowData.createTime) }}</span>
      </template>

      <!-- 更新时间列 -->
      <template #cell(updateTime)="{ rowData }">
        <span>{{ parseTime(rowData.updateTime) }}</span>
      </template>

      <!-- 操作列 -->
      <template #cell(actions)="{ rowData }">
        <div class="flex gap-2">
          <VaButton preset="primary" size="small" icon="mso-edit" @click="updateHandler(rowData)">{{ t('template.buttons.edit') }}</VaButton>
          <VaButton preset="primary" size="small" icon="mso-delete" color="danger" @click="removeHandler(rowData)">{{ t('template.buttons.delete') }}
          </VaButton>
        </div>
      </template>
    </VaDataTable>

    <!-- 分页 -->
    <div class="flex justify-end items-center mt-4 gap-2 filter-actions1 mx-4">
      <div>
        <b>{{ total }} {{ t('template.table.results') }}</b>
        {{ t('template.table.per_page') }}
        <VaSelect v-model="queryParams.pageSize" class="!w-20 inline-block" :options="[10, 20, 50, 100]"
          @update:model-value="getList" />
      </div>
      <VaPagination v-if="totalPages > 0" v-model="queryParams.pageNum" :pages="totalPages" :visible-pages="5"
        buttons-preset="secondary" gapped border-color="primary" class="justify-center sm:justify-start"
        @update:model-value="getList" />
    </div>

    <!-- 新增/编辑模板弹窗 -->
    <VaModal v-model="open" :title="title" max-width="500px" @cancel="cancel" hide-default-actions close-button>
      <VaForm ref="formRef" v-slot="{ isValid }" class="px-4 w-full">
        <VaInput v-model="form.name" :label="t('template.name.label')" :placeholder="t('template.name.placeholder')" class="mb-4" 
          :rules="[v => !!v || t('template.name.required')]" required-mark />
        <div class="mt-4 w-full">
          <VaTextarea v-model="form.remark" :label="t('template.description.label')" :placeholder="t('template.description.placeholder')" 
            class="w-full" :max-rows="4" />
        </div>

        <div class="flex justify-end gap-2 mt-6">
          <VaButton preset="secondary" @click="cancel">{{ t('template.buttons.cancel') }}</VaButton>
          <VaButton :disabled="!isValid" @click="submitForm">{{ t('template.buttons.confirm') }}</VaButton>
        </div>
      </VaForm>
    </VaModal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useToast, defineVaDataTableColumns, useForm } from 'vuestic-ui'
import { add, list, update, remove } from "@/api/ads/template"
import { parseTime, addDateRange } from "@/utils/subdigi"
import { useTransition } from '@/composables/useTransition.js'
import { useModal } from 'vuestic-ui'
import { useI18n } from 'vue-i18n' // 导入 i18n

// 使用 i18n
const { t } = useI18n()

// 使用过渡动画功能
const {
  isExpanded: isFilterExpanded,
  contentRef: filterContent,
  isMobileView,
  toggle: toggleFilter,
  getContentStyles,
  init: initTransition,
  cleanup: cleanupTransition
} = useTransition({
  defaultExpanded: true,  // PC端默认展开
  breakpoint: 992,        // 小于992px为移动设备
  animationDuration: 300  // 动画持续时间
})

// 初始化toast通知
const { init: toast } = useToast()
const formRef = ref(null)
const { confirm } = useModal()

// 表格列定义
const columns = defineVaDataTableColumns([
  { key: 'name', label: t('template.table.name'), sortable: false },
  { key: 'remark', label: t('template.table.description') },
  { key: 'creator', label: t('template.table.creator') },
  { key: 'createTime', label: t('template.table.create_time'), sortable: false },
  { key: 'updator', label: t('template.table.updator') },
  { key: 'updateTime', label: t('template.table.update_time'), sortable: false },
  { key: 'actions', label: t('template.table.actions') }
])

// 加载状态
const loading = ref(false)

// 是否显示搜索区域
const showSearch = ref(true)

// 总条数
const total = ref(0)

// 模板列表数据
const listData = ref([])

// 弹出层标题
const title = ref('')

// 是否显示弹出层
const open = ref(false)

// 日期范围
const dateRange = ref([])

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 20,
  name: undefined
})

// 表单参数
const form = ref({
  id: undefined,
  name: undefined,
  remark: undefined
})

// 计算总页数
const totalPages = computed(() => Math.ceil(total.value / queryParams.value.pageSize))

/** 获取模板列表 */
const getList = () => {
  loading.value = true
  list(addDateRange(queryParams.value, dateRange.value))
    .then(response => {
      listData.value = response.data.pageList
      total.value = response.data.total
    })
    .catch(error => {
      console.error(t('template.messages.get_list_error'), error)
      toast({
        message: t('template.messages.get_list_error'),
        color: 'danger'
      })
    })
    .finally(() => {
      loading.value = false
    })
}

// 取消按钮
const cancel = () => {
  open.value = false
  reset()
}

// 表单重置
const reset = () => {
  form.value = {
    id: undefined,
    name: undefined,
    remark: undefined
  }
  if (formRef.value) {
    formRef.value.reset()
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = []
  queryParams.value = {
    pageNum: 1,
    pageSize: 20,
    name: undefined
  }
  handleQuery()
}

// 修改按钮操作
const updateHandler = (row) => {
  reset()
  form.value = { ...row }
  open.value = true
  title.value = t('template.modal.edit_title')
}

// 删除按钮操作
const removeHandler = (row) => {
  confirm(t('template.confirm.delete', { name: row.name })).then(() => {
    remove(row.id).then(() => {
      getList()
      toast({
        message: t('template.messages.delete_success'),
        color: 'success'
      })
    })
  })
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset()
  open.value = true
  title.value = t('template.modal.add_title')
}

/** 提交按钮 */
const submitForm = () => {
  if (formRef.value.validate()) {
    if (form.value.id !== undefined) {
      update(form.value).then(() => {
        toast({
          message: t('template.messages.edit_success'),
          color: 'success'
        })
        open.value = false
        getList()
      })
    } else {
      add(form.value).then(() => {
        toast({
          message: t('template.messages.add_success'),
          color: 'success'
        })
        open.value = false
        getList()
      })
    }
  }
}

// 生命周期钩子
onMounted(() => {
  getList()
  initTransition() // 初始化过渡动画相关逻辑
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  cleanupTransition() // 清理过渡动画相关事件监听
})
</script>

<style>
.primary-label {
  color: var(--va-primary);
  font-size: 12px;
  font-weight: 600;
}

.filter-label {
  color: var(--va-primary);
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
}

/* 筛选表单布局 */
.filter-form {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* 筛选网格布局 */
.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 12px;
  margin-top: 10px;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 8px;
}

.filter-item-header {
  display: flex;
  align-items: center;
}

.filter-input {
  width: 100%;
}

/* 操作按钮区域 */
.filter-actions {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 10px;
  margin: 8px 0;
}

.filter-actions1 {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  gap: 10px;
  margin: 8px 0;
}

/* 响应式样式 */
@media (max-width: 991px) {
  .filter-toggle {
    display: flex;
  }

  .filter-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 2fr));
  }
}

@media (min-width: 992px) {
  .filter-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }
}
</style>
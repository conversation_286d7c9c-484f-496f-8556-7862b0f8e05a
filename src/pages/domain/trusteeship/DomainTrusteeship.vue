<template>
  <VaCard class="filter-card">
    <!-- 筛选区域标题和控制按钮 -->
    <div class="filter-header flex justify-between items-center pb-2">
      <div class="flex items-center gap-2">
        <VaIcon name="mso-filter_list" color="primary" />
        <h2 class="text-lg font-medium">{{ t('trusteeship.filter.title') }}</h2>
      </div>
      <div class="flex gap-2">
        <!-- 仅在移动设备和平板上显示展开/折叠按钮 -->
        <VaButton v-if="isMobileView" preset="secondary" border-color="primary" size="small" class="filter-toggle"
          @click="toggleFilter" :icon="isFilterExpanded ? 'mso-expand_less' : 'mso-expand_more'"
          :aria-label="isFilterExpanded ? t('trusteeship.filter.collapse') : t('trusteeship.filter.expand')">
          {{ isFilterExpanded ? t('trusteeship.filter.collapse_btn') : t('trusteeship.filter.expand_btn') }}
        </VaButton>
      </div>
    </div>

    <!-- 筛选区域内容 - 使用JS动画 -->
    <div ref="filterContent" class="filter-content" :style="{
      ...getContentStyles(),
    }">
      <!-- 筛选表单 -->
      <div class="filter-form" v-show="isFilterExpanded">
        <!-- 筛选条件网格 -->
        <div class="filter-grid">
          <!-- 站点域名筛选 -->
          <div class="filter-item">
            <div class="filter-item-header">
              <label class="filter-label">{{ t('trusteeship.domain.label') }}</label>
            </div>
            <VaInput v-model="queryParams.name" :placeholder="t('trusteeship.domain.placeholder')" class="filter-input" />
          </div>

          <!-- 托管状态筛选 -->
          <div class="filter-item">
            <div class="filter-item-header">
              <label class="filter-label">{{ t('trusteeship.status.label') }}</label>
            </div>
            <VaSelect v-model="queryParams.status" :placeholder="t('trusteeship.status.placeholder')" :options="domainStatus" text-by="text"
              value-by="value" class="filter-input" />
          </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="filter-actions mt-4">
          <VaButton color="primary" icon="mso-search" @click="handleQuery">{{ t('trusteeship.buttons.search') }}</VaButton>
          <VaButton preset="secondary" border-color="primary" icon="mso-refresh" @click="resetQuery">{{ t('trusteeship.buttons.reset') }}</VaButton>
          <VaButton preset="secondary" border-color="primary" icon="mso-add" @click="handleAdd">{{ t('trusteeship.buttons.add') }}</VaButton>
          <VaButton preset="secondary" border-color="primary" icon="mso-refresh" @click="handleCache">{{ t('trusteeship.buttons.clear_cache') }}</VaButton>
        </div>
      </div>
    </div>
  </VaCard>

  <VaDataTable v-loading="loading" :items="listData" :columns="columns" @sort-change="handleSortChange"
    @row-click="handleSelectionChange" class="mt-4" hoverable striped>
    <VaDataTableColumn label="ID" field="id" width="80px" />

    <template #cell(name)="{ rowData }">
      <a :href="`https://${rowData.name}`" target="_blank">
        <span style="color: #007bff;">{{ rowData.name }}</span>
      </a>
    </template>

    <VaDataTableColumn label="状态" field="status" width="90px" />

    <template #cell(dnsRecords)="{ rowData }">
      <span v-if="rowData.dnsRecords !== undefined">{{ JSON.parse(rowData.dnsRecords)[0].content }}</span>
      <span v-if="rowData.dnsRecords == undefined">--</span>
      <!-- <div v-for="(record, index) in trusteeshipValue(rowData.dnsRecords)" :key="index">
          <span v-if="record.dns_name === '@'">{{ record.content }}</span>
        </div> -->
    </template>

    <VaDataTableColumn label="创建者" field="creator" width="120px" />

    <template #cell(createTime)="{ rowData }">
      <span>{{ parseTime(rowData.createTime) }}</span>
    </template>

    <template #cell(updator)="{ rowData }">
      <span v-if="rowData.updator !== undefined">{{ rowData.updator }}</span>
      <span v-if="rowData.updator == undefined">--</span>
    </template>

    <template #cell(updateTime)="{ rowData }">
      <span v-if="rowData.updateTime !== undefined">{{ parseTime(rowData.updateTime) }}</span>
      <span v-if="rowData.updateTime == undefined">--</span>
    </template>

    <template #cell(action)="{ rowData }">
      <div class="flex gap-2">
        <VaButton preset="secondary" size="small" icon="mso-refresh" @click="updateCFDetail(rowData.zoneId)">{{ t('trusteeship.buttons.update') }}
        </VaButton>
        <VaButton preset="secondary" size="small" icon="mso-edit" @click="handleUpdate(rowData)">{{ t('trusteeship.buttons.edit') }}</VaButton>
      </div>
    </template>
  </VaDataTable>

  <!-- 分页 -->
  <div class="flex justify-end mt-4 gap-2 filter-actions1 mx-4">
    <div>
      <b>{{ total }} {{ t('trusteeship.table.results') }}</b>
      {{ t('trusteeship.table.per_page') }}
      <VaSelect v-model="queryParams.pageSize" class="!w-20 inline-block" :options="[10, 20, 50, 100]"
        @update:model-value="getList" />
    </div>
    <VaPagination v-model="queryParams.pageNum" :pages="total" :visible-pages="5" buttons-preset="secondary" gapped
      border-color="primary" class="justify-center sm:justify-start" @update:modelValue="getList" />
  </div>

  <!-- 添加域名托管对话框 -->
  <VaModal v-model="openAdd" :title="t('trusteeship.modal.add_title')" size="medium" hide-default-actions close-button mobile-fullscreen>
    <VaForm ref="formAddRef" :model="addForm" :rules="rules" class="p-4 md:px-6">
      <!-- 域名选择区域 -->
      <div class="mb-4">
        <VaFormItem :label="t('trusteeship.domain.label')" class="mb-0">
          <VaSelect v-model="addForm.name" v-model:search="searchName" :options="diffDomainList"
            :text-by="option => option.value" :value-by="option => option.value" autocomplete :placeholder="t('trusteeship.modal.domain_select')"
            @update:model-value="formSelectDomain" class="w-full" />
        </VaFormItem>
      </div>

      <!-- 加载状态 -->
      <!-- <VaInnerLoading :loading="dnsLoad" class="my-4" /> -->

      <!-- DNS记录区域 -->
      <div v-if="form_show" class="space-y-4">
        <VaFormItem v-loading="dnsLoad" :label="t('trusteeship.dns.label')" class="mb-0">
          <div v-if="dnsHint" class="text-red-500 mb-2 text-sm">
            {{ t('trusteeship.modal.auto_config') }}
          </div>

          <!-- DNS记录列表 -->
          <div class="space-y-3">
            <div v-for="(item, index) in dnsRecordList" :key="index"
              class="flex flex-col md:flex-row gap-3 p-3 bg-gray-50 rounded-lg">
              <div class="flex-1 grid grid-cols-1 md:grid-cols-3 gap-3">
                <VaInput :placeholder="t('trusteeship.dns.type')" v-model="item.dnsType" disabled :label="t('trusteeship.dns.type')" class="w-full" />
                <VaInput :placeholder="t('trusteeship.dns.subdomain')" v-model="item.dnsName" :label="t('trusteeship.dns.subdomain')" class="w-full" />
                <VaInput :placeholder="t('trusteeship.dns.ip')" v-model="item.content" @blur="validateIP(item, index)" :label="t('trusteeship.dns.ip')"
                  class="w-full" />
              </div>

              <div class="flex items-center justify-end md:justify-start">
                <VaButton preset="secondary" icon="mso-delete" size="small" color="danger" @click="delDns(item, index)"
                  class="self-center">
                  {{ t('trusteeship.dns.delete') }}
                </VaButton>
              </div>
            </div>
          </div>

          <!-- 添加DNS记录按钮 -->
          <div class="flex justify-end mt-4">
            <VaButton icon="mso-add" @click="addDns">{{ t('trusteeship.buttons.add_dns') }}</VaButton>
          </div>
        </VaFormItem>
      </div>

      <!-- 操作描述区域 -->
      <div v-if="form_show" class="mt-4">
        <VaFormItem :label="t('trusteeship.form.remark')" required class="mb-0">
          <VaInput v-model="addForm.remark" :placeholder="t('trusteeship.form.remark_placeholder')" class="w-full" />
        </VaFormItem>
      </div>

      <!-- 操作按钮区域 -->
      <div class="flex flex-wrap justify-end gap-3 mt-6">
        <VaButton v-if="form_show" preset="primary" @click="submitAddForm" :loading="confrimData"
          :disabled="confrimData">
          {{ t('trusteeship.buttons.confirm') }}
        </VaButton>
        <VaButton @click="cancelAdd">{{ t('trusteeship.buttons.cancel') }}</VaButton>
      </div>
    </VaForm>
  </VaModal>

  <!-- 修改域名托管对话框 -->
  <VaModal v-model="openEdit" :title="t('trusteeship.modal.edit_title')" size="medium" hide-default-actions close-button mobile-fullscreen>
    <VaForm ref="formEditRef" :model="changeForm" :rules="rules" class="p-4 md:px-6">
      <!-- 域名区域 -->
      <div class="mb-4">
        <VaFormItem :label="t('trusteeship.domain.label')" class="mb-0">
          <VaInput v-model="changeForm.name" class="w-full" />
        </VaFormItem>
      </div>

      <!-- DNS记录区域 -->
      <div class="space-y-4">
        <VaFormItem :label="t('trusteeship.dns.label')" class="mb-0">
          <div class="space-y-3">
            <div v-for="(item, index) in dnsRecordList" :key="index"
              class="flex flex-col md:flex-row gap-3 p-3 bg-gray-50 rounded-lg">
              <div class="flex-1 grid grid-cols-1 md:grid-cols-3 gap-3">
                <VaInput :placeholder="t('trusteeship.dns.type')" v-model="item.dns_type" disabled :label="t('trusteeship.dns.type')" class="w-full" />
                <VaInput :placeholder="t('trusteeship.dns.subdomain')" v-model="item.dns_name" :label="t('trusteeship.dns.subdomain')" class="w-full" />
                <VaInput :placeholder="t('trusteeship.dns.ip')" v-model="item.content" @blur="validateIP(item, index)" :label="t('trusteeship.dns.ip')"
                  class="w-full" />
              </div>

              <div class="flex items-center justify-end md:justify-start">
                <VaButton preset="secondary" icon="mso-delete" size="small" color="danger" @click="delDns(item, index)"
                  class="self-center">
                  {{ t('trusteeship.dns.delete') }}
                </VaButton>
              </div>
            </div>
          </div>

          <!-- 添加DNS记录按钮 -->
          <div class="flex justify-end mt-4">
            <VaButton icon="mso-add" @click="addDns">{{ t('trusteeship.buttons.add_dns') }}</VaButton>
          </div>
        </VaFormItem>
      </div>

      <!-- 操作描述区域 -->
      <div class="mt-4">
        <VaFormItem :label="t('trusteeship.form.remark')" required class="mb-0">
          <VaInput v-model="changeForm.remark" :placeholder="t('trusteeship.form.remark_placeholder')" class="w-full" />
        </VaFormItem>
      </div>

      <!-- 操作按钮区域 -->
      <div class="flex flex-wrap justify-end gap-3 mt-6">
        <VaButton preset="secondary" @click="cancelEdit">{{ t('trusteeship.buttons.cancel') }}</VaButton>
        <VaButton preset="primary" @click="submitEditForm" :loading="confrimData" :disabled="confrimData">
          {{ t('trusteeship.buttons.confirm') }}
        </VaButton>
      </div>
    </VaForm>
  </VaModal>

</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { list, diffDomain, clearCache, updateCF, getDns, add, update, deleteDns, updateTrusteeship } from "@/api/domain/cloudflare";
import { parseTime } from "@/utils/subdigi";
import { VaInput, useToast } from 'vuestic-ui';
import { useTransition } from '@/composables/useTransition.js'
import { useI18n } from 'vue-i18n'; // 导入 i18n

// 使用 i18n
const { t } = useI18n();

// 使用过渡动画功能
const {
  isExpanded: isFilterExpanded,
  contentRef: filterContent,
  isMobileView,
  toggle: toggleFilter,
  getContentStyles,
  init: initTransition,
  cleanup: cleanupTransition
} = useTransition({
  defaultExpanded: true,  // PC端默认展开
  breakpoint: 992,        // 小于992px为移动设备
  animationDuration: 300  // 动画持续时间
})

const { init: toast } = useToast()

// 遮罩层
const loading = ref(true);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(20); // 每页的数据条数
// 域名列表
const listData = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层 - 通用
const open = ref(false);
// 是否显示添加弹出层
const openAdd = ref(false);
// 是否显示修改弹出层
const openEdit = ref(false);
// 日期范围
const dateRange = ref([]);
const defaultSort = reactive({ prop: 'createTime', order: 'descending' });

const searchName = ref('');

// 表格列定义
const columns = [
  { key: 'id', label: t('trusteeship.table.id'), field: 'id', sortable: false, width: '80px' },
  { key: 'name', label: t('trusteeship.table.domain'), field: 'name', sortable: false, width: '180px' },
  { key: 'status', label: t('trusteeship.table.status'), field: 'status', width: '90px' },
  { key: 'dnsRecords', label: t('trusteeship.table.dns_records'), field: 'dnsRecords' },
  { key: 'creator', label: t('trusteeship.table.creator'), field: 'creator', width: '120px' },
  { key: 'createTime', label: t('trusteeship.table.create_time'), field: 'createTime', sortable: false, width: '200px' },
  { key: 'updator', label: t('trusteeship.table.updator'), field: 'updator' },
  { key: 'updateTime', label: t('trusteeship.table.update_time'), field: 'updateTime' },
  { key: 'action', label: t('trusteeship.table.action'), field: 'action', width: '150px' }
];

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 20,
  status: undefined,
  name: undefined
});

// 表单参数
const form = ref({
  id: undefined,
  name: undefined,
  remark: undefined,
  zoneId: undefined
});

// 表单校验
const rules = {};

// 域名状态选项
const domainStatus = [
  { value: "active", text: t('trusteeship.status.active') },
  { value: "pending", text: t('trusteeship.status.pending') },
  { value: "moved", text: t('trusteeship.status.moved') }
];

const diffDomainList = ref([]);
const form_show = ref(false);
const dnsRecordList = ref([]);
const dnsHint = ref(false);
const dnsLoad = ref(true);
const confrimData = ref(false);
const domainValue = ref(0);
const ids = ref([]);

// 表单引用
const queryForm = ref();
const formRef = ref();
const formAddRef = ref();
const formEditRef = ref();

const changeForm = ref({
  id: undefined,
  name: undefined,
  remark: undefined,
  zoneId: undefined
});

// 页码大小改变
const handleSizeChange = (val) => {
  currentPage.value = 1;
  pageSize.value = val;
};

// 当前页改变时触发 跳转其他页
const handleCurrentChange = (val) => {
  currentPage.value = val;
};

// 默认排序事件
const getDefaultSort = () => {
  queryParams.orderByColumn = defaultSort.prop;
  queryParams.isAsc = defaultSort.order;
  getList();
};

// 排序触发事件
const handleSortChange = (column) => {
  queryParams.orderByColumn = column.prop;
  queryParams.isAsc = column.order;
  getList();
};

// 获取域名列表
const getList = () => {
  loading.value = true;
  list(addDateRange(queryParams.value)).then(response => {
    listData.value = response.data.pageList;
    total.value = response.data.total;
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
};

// 获取系统没有托管的域名列表
const getNoTrustList = () => {
  diffDomain().then(response => {
    diffDomainList.value = response.data.map(domain => domain.value);
    domainValue.value = diffDomainList.value.length;
  });
};

// 清理缓存
const handleCache = () => {
  clearCache().then(response => {
    // 使用Vuestic UI的通知组件
    // this.$vaToast.init({ message: '所有站点的CDN缓存清除成功!! 预计生效时间在10s后', color: 'success' });
    toast({ message: t('trusteeship.messages.cache_cleared'), color: 'success' });
  });
};

// 更新CF上的配置信息
const updateCFDetail = (zoneId) => {
  loading.value = true;
  updateCF(zoneId).then(response => {
    // 使用Vuestic UI的通知组件
    // this.$vaToast.init({ message: '更新配置数据成功', color: 'success' });
    toast({ message: t('trusteeship.messages.update_success'), color: 'success' });
    getList();
  }).catch(() => {
    loading.value = false;
  });
};

// 取消按钮 - 通用
const cancel = () => {
  open.value = false;
  reset();
  getList();
};

// 取消添加按钮
const cancelAdd = () => {
  openAdd.value = false;
  reset();
  getList();
};

// 取消修改按钮
const cancelEdit = () => {
  openEdit.value = false;
  reset();
  getList();
};

// 表单重置
const reset = () => {
  form.value.id = undefined;
  form.value.name = undefined;
  form.value.remark = undefined;
  form.value.zoneId = undefined;

  // 重置表单校验
  if (formRef.value) {
    formRef.value.resetFields();
  }
  if (formAddRef.value) {
    addForm.value.name = undefined;
    addForm.value.remark = undefined;
  }
  if (formEditRef.value) {
    formEditRef.value.resetFields();
  }

  form_show.value = false;
  dnsRecordList.value = [];
};

// 新增按钮操作
const handleAdd = () => {
  reset();
  openAdd.value = true;
  title.value = t('trusteeship.modal.add_title');
};

// 修改按钮操作
const handleUpdate = (row) => {
  reset();
  dnsLoad.value = true;
  //   Object.assign(form, row);
  form_show.value = true;
  dnsRecordList.value.push(
    JSON.parse(row.dnsRecords)[0]
  );
  //   getDns(row.zoneId).then(response => {
  //     dnsRecordList.value = response.data;
  //     dnsLoad.value = false;
  //   });
  openEdit.value = true;
  changeForm.value = row;
  title.value = t('trusteeship.modal.edit_title');
};

// 提交按钮 - 通用
const submitForm = () => {
  formRef.value?.validate(valid => {
    if (valid) {
      confrimData.value = true;
      if (form.value.id != undefined) {
        const updateData = {
          zoneId: form.value.zoneId,
          dnsRecords: dnsRecordList.value,
          remark: form.value.remark
        };
        update(updateData).then(response => {
          toast({ message: t('trusteeship.messages.edit_success'), color: 'success' });
          open.value = false;
          confrimData.value = false;
          getList();
        }).catch(() => {
          confrimData.value = false;
        });
      } else {
        const addData = {
          name: form.value.name,
          dnsRecords: dnsRecordList.value,
          remark: form.value.remark
        };
        add(addData).then(response => {
          toast({ message: t('trusteeship.messages.add_success'), color: 'success' });
          open.value = false;
          confrimData.value = false;
          getList();
        }).catch(() => {
          confrimData.value = false;
        });
      }
    }
  });
};

// 提交添加按钮
const submitAddForm = () => {
  confrimData.value = true;
  const submitData = {
    name: addForm.value.name,
    dnsRecords: JSON.stringify(dnsRecordList.value),
    remark: addForm.value.remark
  };
  add(submitData).then(response => {
    toast({ message: t('trusteeship.messages.add_success'), color: 'success' });
    openAdd.value = false;
    confrimData.value = false;
    getList();
  }).catch(() => {
    confrimData.value = false;
  });
}

// 提交修改按钮
const submitEditForm = () => {
  confrimData.value = true;
  changeForm.value.dnsRecords = JSON.stringify(dnsRecordList.value);
  updateTrusteeship(changeForm.value).then(response => {
    // 使用Vuestic UI的通知组件
    // this.$vaToast.init({ message: '修改成功', color: 'success' });
    toast({ message: t('trusteeship.messages.edit_success'), color: 'success' });
    openEdit.value = false;
    confrimData.value = false;
    getList();
  }).catch(() => {
    confrimData.value = false;
  });
};

// 选择行触发事件
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

// 重置按钮操作
const resetQuery = () => {
  queryParams.value.status = undefined;
  queryParams.value.name = undefined;
  // getList();
  handleQuery();
};

// 添加日期范围
const addDateRange = (params, dateRange, propName) => {
  const search = { ...params };
  if (dateRange && dateRange.length === 2) {
    search.params = typeof search.params === 'object' && search.params !== null && !Array.isArray(search.params) ? search.params : {};
    search.params['beginTime'] = dateRange[0];
    search.params['endTime'] = dateRange[1];
  }
  return search;
};

// 自动完成输入
const queryDiffDomain = (queryString, cb) => {
  const results = queryString
    ? diffDomainList.value.filter(item => item.toLowerCase().indexOf(queryString.toLowerCase()) === 0)
    : diffDomainList.value;
  cb(results);
};

// 选择域名
const formSelectDomain = (value) => {
  dnsLoad.value = true;
  form_show.value = true;

  getDns(value).then(response => {
    if (response.data !== null && response.data !== '') {
      dnsRecordList.value = response.data

    } else {
      dnsHint.value = true;
      dnsRecordList.value = [
        { dns_type: "A", dns_name: value, content: "67.220.85.68", dns_proxied: true }
      ]

    }
    // dnsRecordList.value = response.data;
    // dnsHint.value = true;
    dnsLoad.value = false;
  }).catch(() => {
    dnsLoad.value = false;
  });
};
const addForm = ref({
  name: undefined,
  remark: undefined
});

// 添加DNS记录
const addDns = () => {
  dnsRecordList.value.push({
    dns_type: "A",
    dns_name: "@",
    content: ""
  });
};

// 删除DNS记录
const delDns = (item, index) => {
  if (changeForm.value.id != undefined) {
    // 这里是删除已经存在的记录
    deleteDns(changeForm.value.zoneId, item.id).then(() => {
      dnsRecordList.value.splice(index, 1);
    });
  } else {
    dnsRecordList.value.splice(index, 1);
  }
};

// 验证IP
const validateIP = (item, index) => {
  // 这里可以添加IP验证逻辑
};

// 处理DNS记录显示
const trusteeshipValue = (dnsRecords) => {
  if (dnsRecords && dnsRecords.length > 0) {
    return JSON.parse(dnsRecords);
  }
  return [];
};

// 生命周期钩子
onMounted(() => {
  getDefaultSort();
  getNoTrustList();
  initTransition(); // 初始化过渡动画相关逻辑
});

// 移除事件监听
onUnmounted(() => {
  cleanupTransition(); // 清理过渡动画相关事件监听
});
</script>

<style>
.app-container {
  padding: 20px;
}



.primary-label {
  color: var(--va-primary);
  font-size: 12px;
  font-weight: 600;
}

.filter-label {
  color: var(--va-primary);
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
}

/* 筛选表单布局 */
.filter-form {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* 筛选网格布局 */
.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 12px;
  margin-top: 10px;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 8px;
}

.filter-item-header {
  display: flex;
  align-items: center;
}

.filter-input {
  width: 100%;
}

/* 操作按钮区域 */
.filter-actions {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 10px;
  margin: 8px 0;
}
.filter-actions1 {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  gap: 10px;
  margin: 8px 0;
}

/* 表单区域样式 */
.form-section {
  margin-bottom: 16px;
}

.form-section-title {
  color: var(--va-primary);
  font-size: 14px;
  font-weight: 500;
  display: block;
}

/* DNS记录容器样式 */
.dns-records-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 8px;
  max-height: 400px;
  overflow-y: auto;
  padding: 4px;
}

.dns-record-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;
  border: 1px solid var(--va-background-border);
  border-radius: 4px;
  background-color: var(--va-background-element);
}

.dns-record-inputs {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 8px;
  width: 100%;
}

.dns-input {
  width: 100%;
}

.dns-record-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 4px;
}

/* 响应式样式 */
@media (max-width: 991px) {
  .filter-toggle {
    display: flex;
  }

  .filter-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 2fr));
  }
}

@media (min-width: 992px) {
  .filter-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }

  .dns-record-item {
    flex-direction: row;
    align-items: flex-end;
  }

  .dns-record-inputs {
    flex: 1;
  }

  .dns-record-actions {
    margin-top: 0;
    margin-left: 8px;
  }
}

:deep(.va-radio) {
  flex-wrap: wrap;
}

/* 表单分区标题 */
.form-section {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  width: 100%;
}
</style>
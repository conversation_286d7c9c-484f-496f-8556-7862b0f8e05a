<template>



  <VaCard class="filter-card">
    <!-- 筛选区域标题和控制按钮 -->
    <div class="filter-header flex justify-between items-center pb-2">
      <div class="flex items-center gap-2">
        <VaIcon name="mso-filter_list" color="primary" />
        <h2 class="text-lg font-medium">{{ t('domain.filter.title') }}</h2>
      </div>
      <div class="flex gap-2">
        <!-- 仅在移动设备和平板上显示展开/折叠按钮 -->
        <VaButton v-if="isMobileView" preset="secondary" border-color="primary" size="small" class="filter-toggle"
          @click="toggleFilter" :icon="isFilterExpanded ? 'mso-expand_less' : 'mso-expand_more'"
          :aria-label="isFilterExpanded ? t('domain.filter.collapse') : t('domain.filter.expand')">
          {{ isFilterExpanded ? t('domain.filter.collapseBtn') : t('domain.filter.expandBtn') }}
        </VaButton>
      </div>
    </div>

    <!-- 筛选区域内容 - 使用JS动画 -->
    <div ref="filterContent" class="filter-content" :style="{
      ...getContentStyles(),
    }">
      <!-- 筛选表单 -->
      <div class="filter-form" v-show="isFilterExpanded">
        <!-- 筛选条件网格 -->
        <div class="filter-grid">
          <!-- 日期范围筛选 -->
          <div class="filter-item">
            <div class="filter-item-header">
              <div class="flex items-center">
                <label class="filter-label">{{ t('domain.filter.dateRange') }}</label>
                <VaButtonDropdown preset="plain">
                  <div class="flex items-center flex-col gap-2 date-shortcuts">
                    <VaButton size="small" preset="secondary" border-color="primary" @click="selectLastThreeDays">{{
                      t('domain.filter.lastThreeDays') }}
                    </VaButton>
                    <VaButton size="small" preset="secondary" border-color="primary" @click="selectLastWeek">{{
                      t('domain.filter.lastWeek') }}
                    </VaButton>
                    <VaButton size="small" preset="secondary" border-color="primary" @click="selectLastMonth">{{
                      t('domain.filter.lastMonth') }}
                    </VaButton>
                  </div>
                </VaButtonDropdown>
              </div>
            </div>
            <Datepicker v-model="dateRange" range locale="en-US" format="yyyy/MM/dd" :enable-time-picker="false"
              auto-apply :placeholder="t('domain.filter.selectDateRange')" week-numbers="iso" week-num-name="We"
              @update:model-value="onDateRangeChange" :now-button-label="t('domain.filter.today')" :dark="isDark"
              :clearable="false" class="filter-input" />
          </div>

          <!-- 域名筛选 -->
          <div class="filter-item">
            <div class="filter-item-header">
              <label class="filter-label">{{ t('domain.filter.domain') }}</label>
            </div>
            <VaInput v-model="queryParams.domain" :placeholder="t('domain.filter.inputDomain')" class="filter-input" />
          </div>

          <!-- 创建者筛选 -->
          <div class="filter-item">
            <div class="filter-item-header">
              <label class="filter-label">{{ t('domain.filter.creator') }}</label>
            </div>
            <VaInput v-model="queryParams.creator" :placeholder="t('domain.filter.inputCreator')"
              class="filter-input" />
          </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="filter-actions mt-4">
          <VaButton color="primary" icon="mso-search" @click="handleQuery">{{ t('domain.buttons.search') }}</VaButton>
          <VaButton preset="secondary" border-color="primary" icon="mso-refresh" @click="reset">{{
            t('domain.buttons.reset') }}</VaButton>
          <VaButton preset="secondary" border-color="primary" icon="mso-add" @click="handleAdd">{{
            t('domain.buttons.register') }}</VaButton>
          <VaButton preset="secondary" border-color="primary" icon="mso-refresh" @click="getAllDomainList">{{
            t('domain.buttons.update') }}
          </VaButton>
        </div>
      </div>
    </div>
  </VaCard>

  <VaDataTable v-loading="loading" :items="listData" :columns="columns" ref="tables" :default-sort="defaultSort"
    @sort-change="handleSortChange" class="mt-4" hoverable striped>
    <VaDataTableColumn :label="t('domain.table.id')" field="id" width="80px" sortable />

    <template #cell(domain)="{ rowData }">
      <a :href="`https://www.${rowData.domain}`" target="_blank">
        <span style="color: #007bff;">{{ rowData.domain }}</span>
      </a>
    </template>

    <VaDataTableColumn :label="t('domain.table.registPlat')" field="registPlat" width="120px" />
    <VaDataTableColumn :label="t('domain.table.nameServer')" field="nameServer" width="120px" />
    <VaDataTableColumn :label="t('domain.table.creator')" field="creator" width="120px" />

    <template #cell(createTime)="{ rowData }">
      <span>{{ parseTime(rowData.createTime) }}</span>
    </template>

    <template #cell(registTime)="{ rowData }">
      <span>{{ parseTime(rowData.registTime) }}</span>
    </template>

    <template #cell(expireTime)="{ rowData }">
      <span>{{ parseTime(rowData.expireTime) }}</span>
    </template>

    <template #cell(updator)="{ rowData }">
      <span v-if="rowData.updator !== undefined">{{ parseTime(rowData.updator) }}</span>
      <span v-if="rowData.updator == undefined">--</span>
    </template>

    <template #cell(updateTime)="{ rowData }">
      <span v-if="rowData.updateTime !== undefined">{{ parseTime(rowData.updateTime) }}</span>
      <span v-if="rowData.updateTime == undefined">--</span>
    </template>
  </VaDataTable>

  <div class="flex justify-end mt-4 gap-2 filter-actions1 mx-4">
    <div>
      <b>{{ total }} {{ t('domain.pagination.results') }}</b>
      {{ t('domain.pagination.perPage') }}
      <VaSelect v-model="queryParams.pageSize" class="!w-20 inline-block" :options="[10, 20, 50, 100]"
        @update:model-value="handleQuery" />
    </div>

    <VaPagination v-model="queryParams.pageNum" :pages="totalPages" :visible-pages="5" buttons-preset="secondary" gapped
      border-color="primary" class="justify-center sm:justify-start" @update:modelValue="handleQuery" />
  </div>

  <!-- 购买新的域名 -->
  <VaModal v-model="open" :title="title" size="medium" hide-default-actions close-button>
    <VaForm ref="formRef" :model="registerForm" :rules="rules" class="px-4">
      <!-- 域名搜索区域 -->
      <div class="form-section">
        <label class="form-section-title mb-2">{{ t('domain.modal.register.searchTitle') }}</label>
        <div class="flex items-center w-full gap-2">
          <VaInput :placeholder="t('domain.modal.register.domainPlaceholder')" v-model="registerForm.domain"
            class="flex-grow flex-1" :rules="[v => !!v || t('domain.modal.register.domainRequired')]" />
          <VaButton class="whitespace-nowrap" icon="mso-search" @click="searchDomain" :loading="search">

          </VaButton>
        </div>
      </div>

      <!-- 域名描述区域 - 仅在搜索后显示 -->
      <div v-if="show_form" class="form-section mt-4">
        <VaTextarea :label="t('domain.modal.register.description')" v-model="registerForm.remark" rows="4"
          :placeholder="t('domain.modal.register.descPlaceholder')" class="w-full" />
      </div>

      <!-- 操作按钮区域 -->
      <div class="flex flex-wrap justify-end gap-2 mt-6">
        <VaButton v-if="show_form && registerForm.id == undefined" preset="primary" @click="submitRegisterForm"
          :loading="register" :disabled="register">
          {{ t('domain.buttons.register') }}
        </VaButton>
        <VaButton v-if="registerForm.id !== undefined" preset="primary" @click="updateDomainNs">
          {{ t('domain.buttons.modify') }}
        </VaButton>
        <VaButton @click="cancel">{{ t('domain.buttons.cancel') }}</VaButton>
      </div>
    </VaForm>
  </VaModal>

  <!-- 导入新域名 -->
  <VaModal v-model="domain_open" :title="t('domain.modal.import.title')" size="medium" hide-default-actions
    close-button>
    <div class="p-4">
      <!-- 全选区域 -->
      <div class="form-section">
        <div class="flex items-center gap-3">
          <VaCheckbox v-model="checkAll" :indeterminate="isIndeterminate"
            @update:modelValue="val => handleCheckAllChange(val)">
          </VaCheckbox>
          <label class="form-section-title">{{ t('domain.modal.import.selectAll') }}</label>
        </div>
      </div>

      <!-- 域名选择区域 -->
      <div class="domain-checkbox-container mt-4">
        <VaCheckbox v-for="item in checkedDomain" :key="item" :model-value="checkedDomains.includes(item)"
          @update:modelValue="val => {
            if (val) {
              if (!checkedDomains.includes(item)) {
                checkedDomains.push(item);
              }
            } else {
              const index = checkedDomains.indexOf(item);
              if (index !== -1) {
                checkedDomains.splice(index, 1);
              }
            }
            handleCheckedDomainsChange(checkedDomains);
          }" :label="item" class="domain-checkbox">
        </VaCheckbox>
      </div>

      <!-- 选择计数和提示 -->
      <div class="mt-4 text-sm" v-if="checkedDomains.length > 0">
        <span class="text-primary font-medium">{{ t('domain.modal.import.selected', { count: checkedDomains.length })
        }}</span>
      </div>

      <!-- 操作按钮区域 -->
      <div class="flex flex-wrap justify-end gap-2 mt-6">
        <VaButton @click="cancelDomain">{{ t('domain.buttons.cancel') }}</VaButton>
        <VaButton preset="primary" @click="submitDomain" :disabled="checkedDomains.length === 0">
          {{ t('domain.buttons.submit') }}
        </VaButton>
      </div>
    </div>
  </VaModal>

</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import {
  list, checkDomain, registerDomain,
  getNewsDomainList, importDomain, deleteDomain, updateNs
} from "@/api/domain/nameslio";
import Datepicker from '@vuepic/vue-datepicker'
import { useUserStore } from '@/stores/user-store'
import { useTransition } from '@/composables/useTransition.js'
import { useData } from '@/composables/useData.js'
import { computed } from 'vue';
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const sumPage = ref(0);
const userStore = useUserStore()
const isDark = ref(userStore.theme === 'dark')
// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 20,
  domain: undefined,
  creator: undefined,
  startTime: undefined,
  endTime: undefined
});

// 使用过渡动画功能
const {
  isExpanded: isFilterExpanded,
  contentRef: filterContent,
  isMobileView,
  toggle: toggleFilter,
  getContentStyles,
  init: initTransition,
  cleanup: cleanupTransition
} = useTransition({
  defaultExpanded: true,  // PC端默认展开
  breakpoint: 992,        // 小于992px为移动设备
  animationDuration: 300  // 动画持续时间
})

// 使用日期处理功能
const {
  dateRange,
  selectLastThreeDays: selectLastThreeDaysData,
  selectLastWeek: selectLastWeekData,
  selectLastMonth: selectLastMonthData,
  onDateRangeChange: onDateRangeChangeData
} = useData({
  defaultDateRange: 'week'  // 默认选择最近一周
})

// 遮罩层
const loading = ref(true);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(20); // 每页的数据条数
// 域名列表
const listData = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
const defaultSort = reactive({ prop: 'createTime', order: 'descending' });
// 计算总页数
const totalPages = computed(() => {
  // 如果API返回了sumPage并且有效，则使用它
  if (sumPage.value > 0) {
    return sumPage.value
  }
  // 否则使用计算的总页数
  return Math.ceil(total.value / queryParams.value.pageSize)
})
// 处理日期范围变化
const onDateRangeChange = (value) => {
  if (value && value.length === 2) {
    // 当选择了有效的日期范围时
    queryParams.startTime = dateToTimestamp(value[0]);
    queryParams.endTime = dateToTimestamp(value[1]);
  }
}

// 快捷选择按钮的点击事件处理函数
const selectLastThreeDays = () => {
  selectLastThreeDaysData(() => {
    const [start, end] = dateRange.value;
    queryParams.startTime = dateToTimestamp(start);
    queryParams.endTime = dateToTimestamp(end);
    handleQuery();
  });
}

const selectLastWeek = () => {
  selectLastWeekData(() => {
    const [start, end] = dateRange.value;
    queryParams.startTime = dateToTimestamp(start);
    queryParams.endTime = dateToTimestamp(end);
    handleQuery();
  });
}

const selectLastMonth = () => {
  selectLastMonthData(() => {
    const [start, end] = dateRange.value;
    queryParams.startTime = dateToTimestamp(start);
    queryParams.endTime = dateToTimestamp(end);
    handleQuery();
  });
}

// 表格列定义
const columns = [
  { key: 'id', label: t('domain.table.id'), field: 'id', sortable: false, width: '80px' },
  { key: 'domain', label: t('domain.table.domain'), field: 'domain', sortable: false, width: '180px' },
  { key: 'registPlat', label: t('domain.table.registPlat'), field: 'registPlat', width: '120px' },
  { key: 'nameServer', label: t('domain.table.nameServer'), field: 'nameServer', width: '120px' },
  { key: 'creator', label: t('domain.table.creator'), field: 'creator', width: '120px' },
  { key: 'createTime', label: t('domain.table.createTime'), field: 'createTime', sortable: false, width: '200px' },
  { key: 'registTime', label: t('domain.table.registTime'), field: 'registTime' },
  { key: 'expireTime', label: t('domain.table.expireTime'), field: 'expireTime' },
  { key: 'updator', label: t('domain.table.updator'), field: 'updator' },
  { key: 'updateTime', label: t('domain.table.updateTime'), field: 'updateTime' }
];


// 表单参数
const form = reactive({
  id: undefined,
  domain: undefined,
  ns1: undefined,
  ns2: undefined,
  remark: undefined,
  beginTime: undefined,
  endTime: undefined
});

// 表单校验
const rules = {
  domain: [
    { required: true, message: t('domain.validation.domainRequired'), trigger: "blur" },
  ]
};

const show_form = ref(false);
const register = ref(false);
const search = ref(false);
const checkAll = ref(false);
const checkedDomains = ref([]);
const checkedDomain = ref([]);
const isIndeterminate = ref(false);
const domainValue = ref(0);
const domain_open = ref(false);

// 表单引用
const queryForm = ref();
const formRef = ref();
const tables = ref();

// 获取域名列表
const getList = () => {
  loading.value = true;
  list(addDateRange(queryParams.value)).then(response => {
    listData.value = response.data.pageList;
    total.value = response.data.total;
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
};

// 默认排序事件
const getDefaultSort = () => {
  queryParams.orderByColumn = defaultSort.prop;
  queryParams.isAsc = defaultSort.order;
  getList();
};

// 搜索域名是否被注册
const searchDomain = () => {
  if (!registerForm.value.domain) {
    // 可以添加提示信息
    return;
  }

  search.value = true;
  checkDomain(registerForm.value.domain).then(response => {
    // 使用Vuestic UI的通知组件
    // this.$vaToast.init({ message: response.msg, color: 'success' });
    show_form.value = true;
    search.value = false;
  }).catch(() => {
    search.value = false;
  });
};

// 排序触发事件
const handleSortChange = (column) => {
  queryParams.orderByColumn = column.prop;
  queryParams.isAsc = column.order;
  handleQuery();
};

// 取消按钮
const cancel = () => {
  open.value = false;
  reset();
  handleQuery();
};

// 表单重置
const reset = () => {
  queryParams.domain = undefined;
  queryParams.creator = undefined;
  queryParams.startTime = undefined;
  queryParams.endTime = undefined;
  // 重置日期范围
  dateRange.value = [];

  // 重置表单校验
  if (formRef.value) {
    formRef.value.resetFields();
  }

  show_form.value = false;
  getList();
};

// 日期转时间戳（秒）
const dateToTimestamp = (date) => {
  if (!date) return undefined;
  // 确保date是Date对象
  const dateObj = date instanceof Date ? date : new Date(date);
  // 设置为当天的0:00:00
  dateObj.setHours(0, 0, 0, 0);
  // 返回10位整数时间戳（秒级）
  return Math.floor(dateObj.getTime() / 1000);
};

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1;
  // 使用改进后的dateToTimestamp函数
  // 如果dateRange有值，直接使用
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.startTime = dateToTimestamp(dateRange.value[0]);
    queryParams.endTime = dateToTimestamp(dateRange.value[1]);
  } else {
    // 如果单独设置了日期，处理它们
    if (queryParams.startTime) {
      queryParams.startTime = dateToTimestamp(queryParams.startTime);
    }
    if (queryParams.endTime) {
      queryParams.endTime = dateToTimestamp(queryParams.endTime);
    }
  }
  getList();
};

// 重置按钮操作
const resetQuery = () => {
  if (queryForm.value) {
    queryForm.value.resetFields();
  }
  handleQuery();
};

// 新增按钮操作
const handleAdd = () => {
  reset();
  open.value = true;
  title.value = t('domain.modal.register.title');
};

// 修改按钮操作
const handleUpdate = (row) => {
  reset();
  Object.assign(form, row);
  open.value = true;
  title.value = t('domain.modal.modify.title');
};

const registerForm = ref({
  id: undefined,
  domain: '',
  remark: ''
});

// 提交按钮
const submitRegisterForm = () => {
  if (registerForm.value.domain == undefined) {
    return;
  }
  registerDomain(registerForm.value).then(response => {
    open.value = false;
  });
};

// 修改域名的NS
const updateDomainNs = () => {
  updateNs(registerForm.value).then(response => {
    // 使用Vuestic UI的通知组件
    // this.$vaToast.init({ message: t('domain.messages.modifySuccess'), color: 'success' });
    open.value = false;
    getList();
  });
};

// 添加日期范围
const addDateRange = (params, dateRange, propName) => {
  const search = { ...params };
  if (dateRange && dateRange.length === 2) {
    search.params = typeof search.params === 'object' && search.params !== null && !Array.isArray(search.params) ? search.params : {};
    search.params['beginTime'] = dateRange[0];
    search.params['endTime'] = dateRange[1];
  }
  return search;
};

// 全选域名
const handleCheckAllChange = (val) => {
  // Clear the array and add new items instead of reassigning
  checkedDomains.value.splice(0, checkedDomains.value.length);
  if (val) {
    checkedDomain.value.forEach(item => checkedDomains.value.push(item));
  }
  isIndeterminate.value = false;
};

// 单个域名选择
const handleCheckedDomainsChange = (value) => {
  // Update the array contents instead of reassigning
  checkedDomains.value.splice(0, checkedDomains.value.length, ...value);
  let checkedCount = value.length;
  checkAll.value = checkedCount === checkedDomain.value.length;
  isIndeterminate.value = checkedCount > 0 && checkedCount < checkedDomain.value.length;
};

// 取消域名导入
const cancelDomain = () => {
  domain_open.value = false;
  checkAll.value = false;
  checkedDomains.value.splice(0, checkedDomains.value.length);
};

// 提交域名导入
const submitDomain = () => {
  if (checkedDomains.value.length <= 0) {
    // 使用Vuestic UI的通知组件
    // this.$vaToast.init({ message: t('domain.messages.selectAtLeastOne'), color: 'danger' });
    return;
  }

  importDomain(checkedDomains.value).then(response => {
    // 使用Vuestic UI的通知组件
    // this.$vaToast.init({ message: t('domain.messages.importSuccess'), color: 'success' });
    domain_open.value = false;
    getList();
  });
};

// 获取新域名列表
const getAllDomainList = () => {
  getNewsDomainList().then(response => {
    checkedDomain.value = response.data;
    if (checkedDomain.value.length > 0) {
      domain_open.value = true;
    } else {
      // 使用Vuestic UI的通知组件
      // this.$vaToast.init({ message: t('domain.messages.noNewDomain'), color: 'info' });
    }
  });
};

// 获取新域名数量
const getNewsDomainCount = () => {
  getNewsDomainList().then(response => {
    domainValue.value = response.data.length;
  });
};

// 格式化时间
const parseTime = (time, pattern) => {
  if (time == null || time === '') {
    return "--";
  }
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}';
  let date;
  if (typeof time === 'object') {
    date = time;
  } else {
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time);
    } else if (typeof time === 'string') {
      time = time.replace(new RegExp(/-/gm), '/');
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  };
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value]; }
    if (result.length > 0 && value < 10) {
      value = '0' + value;
    }
    return value || 0;
  });
  return time_str;
};

// 生命周期钩子
onMounted(() => {
  getDefaultSort();
  getNewsDomainCount();
  initTransition(); // 初始化过渡动画相关逻辑
});

// 移除事件监听
onUnmounted(() => {
  cleanupTransition(); // 清理过渡动画相关事件监听
});
</script>

<style>
.domain-buy {
  padding: 20px;
}



.primary-label {
  color: var(--va-primary);
  font-size: 12px;
  font-weight: 600;
}

.filter-label {
  color: var(--va-primary);
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
}

/* 筛选表单布局 */
.filter-form {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* 筛选网格布局 */
.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 12px;
  margin-top: 10px;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 8px;
}

.filter-item-header {
  display: flex;
  align-items: center;
}

.filter-input {
  width: 100%;
}

/* 日期选择器相关样式 */
.date-shortcuts {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 4px;
}

.date-shortcuts .va-button {
  font-size: 12px;
  padding: 4px 8px;
}

/* 操作按钮区域 */
.filter-actions {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 10px;
  margin: 8px 0;
}
/* 操作按钮区域 */
.filter-actions1{
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  gap: 10px;
  margin: 8px 0;
}
/* 表单区域样式 */
.form-section {
  margin-bottom: 16px;
}

.form-section-title {
  color: var(--va-primary);
  font-size: 14px;
  font-weight: 500;
  display: block;
}

/* 域名复选框容器 */
.domain-checkbox-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
  padding: 4px;
  border: 1px solid var(--va-background-border);
  border-radius: 4px;
}

.domain-checkbox {
  margin: 4px 0;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.domain-checkbox:hover {
  background-color: var(--va-background-element);
}

/* 响应式样式 */
@media (max-width: 991px) {
  .filter-toggle {
    display: flex;
  }

  .filter-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 2fr));
  }

  .domain-checkbox-container {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    max-height: 250px;
  }
}

@media (max-width: 576px) {
  .domain-checkbox-container {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 992px) {
  .filter-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }
}

/* 自定义 Datepicker 样式 */
:deep(.dp__main) {
  width: 100%;
}

:deep(.dp__input) {
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
}

:deep(.dp__range_end),
:deep(.dp__range_start),
:deep(.dp__active_date) {
  background-color: var(--va-primary) !important;
}

:deep(.dp__range_between) {
  background-color: var(--va-primary-opacity-3) !important;
}
</style>
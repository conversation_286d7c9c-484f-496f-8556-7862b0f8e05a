<template>
  <div class="ads">


    <VaCard class="filter-card">
      <!-- 筛选区域标题和控制按钮 -->
      <div class="filter-header flex justify-between items-center pb-2">
        <div class="flex items-center gap-2">
          <VaIcon name="mso-filter_list" color="primary" />
          <h2 class="text-lg font-medium">{{ t('ads.filter.title') }}</h2>
        </div>
        <div class="flex gap-2">
          <!-- 仅在移动设备和平板上显示展开/折叠按钮 -->
          <VaButton v-if="isMobileView" preset="secondary" border-color="primary" size="small" class="filter-toggle"
            @click="toggleFilter" :icon="isFilterExpanded ? 'mso-expand_less' : 'mso-expand_more'"
            :aria-label="isFilterExpanded ? t('ads.filter.collapse') : t('ads.filter.expand')">
            {{ isFilterExpanded ? t('ads.filter.collapse_btn') : t('ads.filter.expand_btn') }}
          </VaButton>
        </div>
      </div>

      <!-- 筛选区域内容 - 使用JS动画 -->
      <div ref="filterContent" class="filter-content" :style="{
        ...getContentStyles(),
      }">
        <!-- 筛选表单 -->
        <div class="filter-form" v-show="isFilterExpanded">
          <!-- 筛选条件网格 -->
          <div class="filter-grid">
            <!-- 域名筛选 -->
            <div class="filter-item">
              <div class="filter-item-header">
                <label class="filter-label">{{ t('ads.domain.label') }}</label>
              </div>
              <VaInput v-model="formData.domain" :placeholder="t('ads.domain.placeholder')" class="filter-input" />
            </div>

            <!-- 状态筛选 -->
            <div class="filter-item">
              <div class="filter-item-header">
                <label class="filter-label">{{ t('ads.status.label') }}</label>
              </div>
              <VaSelect v-model="formData.delFlag" :placeholder="t('ads.status.placeholder')" :options="domainStatusList" class="filter-input" />
            </div>

            <!-- 模板筛选 -->
            <div class="filter-item">
              <div class="filter-item-header">
                <label class="filter-label">{{ t('ads.template.label') }}</label>
              </div>
              <VaSelect v-model="formData.template" :placeholder="t('ads.template.placeholder')" :options="templateList"
                :text-by="option => option.name" class="filter-input" />
            </div>

            <!-- 广告主筛选 -->
            <div class="filter-item">
              <div class="filter-item-header">
                <label class="filter-label">{{ t('ads.advertiser.label') }}</label>
              </div>
              <VaSelect v-model="formData.advChannel" :placeholder="t('ads.advertiser.placeholder')" :options="advNameList"
                :text-by="option => option.name" :value-by="option => option.id" class="filter-input" />
            </div>

            <!-- 归属筛选 -->
            <div class="filter-item">
              <div class="filter-item-header">
                <label class="filter-label">{{ t('ads.ownership.label') }}</label>
              </div>
              <VaSelect v-model="formData.isPrivate" :options="isPrivateList" :placeholder="t('ads.ownership.placeholder')" class="filter-input" />
            </div>
          </div>

          <!-- 操作按钮区域 -->
          <div class="filter-actions mt-4">
            <VaButton color="primary" icon="mso-search" @click="handleQuery">{{ t('ads.buttons.search') }}</VaButton>
            <VaButton preset="secondary" border-color="primary" icon="mso-refresh" @click="resetQuery">{{ t('ads.buttons.reset') }}</VaButton>
            <VaButton preset="secondary" border-color="primary" icon="mso-refresh" @click="pullCode">{{ t('ads.buttons.pull_code') }}</VaButton>
            <VaButton preset="secondary" border-color="primary" icon="mso-add" @click="addDomainAds">{{ t('ads.buttons.add') }}</VaButton>
          </div>
        </div>
      </div>
    </VaCard>

    <VaDataTable class="table-crud mt-4" :items="tableData" :columns="columns" :loading="loading" hoverable striped>
      <template #cell(isPrivate)="{ value }">
        <VaTag v-if="value == 1" color="success">{{ t('ads.ownership.self') }}</VaTag>
        <VaTag v-if="value == 0" color="warning">{{ t('ads.ownership.cooperation') }}</VaTag>
      </template>

      <template #cell(domain)="{ value }">
        <span @dblclick="copyToClipboard(value)" :title="t('ads.domain.copy_title')" style="cursor: pointer;">
          {{ value }}
        </span>
      </template>

      <template #cell(templatePath)="{ rowData }">
        <span v-if="rowData && rowData.templatePath">{{ rowData.business }}-{{ rowData.templatePath }}</span>
        <span v-else>--</span>
      </template>

      <template #cell(adsTxt)="{ rowData }">
        <span style="color: #007bff;" v-if="rowData && rowData.domain">https://{{ rowData.domain }}/ads.txt</span>
        <span v-else>--</span>
      </template>

      <template #cell(delFlag)="{ value }">
        <span :style="{ color: getColor(parseInt(value)) }">{{ getDomainStatus(parseInt(value)) }}</span>
      </template>

      <template #cell(gaCode)="{ rowData }">
        <span>{{ rowData.gaCode }}</span>
      </template>

      <template #cell(updator)="{ value }">
        <span v-if="value !== undefined">{{ value }}</span>
        <span v-else>--</span>
      </template>
      <template #cell(channelName)="{ rowData }">
        <span v-if="rowData.channelName">{{ rowData.channelName }}</span>
        <span v-else>--</span>
      </template>

      <template #cell(propertyId)="{ rowData }">
        <span v-if="rowData.propertyId">{{ rowData.propertyId }}</span>
        <span v-else>---</span>
      </template>

      <template #cell(SEO)="{ rowData }">
        <span>NO</span>
      </template>

      <template #cell(updateTime)="{ value }">
        <span v-if="value !== undefined">{{ parseTime(value) }}</span>
        <span v-else>--</span>
      </template>

      <template #cell(createTime)="{ value }">
        <span>{{ parseTime(value) }}</span>
      </template>

      <template #cell(操作)="{ rowData }">
        <div class="flex gap-2">
          <VaButton icon="mso-edit" v-if="rowData.isPrivate == 1" preset="link" size="small"
            @click="handleUpdate(rowData)">
          </VaButton>
          <VaButton v-if="rowData.propertyId != null" preset="link" size="small" @click="applyGaCode(rowData)">GA
          </VaButton>
          <!-- <VaButton icon="mso-delete" preset="link" size="small" @click="handleDelete(rowData)"></VaButton> -->
        </div>
      </template>
    </VaDataTable>

    <!-- 分页 -->
    <div class="flex justify-end mt-4 gap-2 filter-actions1 mx-4">
      <div>
        <b>{{ total }} {{ t('ads.table.results') }}</b>
        {{ t('ads.table.per_page') }}
        <VaSelect v-model="queryParams.pageSize" class="!w-20 inline-block" :options="[10, 20, 50, 100]"
          @update:model-value="getList" />
      </div>
      <VaPagination v-model="queryParams.pageNum"  :visible-pages="5" :pages="totalPages"
        buttons-preset="secondary" gapped
        border-color="primary" class="justify-center sm:justify-start" @update:modelValue="getList" />
    </div>

    <!-- 添加或修改角色配置对话框 -->
    <VaModal v-model="open" :title="title" size="large" hide-default-actions close-button mobile-fullscreen>
      <VaForm ref="formRef" class="px-4">
        <!-- 表单内容区域 -->
        <div ref="formContent" class="form-content">
          <!-- 基本信息部分 -->
          <div class="form-grid">
            <VaInput :label="t('ads.form.domain')" v-model="form.domain" :placeholder="t('ads.domain.placeholder')" clearable />
            <VaInput :label="t('ads.form.site_info')" v-model="form.serviceName" :placeholder="t('ads.domain.placeholder')" />
          </div>

          <div class="my-4"></div>
          <div class="form-section">
            <span class="form-section-title">{{ t('ads.form.site_status') }}</span>
            <div class="my-2"></div>
            <VaRadio v-model="form.delFlag" :options="domainStatusList" :value-by="option => option.value"
              :vertical="isMobileView" />
          </div>

          <div class="my-4"></div>
          <div class="form-grid">
            <VaSelect :label="t('ads.form.advertiser')" v-model="form.advChannel" :placeholder="t('ads.advertiser.placeholder')" :options="advNameList"
              :text-by="option => option.name" :value-by="option => option.id" />
            <VaSelect :label="t('ads.form.business')" v-model="form.business" :placeholder="t('ads.template.placeholder')" :options="serverList"
            text-by="value" value-by="value"  />
          </div>

          <div class="my-4"></div>
          <!-- 网站参数设置折叠面板 -->
          <VaCollapse :header="t('ads.form.website_params')" class="mt-4">
            <template #header="{ value, attrs, iconAttrs, text }">
              <div v-bind="attrs"
                class="w-full flex border-[var(--va-background-border)] border-2 p-2 bg-[var(--va-background-element)]">
                <VaIcon name="va-arrow-down" :class="value ? '' : 'rotate-[-90deg]'" v-bind="iconAttrs" />
                <div class="ml-2">{{ text }}</div>
              </div>
            </template>

            <!-- AI应用提示词选择 -->
            <VaSelect :label="t('ads.form.ai_prompts')" v-if="form.business === 'AI'" v-model="selectPrompts" multiple :placeholder="t('ads.template.placeholder')"
              class="w-full p-4" :options="promptsList" :value-by="option => option.id"
              :text-by="option => option.name">
            </VaSelect>

            <div class="my-4"></div>
            <div class="form-grid">
              <VaSelect :label="t('ads.form.template_used')" v-model="form.templatePath" :options="templateList"
                :value-by="option => option.id" :text-by="option => option.name" :placeholder="t('ads.template.placeholder')">
              </VaSelect>
              <VaInput :label="t('ads.form.contact_email')" v-model="form.contactMail" :placeholder="t('ads.form.email_placeholder')" />
            </div>

            <div class="my-4"></div>
            <!-- 文本区域使用全宽布局 -->
            <VaTextarea :label="t('ads.form.website_title')" v-model="form.title" rows="3" :placeholder="t('ads.form.title_placeholder')" class="w-full" />

            <div class="my-4"></div>
            <VaTextarea :label="t('ads.form.keywords')" v-model="form.keywords" rows="3" :placeholder="t('ads.form.keywords_placeholder')" class="w-full" />

            <div class="my-4"></div>
            <VaTextarea :label="t('ads.form.website_desc')" v-model="form.content" rows="4" :placeholder="t('ads.form.desc_placeholder')" class="w-full" />
          </VaCollapse>

          <!-- 广告ADS设置折叠面板 -->
          <VaCollapse :header="t('ads.form.ads_settings')" class="mt-4">
            <template #header="{ value, attrs, iconAttrs, text }">
              <div v-bind="attrs"
                class="w-full flex border-[var(--va-background-border)] border-2 p-2 bg-[var(--va-background-element)]">
                <VaIcon name="va-arrow-down" :class="value ? '' : 'rotate-[-90deg]'" v-bind="iconAttrs" />
                <div class="ml-2">{{ text }}</div>
              </div>
            </template>

            <div class="my-4"></div>
            <VaInput :label="t('ads.form.ga_code')" v-model="form.gaCode" :placeholder="t('ads.form.ga_placeholder')" class="w-full" />

            <div class="my-4"></div>
            <VaTextarea :label="t('ads.form.ads_txt')" v-model="form.adsTxt" rows="6" :placeholder="t('ads.form.ads_placeholder')" class="w-full" />
          </VaCollapse>

          <div class="my-4"></div>
          <!-- 操作描述 -->
          <VaInput :label="t('ads.form.operation_desc')" v-if="form.id == undefined" v-model="form.remark" :placeholder="t('ads.form.remark_placeholder')"
            class="w-full" />
          <VaInput :label="t('ads.form.operation_desc')" v-else v-model="form.remark" :placeholder="t('ads.form.remark_edit_placeholder')" clearable class="w-full" />

          <!-- 操作按钮 -->
          <div class="flex flex-wrap justify-end gap-2 mt-6">
            <VaButton @click="open = false" preset="primary">{{ t('ads.buttons.cancel') }}</VaButton>
            <VaButton @click="submitForm">{{ t('ads.buttons.confirm') }}</VaButton>
            <VaButton v-if="form.zoneId !== undefined" @click="clearCDNCacheBtn(form)">{{ t('ads.buttons.clear_cdn') }}</VaButton>
          </div>
        </div>
      </VaForm>
    </VaModal>

    <!-- 添加域名广告 -->
    <VaModal v-model="showAddDomainAds" :title="t('ads.modal.add_title')" size="large" hide-default-actions close-button
      mobile-fullscreen>
      <VaForm ref="addFormRef" class="px-4" @submit.prevent="submitAddDomainAds">
        <!-- 表单内容区域 -->
        <div class="form-content">
          <!-- 基本信息部分 -->
          <div class="form-grid">
            <VaInput :label="t('ads.form.domain')" v-model="addForm.domain" :placeholder="t('ads.domain.placeholder')" clearable
              :rules="[(value) => (value && value.length > 0) || t('ads.form.domain_required')]">
              <template #label>
                <span class="required-field">{{ t('ads.form.domain') }}</span>
              </template>
            </VaInput>
            <VaInput :label="t('ads.form.site_info')" v-model="addForm.serviceName" :placeholder="t('ads.domain.placeholder')" />
          </div>

          <div class="my-4"></div>
          <!-- 站点状态和广告主 -->
          <div class="form-grid">
            <VaSelect v-model="addForm.delFlag" :options="domainStatusList.slice(1)" :value-by="option => option.value"
              :placeholder="t('ads.status.placeholder')" :rules="[(value) => (value !== undefined && value !== '') || t('ads.form.status_required')]">
              <template #label>
                <span class="required-field">{{ t('ads.form.site_status') }}</span>
              </template>
            </VaSelect>
            <VaSelect :label="t('ads.form.advertiser')" v-model="addForm.advChannel" :placeholder="t('ads.advertiser.placeholder')" :options="advNameList.slice(1)"
              :text-by="option => option.name" :value-by="option => option.id" />
          </div>

          <div class="my-4"></div>
          <VaSelect :label="t('ads.form.business')" v-model="addForm.business" :placeholder="t('ads.template.placeholder')" :options="serverList"
          text-by="value" value-by="value" />

          <div class="my-4"></div>
          <div class="form-grid">
            <VaInput :label="t('ads.form.contact_email')" v-model="addForm.contactMail" :placeholder="t('ads.form.email_placeholder')" />
            <VaInput :label="t('ads.form.website_title')" v-model="addForm.title" :placeholder="t('ads.form.title_placeholder')" />
          </div>

          <div class="my-4"></div>
          <!-- 文本区域在移动设备上堆叠，在大屏上并排 -->
          <div class="form-textarea-grid">
            <VaTextarea :label="t('ads.form.keywords')" v-model="addForm.keywords" :placeholder="t('ads.form.keywords_placeholder')" rows="4"
              class="w-full" />
            <VaTextarea :label="t('ads.form.website_desc')" v-model="addForm.content" :placeholder="t('ads.form.desc_placeholder')" rows="4" class="w-full" />
          </div>

          <div class="my-4"></div>
          <VaInput :label="t('ads.form.ga_code')" v-model="addForm.gaCode" :placeholder="t('ads.form.ga_placeholder')" class="w-full" />

          <div class="my-4"></div>
          <VaTextarea :label="t('ads.form.ads_txt')" v-model="addForm.adsTxt" :placeholder="t('ads.form.ads_placeholder')" rows="6" class="w-full" />

          <div class="my-4"></div>
          <!-- 操作按钮 -->
          <div class="flex flex-wrap justify-end gap-2 mt-6">
            <VaButton preset="primary" @click="showAddDomainAds = false">{{ t('ads.buttons.cancel') }}</VaButton>
            <VaButton type="submit" :disabled="addForm.domain === '' || addForm.delFlag === ''">{{ t('ads.buttons.confirm') }}</VaButton>
          </div>
        </div>
      </VaForm>
    </VaModal>
  </div>
</template>

<script setup>
import { onMounted, ref, watch, reactive, computed, onUnmounted } from 'vue';
import { useForm, defineVaDataTableColumns, useToast } from 'vuestic-ui';
import { parseTime } from '../../../utils/subdigi';
import { useTransition } from '@/composables/useTransition.js';
import { useI18n } from 'vue-i18n'; // 导入 i18n
import {
  listDomain,
  clearCdnCache,
  updateDomain,
  addDomain,
  diffDomain,
  advchannel,
  settingNginx,
  execPullCode,
  getWebPrompts,
  saveWebPrompts,
  addSite,
  changeDomain
} from '@/api/domain/domain';
import { getPageInit } from '@/api/ads/adv';
import { addGa, insights } from '@/api/ads/ga.js';

// 使用 i18n
const { t } = useI18n();

// 使用过渡动画功能
const {
  isExpanded: isFilterExpanded,
  contentRef: filterContent,
  isMobileView,
  toggle: toggleFilter,
  getContentStyles,
  init: initTransition,
  cleanup: cleanupTransition
} = useTransition({
  defaultExpanded: true,  // PC端默认展开
  breakpoint: 992,        // 小于992px为移动设备
  animationDuration: 300  // 动画持续时间
})

const { init: toast } = useToast()
const { validate, reset, resetValidation } = useForm(['formRef', 'addFormRef'], {
  validateOnBlur: true,
  validateOnChange: true,
  validateOnSubmit: true,
});

// 表单引用
const formRef = ref();
const form = ref({});
// 总条数
const total = ref(0);

// 表格列定义
const columns = defineVaDataTableColumns([
  { key: "id", label: t('ads.table.id'), sortable: false },
  { key: "isPrivate", label: t('ads.table.ownership') },
  { key: "domain", label: t('ads.table.domain'), sortable: false },
  { key: "templatePath", label: t('ads.table.template_number') },
  { key: "adsTxt", label: t('ads.table.ads_txt') },
  { key: "delFlag", label: t('ads.table.status') },
  { key: "channelName", label: t('ads.table.advertiser') },
  { key: "gaCode", label: t('ads.table.ga_code') },
  { key: "propertyId", label: t('ads.table.property_id') },
  { key: "SEO", label: t('ads.table.seo') },
  { key: "creator", label: t('ads.table.creator') },
  { key: "createTime", label: t('ads.table.create_time'), sortable: false },
  { key: "updator", label: t('ads.table.updator') },
  { key: "updateTime", label: t('ads.table.update_time') },
  { key: "操作", label: t('ads.table.action') },
]);

// 查询参数
const queryParams = ref({
  searchValue: undefined,
  delFlag: undefined,
  templatePath: undefined,
  advChannel: undefined,
  isPrivate: undefined,
  pageNum: 1,
  pageSize: 20,
});

// 计算总页数
const totalPages = computed(() => Math.ceil(total.value / queryParams.value.pageSize));

// 表单字段
const formData = ref({
  domain: '',
  delFlag: '',
  template: null,
  advChannel: '',
  isPrivate: ''
});

// 表格数据
const tableData = ref([]);

// 加载状态
const loading = ref(false);

// 弹窗控制
const open = ref(false);
const title = ref('');

const isPrivateList = ref([
  { text: t('ads.ownership.all'), value: '' },
  { text: t('ads.ownership.self'), value: 1 },
  { text: t('ads.ownership.cooperation'), value: 0 }
])

// 域名状态列表
const domainStatusList = ref([
  { text: t('ads.status.all'), value: '' },
  { text: t('ads.status.idle'), value: 0 },
  { text: t('ads.status.applying'), value: 1 },
  { text: t('ads.status.passed'), value: 2 },
  { text: t('ads.status.promoting'), value: 3 },
  { text: t('ads.status.reclaimed'), value: 4 }
]);

// 服务器列表
const serverList = ref([
  { value: 'Games' }, { value: 'News' },
  { value: 'Finance' }, { value: 'Recipe' },
  { value: 'Travel' }, { value: 'Tech' },
  { value: 'Entertainment' }, { value: 'Quiz' },
  { value: 'AI' }, { value: 'Other' }
]);

// 模板列表
const templateList = ref([]);

// 广告名称列表
const advNameList = ref([
]);

// 提示词列表
const promptsList = ref([]);
const selectPrompts = ref([]);
const diffDomainList = ref([]);

// 当前分析的域名
const useAnalytic = ref({});

// GA分析相关
const analyticOpen = ref(false);
const analyticTitle = ref('');
const analytic_data = ref([]);
const analytic_total = ref(0);
const groupBy = ref(true);
const loading_ga = ref(false);
const googledateRange = ref([]);
const analyticParams = ref({
  propertyId: undefined,
  groupBy: undefined,
});
const addForm = ref({
  domain: '',
  serviceName: '',
  contactMail: '',
  title: '',
  keywords: '',
  content: '',
  gaCode: '',
  adsTxt: '',
  delFlag: '',
  advChannel: '',
  business: '',
  remark: '',
});
const showAddDomainAds = ref(false);
const addDomainAds = () => {
  showAddDomainAds.value = true;
  // 重置添加表单
  addForm.value = {
    domain: '',
    serviceName: '',
    contactMail: '',
    title: '',
    keywords: '',
    content: '',
    gaCode: '',
    adsTxt: '',
    delFlag: '',
    advChannel: '',
    business: '',
    remark: '',
  };
  // 重置主表单
  form.value = {
    domain: '',
    delFlag: '',
    template: null,
    advChannel: null,
  }
}
const submitAddDomainAds = async () => {
  if (addForm.value.business != null) {
    addForm.value.business = addForm.value.business.text;
  }
  addSite(addForm.value).then(response => {
    if (response.code === 200) {
      toast({ message: t('ads.messages.add_success'), color: 'success' });
      getList();
      showAddDomainAds.value = false;
    }
  })
}

// 删除站点
const handleDelete = (row) => {

}

const clearCDNCacheBtn = (row) => {
  clearCdnCache(row.zoneId).then(response => {
    if (response.code === 200) {
      toast({ message: t('ads.messages.clear_cdn_success'), color: 'success' });
    }
  })
}

// 监听分页改变
// watch(() => queryParams.value.pageNum, (newValue, oldValue) => {
//   console.log('pageNum changed from', oldValue, 'to', newValue);
//   getList();
// });

// 获取域名列表
const getList = async () => {
  loading.value = true;
  try {
    const res = await listDomain(queryParams.value);
    tableData.value = res.data.pageList;
    total.value = res.data.total;
  } catch (error) {
    console.error(t('ads.messages.list_error'), error);
  } finally {
    loading.value = false;
  }
};


// 获取广告商列表
const getAdvAndtemplateList = async () => {
  try {
    const res = await getPageInit();
    advNameList.value = [{ name: t('ads.status.all') }, ...res.data.adsAdvList];
    templateList.value = [{ name: t('ads.status.all') }, ...res.data.templateList];
    diffDomainList.value = res.data.notCfDomainList;
  } catch (error) {
    console.error(t('ads.messages.adv_list_error'), error);
  }
};

// 获取未加入的域名数据
// const getDiffDomainList = async () => {
//   try {
//     const res = await diffDomain();
//     diffDomainList.value = res.data;
//   } catch (error) {
//     console.error('获取未加入的域名数据失败:', error);
//   }
// };

// 搜索按钮操作
const handleQuery = async () => {
  queryParams.value.pageNum = 1;
  queryParams.value.searchValue = formData.value.domain;
  queryParams.value.delFlag = formData.value.delFlag && formData.value.delFlag.value !== undefined ? formData.value.delFlag.value : '';
  queryParams.value.templatePath = formData.value.template ? formData.value.template['id'] : '';

  if (formData.value.advChannel) {
    if (typeof formData.value.advChannel === 'object' && formData.value.advChannel.id) {
      queryParams.value.advChannel = formData.value.advChannel.id;
    } else {
      queryParams.value.advChannel = formData.value.advChannel;
    }
  } else {
    queryParams.value.advChannel = '';
  }

  queryParams.value.isPrivate = formData.value.isPrivate && formData.value.isPrivate.value !== undefined ? formData.value.isPrivate.value : '';
  await getList();
};

// 重置按钮操作
const resetQuery = () => {
  formData.value = {
    domain: '',
    status: { text: '所有', value: '' },
    template: null,
    advChannel: null,
    isPrivate: { text: '所有', value: '' }
  };
  queryParams.value = {
    searchValue: undefined,
    delFlag: undefined,
    templatePath: undefined,
    advChannel: undefined,
    isPrivate: undefined,
    pageNum: 1,
    pageSize: 20,
  };
  handleQuery();
};

// PULL代码
const pullCode = async () => {
  try {
    const res = await execPullCode();
    toast({ message: t('ads.messages.pull_code_success'), color: 'success' });
  } catch (error) {
    console.error(t('ads.messages.pull_code_error'), error);
  }
};

// 获取域名状态
const getDomainStatus = (id) => {
  let length = domainStatusList.value.length;
  if (id === undefined || id >= length) {
    return t('ads.status.unknown');
  }
  return domainStatusList.value.find(item => item.value === id).text;
};

const submitForm = () => {
  changeDomain(form.value).then(response => {
    if (response.code === 200) {
      toast({ message: t('ads.messages.edit_success'), color: 'success' });
      getList();
      open.value = false;
    }
  })
}

// 获取状态颜色
const getColor = (value) => {
  switch (value) {
    case 0:
      return '#f3ae3d';
    case 1:
      return '#6b8cff';
    case 4:
      return '#ea335d';
    case 2:
    case 3:
      return '#64c529';
    default:
      return '#333333';
  }
};

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text);
    toast({ message: t('ads.domain.copy_success'), color: 'success' });
  } catch (err) {
    toast({ message: t('ads.domain.copy_fail'), color: 'danger' });
  }
};

// 申请GA代码
const applyGaCode = async (row) => {
  try {
    let res = await addGa(row.domain);
    if (res.code === 200) {
      toast({ message: t('ads.messages.ga_apply_success'), color: 'success' });
      getList();
    }
  } catch (error) {
    console.error(t('ads.messages.ga_apply_error'), error);
  }
};

// 修改按钮操作
const handleUpdate = (row) => {
  if (!row) {
    console.error('行数据为空');
    return;
  }

  // 重置表单
  form.value = row;
  // 确保remark字段存在
  if (form.value) {
    form.value.remark = undefined;
  }
  // 打开弹窗
  open.value = true;
  title.value = t('ads.modal.edit_title');
  // 如果是AI业务，获取提示词
  if (row.business === 'AI') {
    getWebPrompts({ id: row.id }).then(response => {
      if (response.data != null) {
        const result = response.data;
        selectPrompts.value = result.map(item => item.id);
      }
    });
  }
};
const addNewAdvChannelOption = () => {
  advNameList.value.push({ name: '所有' });
};
// 查看GA数据
const checkGaData = (row) => {
  if (!row || !row.domain) {
    console.error('行数据为空或没有domain属性');
    return;
  }
  // 清除分析数据
  clearAnalytic();
  // 设置当前分析的域名
  useAnalytic.value = row;
  analyticTitle.value = row.domain + " Analytic";
  // analyticOpen.value = true;
  // 初始化分析参数
  initAnalyticParams();
  // 获取分析数据
  getAnalyticList();
};

// 默认显示days天的数据
const getPreviousDaysDateRange = (days) => {
  const today = new Date();
  const endDate = new Date(today); // 结束日期为今天

  // 开始日期为今天减去指定的天数
  const startDate = new Date(today);
  startDate.setDate(startDate.getDate() - days);

  // 格式化为字符串
  const startDateStr = `${startDate.getFullYear()}-${('0' + (startDate.getMonth() + 1)).slice(-2)}-${('0' + startDate.getDate()).slice(-2)}`;
  const endDateStr = `${endDate.getFullYear()}-${('0' + (endDate.getMonth() + 1)).slice(-2)}-${('0' + endDate.getDate()).slice(-2)}`;

  // 返回日期范围数组
  return [startDateStr, endDateStr];
};

// 给需要查询GA数据赋予默认值
const initAnalyticParams = () => {
  googledateRange.value = getPreviousDaysDateRange(7);
  analyticParams.value = {
    propertyId: useAnalytic.value.propertyId,
    groupBy: "date",

  };
};

// 重置分析参数
const resetAnalytic = () => {
  initAnalyticParams();
};

// 清除查看GA数据的查询数据
const clearAnalytic = () => {
  googledateRange.value = getPreviousDaysDateRange(7);
  analyticParams.value = {
    propertyId: undefined,
    groupBy: undefined,
  };
  analyticTitle.value = "";
  analyticOpen.value = false;
  analytic_data.value = [];
  useAnalytic.value = {};
};

// 获取分析数据列表
const getAnalyticList = () => {
  loading_ga.value = true;
  analyticParams.value.beginTimeStr = googledateRange.value[0];
  analyticParams.value.endTimeStr = googledateRange.value[1];
  insights(analyticParams.value).then(response => {
    analytic_data.value = response.rows;
    analytic_total.value = response.total;
    loading_ga.value = false;
  }).catch(error => {
    console.error('获取分析数据失败:', error);
    loading_ga.value = false;
  });
};

// 搜索分析数据
const searchAnalytic = () => {
  analyticParams.value.groupBy = groupBy.value === false ? "" : "date";
  getAnalyticList();
};

// 生命周期钩子
onMounted(() => {
  getList();
  getAdvAndtemplateList();
  initTransition(); // 初始化过渡动画相关逻辑
});

// 组件卸载时清理事件监听
onUnmounted(() => {
  cleanupTransition(); // 清理过渡动画相关事件监听
});
</script>

<style>
.app-container {
  padding: 20px;
}


.primary-label {
  color: var(--va-primary);
  font-size: 12px;
  font-weight: 600;
}

.filter-label {
  color: var(--va-primary);
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
}

/* 筛选表单布局 */
.filter-form {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* 筛选网格布局 */
.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 12px;
  margin-top: 10px;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 8px;
}

.filter-item-header {
  display: flex;
  align-items: center;
}

.filter-input {
  width: 100%;
}

/* 操作按钮区域 */
.filter-actions {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 10px;
  margin: 8px 0;
}
/* 操作按钮区域 */
.filter-actions1 {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  gap: 10px;
  margin: 8px 0;
}
/* 表单样式 */
.form-content {
  width: 100%;
  padding: 8px 0;
}

/* 表单网格布局 - 响应式 */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 16px;
  width: 100%;
}

/* 文本区域网格布局 - 响应式 */
.form-textarea-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  width: 100%;
}

/* 表单分区标题 */
.form-section-title {
  color: var(--va-primary);
  font-size: 14px;
  font-weight: 500;

}




/* 必填字段标记 */
.required-field::after {
  content: '*';
  color: var(--va-danger);
  margin-left: 4px;
}

/* 响应式样式 */
@media (max-width: 991px) {
  .filter-toggle {
    display: flex;
  }

  .filter-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 2fr));
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

@media (min-width: 992px) {
  .filter-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }

  .form-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .form-textarea-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
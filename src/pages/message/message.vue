<template>
    <div class="message-page">
        <div class="page-header">
            <h1 class="page-title">{{ $t('message.title') }}</h1>

            <div class="page-actions" v-if="currentView === 'list'">
                <VaButtonGroup v-model="activeTab" :options="tabOptions" />
            </div>
        </div>

        <!-- 消息列表视图 -->
        <div v-if="currentView === 'list'">
            <MessageList :messages="filteredMessages" :loading="loading" :total="total"
                :current-page="queryParams.pageNum" :total-pages="totalPages" @select-message="showMessageDetail"
                @page-change="handlePageChange" />
        </div>

        <!-- 消息详情视图 -->
        <div v-else-if="currentView === 'detail' && selectedMessage">
            <MessageDetail :message="selectedMessage" @back="backToList" @update="updateMessage" />
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { getMessageList, updateMessageStatus } from '@/api/message/message'
import MessageList from './components/MessageList.vue'
import MessageDetail from './components/MessageDetail.vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const { t } = useI18n()

// 视图状态
const currentView = ref('list')
const selectedMessage = ref(null)

// 消息列表数据
const messageList = ref([])
const loading = ref(false)
const total = ref(0)

// 查询参数
const queryParams = ref({
    pageNum: 1,
    pageSize: 20,
    status: undefined // 消息状态：0-未读，1-已读，undefined-全部
})

// 计算总页数
const totalPages = computed(() => {
    return Math.ceil(total.value / queryParams.value.pageSize)
})

// 标签页选项
const tabOptions = [
    { text: t('message.tabs.all'), value: 'all' },
    { text: t('message.tabs.unread'), value: 'unread' },
    { text: t('message.tabs.read'), value: 'read' }
]

// 当前选中的标签页
const activeTab = ref('all')

// 监听标签页变化
const handleTabChange = (tab) => {
    switch (tab) {
        case 'unread':
            queryParams.value.status = 0
            break
        case 'read':
            queryParams.value.status = 1
            break
        default:
            queryParams.value.status = undefined
            break
    }
    queryParams.value.pageNum = 1
    getMessageListData()
}

// 监听activeTab变化
const watchActiveTab = computed(() => {
    handleTabChange(activeTab.value)
    return activeTab.value
})

// 过滤后的消息列表
const filteredMessages = computed(() => {
    return messageList.value
})

// 根据id 查找消息
const getMessageById = async (id) => { 
    selectedMessage.value = messageList.value.find(msg => msg.id === id)
    currentView.value = 'detail'
}

// 获取消息列表数据
const getMessageListData = async () => {
    loading.value = true

    try {
        const params = { ...queryParams.value }
        const response = await getMessageList(params)

        if (response && response.code === 200 && response.data) {
            messageList.value = response.data.pageList || []
            total.value = response.data.total || 0
        } else {
            messageList.value = []
            total.value = 0
        }
    } catch (error) {
        console.error('获取消息列表失败:', error)
        messageList.value = []
        total.value = 0
    } finally {
        loading.value = false
    }
}

// 处理分页变化
const handlePageChange = (page) => {
    queryParams.value.pageNum = page
    getMessageListData()
}

// 显示消息详情
const showMessageDetail = (message) => {
    selectedMessage.value = message
    currentView.value = 'detail'
}

// 返回列表
const backToList = () => {
    currentView.value = 'list'
    selectedMessage.value = null
}

// 更新消息
const updateMessage = (updatedMessage) => {
    // 更新列表中的消息
    const index = messageList.value.findIndex(msg => msg.id === updatedMessage.id)
    if (index !== -1) {
        messageList.value[index] = updatedMessage
    }

    // 更新选中的消息
    selectedMessage.value = updatedMessage
}

// 组件挂载时获取消息列表
onMounted(async() => {
    await getMessageListData()
      // 获取路由中的id
    const id = route.query.id
    if (id) {
        getMessageById(id)
    }
})
</script>

<style scoped>
.message-page {
    width: 100%;
    max-width: 2000px;
    margin: 0 auto;
    padding: 0 16px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;
    gap: 16px;
}

.page-title {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .page-actions {
        width: 100%;
    }
}
</style>
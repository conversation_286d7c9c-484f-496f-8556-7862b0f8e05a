<template>
    <div>
        <!-- 统计卡片 -->
        <div class="stats-grid">
            <VaCard class="stats-card">
                <VaCardContent>
                    <div class="flex flex-col">
                        <span class="stats-label">{{ $t('message.stats.anomalies') }}</span>
                        <span class="stats-value">{{ totalAnomalies }}</span>
                        <span class="stats-desc">{{ $t('message.stats.anomaliesDesc') }}</span>
                    </div>
                </VaCardContent>
            </VaCard>

            <VaCard class="stats-card">
                <VaCardContent>
                    <div class="flex flex-col">
                        <span class="stats-label">{{ $t('message.stats.domains') }}</span>
                        <span class="stats-value">{{ affectedDomains.length }}</span>
                        <span class="stats-desc">{{ $t('message.stats.domainsDesc') }}</span>
                    </div>
                </VaCardContent>
            </VaCard>

            <VaCard class="stats-card">
                <VaCardContent>
                    <div class="flex flex-col">
                        <span class="stats-label">{{ $t('message.stats.adSlots') }}</span>
                        <span class="stats-value">{{ adSlots.length }}</span>
                        <span class="stats-desc">{{ $t('message.stats.adSlotsDesc') }}</span>
                    </div>
                </VaCardContent>
            </VaCard>

            <VaCard class="stats-card">
                <VaCardContent>
                    <div class="flex flex-col">
                        <span class="stats-label">{{ $t('message.stats.avgDecline') }}</span>
                        <span class="stats-value">{{ avgDeclinePercent }}%</span>
                        <span class="stats-desc">{{ $t('message.stats.avgDeclineDesc') }}</span>
                    </div>
                </VaCardContent>
            </VaCard>
        </div>

        <!-- 图表 -->
        <!-- <VaCard class="mt-4">
            <VaCardTitle>{{ $t('message.stats.chartTitle') }}</VaCardTitle>
            <VaCardContent>
                <div class="chart-filters">
                    <VaButtonGroup v-model="selectedMetric" :options="metricOptions" />
                </div>
                <div class="chart-container">
                    <canvas ref="chartCanvas"></canvas>
                </div>
            </VaCardContent>
        </VaCard> -->
    </div>
</template>

<script setup>
import { defineProps, ref, computed, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import Chart from 'chart.js/auto'

const { t } = useI18n()

const props = defineProps({
    messageData: {
        type: Array,
        required: true
    }
})

const chartCanvas = ref(null)
let chartInstance = null

// 指标选项
const metricOptions = [
    { text: t('message.metrics.requestDecline'), value: '请求下降率' },
    { text: t('message.metrics.revenueDecline'), value: '收益下降率' },
    { text: t('message.metrics.matchRate'), value: '广告匹配率' }
]

const selectedMetric = ref(metricOptions[0].value)

// 计算受影响的域名
const affectedDomains = computed(() => {
    if (!props.messageData || !props.messageData.length) return [];

    // 从title中提取域名
    const domains = new Set();
    props.messageData.forEach(item => {
        const match = item.title.match(/^([^\ ]+)\ 域名/);
        if (match && match[1]) {
            domains.add(match[1]);
        }
    });

    return Array.from(domains);
});

// 计算受影响的广告位
const adSlots = computed(() => {
    if (!props.messageData || !props.messageData.length) return [];

    // 从title中提取广告位
    const slots = new Set();
    props.messageData.forEach(item => {
        const match = item.title.match(/([^\ ]+)\ 广告位/);
        if (match && match[1]) {
            slots.add(match[1]);
        }
    });

    return Array.from(slots);
});

// 计算异常数据总数
const totalAnomalies = computed(() => {
    return props.messageData ? props.messageData.length : 0;
});

// 计算平均下降率
const avgDeclinePercent = computed(() => {
    if (!props.messageData || !props.messageData.length) return 0;

    const sum = props.messageData.reduce((acc, item) => acc + item.declinePercent * 100, 0);
    return (sum / props.messageData.length).toFixed(2);
});

// 初始化图表
const initChart = () => {
    if (!chartCanvas.value) return;

    const ctx = chartCanvas.value.getContext('2d');
    if (!ctx) return;

    // 清理之前的图表实例
    if (chartInstance) {
        chartInstance.destroy();
    }

    // 根据选择的指标过滤数据
    const filteredData = props.messageData.filter(item => {
        return item.title.includes(selectedMetric.value);
    });

    // 准备图表数据
    const chartData = {
        labels: filteredData.slice(0, 10).map(item => {
            // 从title中提取广告位名称
            const match = item.title.match(/([^\ ]+)\ 广告位/);
            return match && match[1] ? match[1] : '未知';
        }),
        datasets: [
            {
                label: t('message.chart.declinePercent'),
                data: filteredData.slice(0, 10).map(item => item.declinePercent * 100),
                backgroundColor: 'rgba(67, 97, 238, 0.7)',
                borderColor: 'rgb(67, 97, 238)',
                borderWidth: 1
            },
            {
                label: t('message.chart.threshold'),
                data: filteredData.slice(0, 10).map(item => item.threshold * 100),
                backgroundColor: 'rgba(240, 82, 82, 0.3)',
                borderColor: 'rgb(240, 82, 82)',
                borderWidth: 1,
                type: 'line'
            }
        ]
    };

    // 创建图表
    chartInstance = new Chart(ctx, {
        type: 'line',
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: t('message.chart.percentLabel')
                    },
                    ticks: {
                        callback: function (value) {
                            return value + '%';
                        }
                    }
                },
                x: {
                    ticks: {
                        maxRotation: 45,
                        minRotation: 45
                    }
                }
            },
            plugins: {
                title: {
                    display: true,
                    text: t('message.chart.title', { metric: selectedMetric.value })
                },
                tooltip: {
                    callbacks: {
                        label: function (context) {
                            return context.dataset.label + ': ' + context.parsed.y.toFixed(2) + '%';
                        }
                    }
                }
            }
        }
    });
};

// 当选择的指标变化时重新渲染图表
watch(selectedMetric, () => {
    initChart();
});

// 当消息数据变化时重新渲染图表
watch(() => props.messageData, () => {
    initChart();
}, { deep: true });

// 组件挂载时初始化图表
onMounted(() => {
    initChart();
});
</script>

<style scoped>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
}

.stats-card {
    height: 100%;
}

.stats-label {
    font-size: 14px;
    color: var(--va-text-secondary);
    margin-bottom: 8px;
}

.stats-value {
    font-size: 24px;
    font-weight: 600;
    color: var(--va-primary);
    margin-bottom: 4px;
}

.stats-desc {
    font-size: 12px;
    color: var(--va-text-secondary);
}

.chart-container {
    height: 300px;
    position: relative;
    margin-top: 16px;
}

.chart-filters {
    display: flex;
    justify-content: center;
    margin-bottom: 16px;
}
</style>
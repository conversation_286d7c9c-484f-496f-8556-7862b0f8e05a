<template>

    <VaCard>
        <div v-if="loading" class="flex justify-center py-8">
            <VaProgressCircle indeterminate color="primary" />
        </div>
        <div v-else-if="!messages.length" class="flex flex-col items-center py-8">
            <VaIcon name="mso-notifications_off" size="large" color="gray" />
            <p class="mt-4 text-gray-500">{{ $t('message.list.noMessages') }}</p>
        </div>
        <div v-else>
            <!-- 添加Tab导航栏 -->
            <!-- <div class="border-b border-backgroundBorder mb-4">
                <div class="flex gap-1 sm:gap-2 -mb-px">
                    <button v-for="tab in messageCategories" :key="tab.key" @click="handleTabChange(tab.key)"
                        :class="['tab-button', { 'active-tab': activeTab === tab.key }]">
                        {{ tab.name }}
                    </button>
                </div>
            </div> -->

            <!-- 添加过渡效果 -->
            <transition name="layout-switch" mode="out-in">
                <div :key="`${activeTab}-content`">
                    <transition-group name="message-item-anim" tag="div" class="message-list">
                        <div v-for="(message, index) in filteredMessages" :key="message.id" class="message-item"
                            :class="{ 'message-item-unread': message.status === 0 }" 
                            :style="{ '--i': index }"
                            @click="$emit('select-message', message)">
                            <div class="message-content">
                                <div class="message-header">
                                    <h3 class="message-title">{{ message.title }}</h3>
                                    <VaBadge :text="getLevelText(message.level)" :color="getLevelColor(message.level)" style="margin-right: 8px;"/>
                                    <VaBadge :text="getStatusText(message.status)"
                                    :color="message.status === 0 ? 'primary' : 'success'" size="small" />
                                </div>
                      
                            </div>
                            <div class="message-meta">
                                <div class="message-time">{{ formatTime(message.createTime) }}</div>
                            
                            </div>
                        </div>
                    </transition-group>
                </div>
            </transition>

            <!-- 分页 -->
            <div class="flex justify-between items-center mt-6">
       
                <VaPagination v-if="totalPages > 1" v-model="currentPage" :pages="totalPages" :visible-pages="5"
                    buttons-preset="secondary" gapped border-color="primary"
                    @update:modelValue="$emit('page-change', $event)" />
            </div>
        </div>
    </VaCard>
</template>

<script setup>
import { defineProps, defineEmits, ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
    messages: {
        type: Array,
        default: () => []
    },
    loading: {
        type: Boolean,
        default: false
    },
    total: {
        type: Number,
        default: 0
    },
    currentPage: {
        type: Number,
        default: 1
    },
    totalPages: {
        type: Number,
        default: 1
    }
})

const currentPage = ref(props.currentPage)
const activeTab = ref('all')

const messageCategories = ref([
    { key: 'all', name: t('message.tabs.all') },
    { key: 'unread', name: t('message.tabs.unread') },
    { key: 'read', name: t('message.tabs.read') }
])

const emit = defineEmits(['select-message', 'page-change', 'tab-change'])

// 根据activeTab过滤消息
const filteredMessages = computed(() => {
    if (activeTab.value === 'all') {
        return props.messages.sort((a, b) => b.createTime - a.createTime);
    } else if (activeTab.value === 'unread') {
        return props.messages.filter(message => message.status === 0);
    } else if (activeTab.value === 'read') {
        return props.messages.filter(message => message.status === 1);
    }
    // 根据createTime排序 最大的放最前面
    return props.messages.sort((a, b) => b.createTime - a.createTime);
});

// 获取消息预览内容
const getMessagePreview = (content) => {
    try {
        if (!content) return '无内容';

        // 尝试解析JSON内容
        const parsedContent = JSON.parse(content);

        // 如果是数组，取前3个元素的title
        if (Array.isArray(parsedContent) && parsedContent.length > 0) {
            const titles = parsedContent.slice(0, 3).map(item => item.title);
            return titles.join(', ') + (parsedContent.length > 3 ? '...' : '');
        }

        return content.substring(0, 100) + (content.length > 100 ? '...' : '');
    } catch (e) {
        // 如果不是JSON，直接截取字符串
        return content.substring(0, 100) + (content.length > 100 ? '...' : '');
    }
};

// 处理标签切换
const handleTabChange = (tabKey) => {
    activeTab.value = tabKey;
    emit('tab-change', tabKey);
}

// 格式化时间
const formatTime = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp * 1000);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
};

// 获取消息级别文本
const getLevelText = (level) => {
    switch (level) {
        case 1: return t('message.level.critical');
        case 2: return t('message.level.warning');
        case 3: return t('message.level.info');
        default: return t('message.level.unknown');
    }
};

// 获取消息级别颜色
const getLevelColor = (level) => {
    switch (level) {
        case 1: return 'danger';
        case 2: return 'warning';
        case 3: return 'info';
        default: return 'gray';
    }
};

// 获取消息状态文本
const getStatusText = (status) => {
    return status === 0 ? t('message.status.unread') : t('message.status.read');
};
</script>

<style scoped>
.message-list {
    display: flex;
    flex-direction: column;
    position: relative; /* 添加相对定位，用于过渡动画 */
}

.message-item {
    display: flex;
    padding: 8px;
    border-bottom: 1px solid var(--va-background-border);
    cursor: pointer;
    transition: background-color 0.2s, transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.message-item:hover {
    background-color: var(--va-background-element);
    transform: translateY(-2px);
}

.message-item-unread {
    background-color: rgba(var(--va-primary-rgb), 0.05);
    border-left: 3px solid var(--va-primary);
}

.message-content {
    flex: 1;
    min-width: 0;
    /* 确保文本可以正确截断 */
}

.message-header {
    display: flex;
    align-items: center;
}

.message-title {
    font-weight: 500;
    margin-right: 8px;
    font-size: 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80%;
}

.message-preview {
    color: var(--va-text-secondary);
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.message-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    min-width: 120px;
}

.message-time {
    font-size: 13px;
    color: var(--va-text-secondary);
    margin-bottom: 8px;
}

/* Tab样式 */
.tab-button {
    padding: 4px 8px;
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: 6px 6px 0 0;
    border-bottom: 2px solid transparent;
    color: var(--va-text-secondary);
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

.tab-button:hover {
    color: var(--va-primary);
    background-color: var(--va-background-element);
}

.tab-button.active-tab {
    color: var(--va-primary);
    background-color: var(--va-background-primary);
    border-color: var(--va-primary);
}

@media (min-width: 640px) {
    .tab-button {
        padding: 8px 16px;
        font-size: 1rem;
    }
}

/* 动画样式 */
/* 整体布局切换动画 */
.layout-switch-enter-active,
.layout-switch-leave-active {
    transition: all 0.1s ease;
}

.layout-switch-enter-from,
.layout-switch-leave-to {
    opacity: 0;
    transform: translateY(20px);
}

/* 消息项动画 */
.message-item-anim-enter-active,
.message-item-anim-leave-active {
    transition: all 0.1s ease;
    transition-delay: calc(0.01s * var(--i, 0)); /* 根据索引添加延迟 */
}

.message-item-anim-leave-active {
    position: absolute;
    width: 100%;
}

.message-item-anim-enter-from {
    opacity: 0;
    transform: translateY(20px);
}

.message-item-anim-leave-to {
    opacity: 0;
    transform: translateX(-20px);
}
</style>
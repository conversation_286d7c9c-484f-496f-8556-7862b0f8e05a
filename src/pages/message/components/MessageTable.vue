<template>
    <VaCard>
        <VaCardContent>
            <!-- 表格 -->
            <div class="table-responsive">
                <VaDataTable :items="filteredData" :columns="columns" :loading="loading" striped hoverable bordered>
                <template #cell(statTimeStr)="{ rowData }">
                        <template v-if="rowData.statTimeStr">
                            {{ rowData.statTimeStr }}
                        </template>
                        <template v-else>
                            --
                        </template>
                    </template>
                <template #cell(appName)="{ rowData }">
                <template v-if="rowData.appName">
                        {{ rowData.appName }}
                </template>
                <template v-else>
                        --
                </template>
                </template>
                <template #cell(affName)="{ rowData }">
                    <template v-if="rowData.affName">
                        {{ rowData.affName }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(advName)="{ rowData }">
                    <template v-if="rowData.advName">
                        {{ rowData.advName }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(adsRequest)="{ rowData }">
                    <template v-if="rowData.adsRequest !== undefined && rowData.adsRequest !== null">
                        {{ formatNumber(rowData.adsRequest) }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(adsMatch)="{ rowData }">
                    <template v-if="rowData.adsMatch !== undefined && rowData.adsMatch !== null">
                        {{ formatNumber(rowData.adsMatch) }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(matchRate)="{ rowData }">
                    <template v-if="rowData.matchRate !== undefined && rowData.matchRate !== null">
                        {{ rowData.matchRate + '%' }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <!-- 数据列自定义渲染 -->
                <template #cell(adsImpression)="{ rowData }">
                    <template v-if="rowData.adsImpression !== undefined && rowData.adsImpression !== null">
                        {{ formatNumber(rowData.adsImpression) }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(impressionRate)="{ rowData }">
                    <template v-if="rowData.impressionRate !== undefined && rowData.impressionRate !== null">
                        {{ rowData.impressionRate + '%' }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(adsDisplay)="{ rowData }">
                    <template v-if="rowData.adsDisplay !== undefined && rowData.adsDisplay !== null">
                        {{ formatNumber(rowData.adsDisplay) }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(activeViewRate)="{ rowData }">
                    <template v-if="rowData.activeViewRate !== undefined && rowData.activeViewRate !== null">
                        {{ rowData.activeViewRate + '%' }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(adsClick)="{ rowData }">
                    <template v-if="rowData.adsClick !== undefined && rowData.adsClick !== null">
                        {{ formatNumber(rowData.adsClick) }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(clickRate)="{ rowData }">
                    <template v-if="rowData.clickRate !== undefined && rowData.clickRate !== null">
                        {{ rowData.clickRate + '%' }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(estimateRevenue)="{ rowData }">
                    <template v-if="rowData.estimateRevenue !== undefined && rowData.estimateRevenue !== null">
                        {{ '$' + formatNumber(rowData.estimateRevenue) }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(ecpm)="{ rowData }">
                    <template v-if="rowData.ecpm !== undefined && rowData.ecpm !== null">
                        {{ '$' + rowData.ecpm }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <!-- <template #cell(ecpc)="{ rowData }">
                    <template v-if="rowData.ecpc !== undefined && rowData.ecpc !== null">
                        {{ '$' + rowData.ecpc }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template> -->

    
                </VaDataTable>
            </div>
        </VaCardContent>
    </VaCard>
</template>

<script setup>
import { defineProps, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { formatNumber } from '@/utils'
const { t } = useI18n()

const props = defineProps({
    messageData: {
        type: Array,
        required: true
    },
    loading: {
        type: Boolean,
        default: false
    }
})
const formatFn = (value) => {
    let month = new Date(value * 1000).getMonth() + 1 > 9 ? new Date(value * 1000).getMonth() + 1 : '0' + (new Date(value * 1000).getMonth() + 1)
    let day = new Date(value * 1000).getDate() > 9 ? new Date(value * 1000).getDate() : '0' + new Date(value * 1000).getDate()
    return value ? month + '-' + day : '--'
}
// 表格列定义
const columns = [
   // 分组类别的列
   {
      key: 'statTimeStr',
      label: t('insight.table.date'),
      category: '分组列',
      visible: true,
      sortable: false,
      field: 'statTimeStr',
      groupName: 'dataName'
    },
    {
      key: 'appName',
      label: t('insight.table.domain'),
      category: '分组列',
      visible: false,
      sortable: false,
      field: 'app_name',
      groupName: 'appName'
    },

    {
      key: 'advName',
      label: 'Advertiser',
      category: '分组列',
      visible: false,
      sortable: false,
      field: 'adv_id',
      groupName: 'advName'
    },
    {
      key: 'affName',
      label: t('insight.table.channel'),
      category: '分组列',
      visible: false,
      sortable: false,
      field: 'aff_id',
      groupName: 'affName'
    },
    // {
    //   key: 'adsName',
    //   label: t('insight.table.adUnit'),
    //   category: '分组列',
    //   visible: false,
    //   sortable: false,
    //   field: 'ads_name',
    //   groupName: 'adsName'
    // },
    // {
    //   key: 'pubId',
    //   label: 'Publisher ID',
    //   category: '分组列',
    //   visible: false,
    //   sortable: false,
    //   field: 'pub_id',
    //   groupName: 'pubId'
    // },

    // 用户数据类别的列

    // 广告数据类别的列
    {
      key: 'adsRequest',
      label: t('insight.table.adRequest'),
      category: '广告数据',
      visible: true,
      sortable: false
    },
    {
      key: 'adsMatch',
      label: t('insight.table.adMatch'),
      category: '广告数据',
      visible: true,
      sortable: false
    },
    {
      key: 'matchRate',
      label: t('insight.table.matchRate'),
      category: '广告数据',
      visible: true,
      sortable: false
    },
    {
      key: 'adsImpression',
      label: t('insight.table.adImpression'),
      category: '广告数据',
      visible: true,
      sortable: false
    },
    {
      key: 'impressionRate',
      label: t('insight.table.impressionRate'),
      category: '广告数据',
      visible: true,
      sortable: false
    },
    {
      key: 'adsDisplay',
      label: t('insight.table.adDisplay'),
      category: '广告数据',
      visible: true,
      sortable: false
    },
    {
      key: 'activeViewRate',
      label: t('insight.table.activeViewRate'),
      category: '广告数据',
      visible: true,
      sortable: false
    },
    {
      key: 'adsClick',
      label: t('insight.table.adClick'),
      category: '广告数据',
      visible: true,
      sortable: false
    },
    {
      key: 'clickRate',
      label: t('insight.table.clickRate'),
      category: '广告数据',
      visible: true,
      sortable: false
    },

    // 收益数据类别的列
    {
      key: 'estimateRevenue',
      label: t('insight.table.estimatedRevenue'),
      category: '收益数据',
      visible: true,
      sortable: false
    },
    {
      key: 'ecpm',
      label: t('insight.table.ecpm'),
      category: '收益数据',
      visible: true,
      sortable: false
    },
    // {
    //   key: 'ecpc',
    //   label: t('insight.table.ecpc'),
    //   category: '收益数据',
    //   visible: true,
    //   sortable: false
    // }
]


// 过滤后的数据
const filteredData = computed(() => {
    if (!props.messageData) return [];

    return props.messageData
});

// 从标题中提取域名
const extractDomain = (title) => {
    if (!title) return '';
    const match = title.match(/^([^\ ]+)\ 域名/);
    return match && match[1] ? match[1] : '';
};

// 从标题中提取广告位
const extractAdSlot = (title) => {
    if (!title) return '';
    const match = title.match(/([^\ ]+)\ 广告位/);
    return match && match[1] ? match[1] : '';
};

// 从标题中提取指标
const extractMetric = (title) => {
    if (!title) return '';
    const match = title.match(/的\ ([^\ ]+)\ 数据下降/);
    return match && match[1] ? match[1] : '';
};

// 格式化百分比
const formatPercent = (value) => {
    if (value === undefined || value === null) return '';
    return (value).toFixed(2) + '%';
};

// 根据column 决定展示百分比还是数字
const formatValue = (value, column) => {
    console.log('column',column);
    if (column.includes('rate') || column.includes('Rate')) {
        return formatPercent(value);
    }
    return formatNumber(value);
};


// 获取下降率的样式
const getDeclineStyle = (value) => {
    if (value === undefined || value === null) return {};

    const percent = value * 100;
    let color = '';

    if (percent >= 90) {
        color = 'var(--va-danger)';
    } else if (percent >= 80) {
        color = 'var(--va-warning)';
    } else if (percent >= 60) {
        color = 'var(--va-orange)';
    } else {
        color = 'var(--va-text-primary)';
    }

    return {
        color: color,
        fontWeight: 'bold'
    };
};
</script>

<style scoped>
.filter-container {
    margin-bottom: 16px;
}

.filter-group {
    margin-bottom: 12px;
}

.filter-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--va-text-primary);
}

.filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.filter-chip {
    cursor: pointer;
}

.table-responsive {
    overflow-x: auto;
}

.domain-cell {
    font-weight: 500;
    color: var(--va-primary);
}

.ad-slot-cell {
    font-size: 14px;
    color: var(--va-text-secondary);
}

.metric-cell {
    font-weight: 500;
}

.decline-cell {
    font-weight: bold;
}

.threshold-cell {
    color: var(--va-text-secondary);
}
</style>
<template>
    <div class="message-detail">
        <!-- 标题和操作区域 -->
        <div class="detail-header">
            <VaButton preset="secondary" icon="mso-arrow_back" @click="$emit('back')">
                {{ $t('message.detail.back') }}
            </VaButton>
        </div>

        <!-- 消息标题和时间 -->
        <VaCard class="mt-4">
            <VaCardContent>
                <div class="message-header">
                    <h2 class="message-title">{{ message.title }}</h2>
                    <VaBadge :text="getLevelText(message.level)" :color="getLevelColor(message.level)" />
                </div>

                <div class="message-meta">
                    <div class="meta-item">
                        <VaIcon name="mso-access_time" size="small" />
                        <span>{{ $t('message.detail.notificationTime') }}: {{ formatTime(message.createTime) }}</span>
                    </div>
                    <div class="meta-item">
                        <VaIcon name="mso-calendar_today" size="small" />
                        <span>{{ $t('message.detail.dataDate') }}: {{ getDataDate(message) }}</span>
                    </div>
                    <div class="meta-item">
                        <VaIcon name="mso-info" size="small" />
                        <span>{{ $t('message.detail.status') }}: {{ getStatusText(message.status) }}</span>
                    </div>
                </div>
            </VaCardContent>
        </VaCard>

        <!-- 统计信息 -->
        <!-- <div class="mt-4" v-if="parsedContent.length > 0">
            <MessageStats :message-data="parsedContent" />
        </div> -->

        <!-- 异常数据表格 -->
        <div class="mt-4" v-if="parsedContent.length > 0">
            <MessageTable :message-data="parsedContent" :loading="false" />
        </div>
    </div>
</template>

<script setup>
import { defineProps, defineEmits, computed, ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { updateMessageStatus } from '@/api/message/message'
import { useToast } from 'vuestic-ui'
import MessageStats from './MessageStats.vue'
import MessageTable from './MessageTable.vue'
const { t } = useI18n()
const { init: toast } = useToast()
const props = defineProps({
    message: {
        type: Object,
        required: true
    }
})

const emit = defineEmits(['back', 'update'])

// 解析消息内容
const parsedContent = computed(() => {
    console.log(JSON.parse(props.message.content))
    if (!props.message || !props.message.content) return [];

    try {

        return JSON.parse(props.message.content);
    } catch (e) {
        console.error('解析消息内容失败:', e);
        return [];
    }
});

// 标记为已读状态
const markingAsRead = ref(false);

// 标记消息为已读
const markAsRead = async () => {
    if (!props.message || props.message.status !== 0) return;

    markingAsRead.value = true;

    try {
        const response = await updateMessageStatus({
            ids: [props.message.id],
            isAll: false
        });

        if (response && response.code === 200) {
            // 自动标记为已读，不显示成功提示
            // 通知父组件更新消息状态
            emit('update', {
                ...props.message,
                status: 1
            });
        }
    } catch (error) {
    } finally {
        markingAsRead.value = false;
    }
};

// 格式化时间
const formatTime = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp * 1000);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
};

// 获取数据日期
const getDataDate = (message) => {
    if (!message || !parsedContent.value || parsedContent.value.length === 0) return '';

    const firstItem = parsedContent.value[0];
    if (firstItem && firstItem.time) {
        return firstItem.time;
    }

    return '';
};

// 获取消息级别文本
const getLevelText = (level) => {
    switch (level) {
        case 1: return t('message.level.critical');
        case 2: return t('message.level.warning');
        case 3: return t('message.level.info');
        default: return t('message.level.unknown');
    }
};

// 获取消息级别颜色
const getLevelColor = (level) => {
    switch (level) {
        case 1: return 'danger';
        case 2: return 'warning';
        case 3: return 'info';
        default: return 'gray';
    }
};

// 获取消息状态文本
const getStatusText = (status) => {
    return status === 0 ? t('message.status.unread') : t('message.status.read');
};

// 组件挂载时自动标记消息为已读
onMounted(() => {
    if (props.message && props.message.status === 0) {
        markAsRead();
    }
});
</script>

<style scoped>
.message-detail {
    width: 100%;
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.detail-actions {
    display: flex;
    gap: 12px;
}

.message-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.message-title {
    font-size: 20px;
    font-weight: 600;
    margin-right: 12px;
}

.message-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    color: var(--va-text-secondary);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
}

@media (max-width: 768px) {
    .detail-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .detail-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .message-meta {
        flex-direction: column;
        gap: 8px;
    }
}
</style>
<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { getAdsData as basic } from '@/api/ads/statics'
import SubLoading from '../../../components/SubLoading.vue'
import {
  GADataChart,
  AdsDataChart,
  AdsDataChartLine,
  UnifiedStatsCard,
} from './components'

import Datepicker from '@vuepic/vue-datepicker'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const userStore = useUserStore()
import { useUserStore } from '@/stores/user-store'
const isPermission = ref(false)
// 响应式状态
const loading = ref(true)
const isDark = computed(() => userStore.theme === 'dark')

// 日期范围设置 - 默认显示最近一个月
const today = new Date()
const lastMonth = new Date()
lastMonth.setMonth(today.getMonth() - 1) // 一个月前

const dateRange = ref([lastMonth, today])
// 处理日期范围变化
const onDateRangeChange = (value) => {
  if (value && value.length === 2) {
    // 当选择了有效的日期范围时
    handleQuery(); // 触发查询
  }
}

// 快捷选择按钮的点击事件处理函数
const selectLastThreeDays = () => {
  const today = new Date()
  const lastWeek = new Date()
  lastWeek.setDate(today.getDate() - 3) // 最近三天
  dateRange.value = [lastWeek, today]
  handleQuery()
}

const selectLastWeek = () => {
  const today = new Date()
  const lastWeek = new Date()
  lastWeek.setDate(today.getDate() - 6) // 最近一周是今天和前6天
  dateRange.value = [lastWeek, today]
  handleQuery()
}

const selectLastMonth = () => {
  const today = new Date()
  const lastMonth = new Date()
  lastMonth.setDate(today.getDate() - 29) // 最近一月是今天和前29天
  dateRange.value = [lastMonth, today]
  handleQuery()
}

const dashboardData = reactive({
  domain: {
    sumCount: 0,
    privateCount: 0,
    applyInCount: 0,
    passCount: 0,
    useCount: 0,
    recycleCount: 0,
    secondDomainCount: 0
  },
  revenueStatics: {
    todayRev: 0,
    yesterdayRev: 0,
    thisMonthRev: 0,
    lastMonthRev: 0
  },
  adsInsightsSum: {
    adsRequest: 0,
    adsMatch: 0,
    matchRate: 0,
    adsImpression: 0,
    adsDisplay: 0,
    adsClick: 0,
    clickRate: 0,
    estimateRevenue: 0,
    ecpm: 0,
    activeViewRate: 0,
    impressionRate: 0,
    ecpc: 0,
    originalRevenue: 0
  },
  googleInsightsList: [],
  adsInsightsVoList: [],
  adsInsightsDomainList: []
})

// 计算属性，用于确定显示哪些组件
const showDomainStats = computed(() => dashboardData.domain &&
  (dashboardData.domain.sumCount !== undefined ||
    dashboardData.domain.privateCount !== undefined ||
    dashboardData.domain.useCount !== undefined))

const showRevenueStats = computed(() => dashboardData.revenueStatics &&
  (dashboardData.revenueStatics.todayRev !== undefined ||
    dashboardData.revenueStatics.yesterdayRev !== undefined ||
    dashboardData.revenueStatics.thisMonthRev !== undefined))

const showAdsInsights = computed(() => dashboardData.adsInsightsSum &&
  (dashboardData.adsInsightsSum.adsRequest !== undefined ||
    dashboardData.adsInsightsSum.adsMatch !== undefined ||
    dashboardData.adsInsightsSum.adsImpression !== undefined))

const showAdsChartLine = computed(() => dashboardData.adsInsightsVoList &&
  dashboardData.adsInsightsVoList.length > 0)

const showGAChart = computed(() => dashboardData.googleInsightsList &&
  dashboardData.googleInsightsList.length > 0)

const showAdsChart = computed(() => dashboardData.adsInsightsDomainList &&
  dashboardData.adsInsightsDomainList.length > 0)

const refreshData = async () => {
  await syncData()
  await syncDataList()
}
// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    // 将日期转换为10位时间戳，并设置为当天的0:00
    const startDate = dateRange.value[0] ? new Date(dateRange.value[0]) : undefined
    const endDate = dateRange.value[1] ? new Date(dateRange.value[1]) : undefined

    // 设置为当天的0:00
    if (startDate) startDate.setHours(0, 0, 0, 0)
    if (endDate) endDate.setHours(0, 0, 0, 0)

    const startTime = startDate ? Math.floor(startDate.getTime() / 1000) : undefined
    const endTime = endDate ? Math.floor(endDate.getTime() / 1000) : undefined

    // 调用API，传入时间范围参数
    const response = await basic({
      startTime,
      endTime
    })

    if (response.code === 200 && response.data) {
      // 更新数据前先清空
      dashboardData.domain = response.data.domain || null
      dashboardData.revenueStatics = response.data.revenueStatics || null
      dashboardData.adsInsightsSum = response.data.adsInsightsSum || {}
      dashboardData.googleInsightsList = response.data.googleInsightsList || []
      dashboardData.adsInsightsVoList = response.data.adsInsightsVoList || []
      dashboardData.adsInsightsDomainList = response.data.adsInsightsDomainList || []
    }
  } catch (error) {
    console.error(t('dashboard.errors.fetchFailed'), error)
  } finally {
    loading.value = false
  }
}

// 处理日期范围变更
const handleQuery = () => {
  fetchData()
}

// 组件挂载时获取数据
onMounted(() => {
  isPermission.value = userStore.roleInfo.name != '广告渠道'
  fetchData()
})
</script>

<template>
  <div class="dashboard-container">
    <SubLoading :loading="loading">
      <div>
        <div class="flex justify-between items-center mb-4 md:flex-col md:items-start md:w-full md:gap-3 p-1">
          <div class="md:flex-row flex  gap-3 items-center">
            <div class="flex items-center">
              <label class="primary-label">{{ t('dashboard.dateRange') }}</label>
              <VaButtonDropdown preset="plain">
                <div class="flex items-center flex-col gap-2 date-shortcuts">
                  <VaButton size="small" preset="secondary" border-color="primary" @click="selectLastThreeDays">
                    {{ t('dashboard.dateRanges.lastThreeDays') }}
                  </VaButton>
                  <VaButton size="small" preset="secondary" border-color="primary" @click="selectLastWeek">
                    {{ t('dashboard.dateRanges.lastWeek') }}
                  </VaButton>
                  <VaButton size="small" preset="secondary" border-color="primary" @click="selectLastMonth">
                    {{ t('dashboard.dateRanges.lastMonth') }}
                  </VaButton>
                </div>
              </VaButtonDropdown>
            </div>
            <div class="flex flex-col gap-2 min-w-[260px]">
              <Datepicker v-model="dateRange" range locale="en-US" format="yyyy/MM/dd" :enable-time-picker="false"
                auto-apply :placeholder="t('dashboard.selectDateRange')" week-numbers="iso" week-num-name="We"
                class="w-full w-[260px]" @update:model-value="onDateRangeChange"
                :now-button-label="t('dashboard.today')" :dark="isDark" :clearable="false" >
              </Datepicker>
            </div>
          </div>
        </div>


        <!-- 概览卡片 -->
        <div class="overview-cards mb-2 ">
          <!-- 统一数据卡片 -->
          <UnifiedStatsCard v-if="showAdsInsights || showDomainStats || showRevenueStats"
            :adsInsightsData="dashboardData.adsInsightsSum" :domainData="dashboardData.domain"
            :revenueData="dashboardData.revenueStatics" />

        </div>

        <!-- 折线图区域 -->
        <div v-if="showAdsChartLine" class="mb-4">
          <AdsDataChartLine :adsInsightsVoList="dashboardData.adsInsightsVoList" />
        </div>

        <!-- 图表区域 -->
        <div class="charts-container">
          <!-- GA数据图表 -->
          <GADataChart v-if="showGAChart" :googleInsightsList="dashboardData.googleInsightsList" />
          <!-- ADS数据图表 -->
          <AdsDataChart v-if="showAdsChart" :adsInsightsDomainList="dashboardData.adsInsightsDomainList" />
        </div>
        <!-- 如果没有任何数据可显示，显示空状态 -->
        <div
          v-if="!showAdsInsights && !showDomainStats && !showRevenueStats && !showAdsChartLine && !showGAChart && !showAdsChart"
          class="empty-state-container">
          <VaCard class="empty-state-card">
            <VaCardContent>
              <div class="empty-state">
                <div class="icon-container">
                  <VaIcon name="mso-dashboard_customize" size="64px" color="primary" />
                </div>
                <h2 class="empty-state-title">{{ t('dashboard.emptyState.title') }}</h2>
                <p class="empty-state-description">{{ t('dashboard.emptyState.description') }}</p>
              </div>
            </VaCardContent>
          </VaCard>
        </div>
      </div>
    </SubLoading>
  </div>
</template>

<style scoped>
.dashboard-container {
  max-width: 2000px;
  margin: 0 auto;
  color: var(--va-primary-text);
  font-family: 'Poppins', 'PingFang SC', sans-serif;
}

.overview-cards {
  background-color: var(--va-background-primary) !important;
}

/* 图表容器 */
.charts-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

/* 空状态容器样式 */
.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 1rem;
}

.empty-state-card {
  max-width: 500px;
  width: 100%;
  border-radius: 16px;
  animation: fadeInUp 0.5s ease forwards;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 2rem;
}

.icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: var(--va-primary-opacity-2);
  margin-bottom: 1.5rem;
  animation: pulse 2s infinite ease-in-out;
}

.empty-state-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--va-primary-text);
}

.empty-state-description {
  font-size: 1rem;
  color: var(--va-secondary-text);
  margin-bottom: 1.5rem;
}

.empty-state-actions {
  margin-top: 1rem;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

/* 图表容器也使用Tailwind响应式 */
.charts-container {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-5 lg:gap-6 mb-8;
}

/* 空状态响应式 */
.empty-state {
  @apply p-6 sm:p-8;
}

.icon-container {
  @apply w-20 h-20 md:w-24 md:h-24;
}

.empty-state-title {
  @apply text-lg sm:text-xl md:text-2xl;
}
</style>

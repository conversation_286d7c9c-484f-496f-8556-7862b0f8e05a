<script setup>
import { defineProps, onMounted, ref, watch } from 'vue'
import Chart from 'chart.js/auto'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
    googleInsightsList: {
        type: Array,
        required: true,
        default: () => []
    }
})

const chartContainer = ref(null)
let chartInstance = null

// 格式化大数字，如 1000000 -> 1M
const formatNumber = (num) => {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num;
}

// 初始化图表
const initChart = () => {
    // 确保容器和数据都存在
    if (!chartContainer.value || props.googleInsightsList.length === 0) return

    // 获取canvas元素
    const canvas = document.getElementById('ga-chart-canvas')
    if (!canvas) return

    // 清理之前的图表实例
    if (chartInstance) {
        chartInstance.destroy()
        chartInstance = null
    }

    // 创建新的图表
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    chartInstance = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: props.googleInsightsList.map(item => item.appName),
            datasets: [
                {
                    label: t('dashboard.charts.ga.sessionDuration'),
                    data: props.googleInsightsList.map(item =>
                        item.sessionDurationPerUser || 0
                    ),
                    backgroundColor: 'rgba(255, 127, 80, 0.7)',
                    borderColor: 'rgba(255, 127, 80, 1)',
                    borderWidth: 2,
                    yAxisID: 'y1', // 使用右侧Y轴
                    tension: 0.3,
                    pointRadius: 3,
                    pointHoverRadius: 5
                },
                {
                    label: t('dashboard.charts.ga.bounceRate'),
                    data: props.googleInsightsList.map(item =>
                        item.bounceRate || 0
                    ),
                    backgroundColor: 'rgba(100, 116, 139, 0.7)',
                    borderColor: 'rgba(100, 116, 139, 1)',
                    borderWidth: 2,
                    yAxisID: 'y1', // 使用右侧Y轴
                    tension: 0.3,
                    pointRadius: 3,
                    pointHoverRadius: 5
                },
                {
                    label: t('dashboard.charts.ga.newUsers'),
                    data: props.googleInsightsList.map(item => {
                        return item.newUser || 0;
                    }),
                    backgroundColor: 'rgba(249, 115, 22, 0.7)', // 橙色
                    borderColor: 'rgba(249, 115, 22, 1)',
                    borderWidth: 2,
                    yAxisID: 'y', // 使用左侧Y轴
                    tension: 0.3,
                    pointRadius: 3,
                    pointHoverRadius: 5
                },
                {
                    label: t('dashboard.charts.ga.totalUsers'),
                    data: props.googleInsightsList.map(item => {
                        return item.totalUser || 0;
                    }),
                    backgroundColor: 'rgba(139, 92, 246, 0.7)', // 紫色
                    borderColor: 'rgba(139, 92, 246, 1)',
                    borderWidth: 2,
                    yAxisID: 'y', // 使用左侧Y轴
                    tension: 0.3,
                    pointRadius: 3,
                    pointHoverRadius: 5
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: t('dashboard.charts.ga.usersCount'),
                        color: 'rgba(139, 92, 246, 1)'
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    ticks: {
                        callback: function (value) {
                            return formatNumber(value);
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: t('dashboard.charts.ga.durationAndRate'),
                        color: 'rgba(255, 127, 80, 1)'
                    },
                    grid: {
                        drawOnChartArea: false, // 只显示一组网格线
                    },
                    max: 100, // 设置最大值为100，适合百分比和较小的时长值
                },
                x: {
                    grid: {
                        display: false
                    },
                    // title: {
                    //     display: true,
                    //     text: t('dashboard.charts.ga.appName')
                    // }
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 15,
                        font: {
                            size: 10
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    titleColor: '#4A4A4A',
                    bodyColor: '#4A4A4A',
                    borderColor: 'rgba(0, 0, 0, 0.05)',
                    borderWidth: 1,
                    padding: 12,
                    boxPadding: 6,
                    callbacks: {
                        label: function (context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }

                            const value = context.parsed.y;

                            // 根据数据集类型添加适当的格式和单位
                            if (context.dataset.label.includes(t('dashboard.charts.ga.newUsers')) ||
                                context.dataset.label.includes(t('dashboard.charts.ga.totalUsers'))) {
                                label += formatNumber(value);
                            } else if (context.dataset.label.includes(t('dashboard.charts.ga.bounceRate'))) {
                                label += value.toFixed(2) + '%';
                            } else if (context.dataset.label.includes(t('dashboard.charts.ga.sessionDuration'))) {
                                label += value.toFixed(2) + ' ' + t('dashboard.charts.ga.seconds');
                            } else {
                                label += value;
                            }

                            return label;
                        }
                    }
                },

            }
        }
    })
}

// 监听数据变化
watch(() => props.googleInsightsList, () => {
    // 使用setTimeout确保DOM已更新
    setTimeout(() => {
        initChart()
    }, 100)
}, { deep: true })

// 组件挂载时初始化图表
onMounted(() => {
    // 使用setTimeout确保DOM已完全渲染
    setTimeout(() => {
        initChart()
    }, 300)
})
</script>

<template>
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">{{ t('dashboard.charts.ga.title') }}</div>
        </div>
        <div class="chart-container" ref="chartContainer">
            <canvas id="ga-chart-canvas"></canvas>
        </div>
    </div>
</template>

<style scoped>
.chart-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 18px;
    padding: 25px;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    margin-bottom: 25px;
}

.chart-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-title {
    display: flex;
    justify-content: center;
    gap: 10px;
    width: 100%;
    font-size: 20px;
    font-weight: 600;
    color: var(--primary-text);
}

.chart-actions {
    display: flex;
    gap: 10px;
}

.chart-btn {
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 6px 12px;
    font-size: 14px;
    color: var(--secondary-text);
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-btn:hover {
    background: white;
    color: var(--accent-color);
    border-color: rgba(255, 127, 80, 0.3);
}

.chart-container {
    height: 300px;
    position: relative;
    width: 100%;
}

canvas {
    display: block;
    width: 100% !important;
    height: 100% !important;
}
</style>
<script setup>
import { defineProps, ref, computed } from 'vue'
import { VaBadge, VaIcon } from 'vuestic-ui'

const props = defineProps({
    revenueData: {
        type: Object,
        required: true,
        default: () => ({
            todayRev: 0,
            yesterdayRev: 0,
            thisMonthRev: 0,
            lastMonthRev: 0,
            sumRev: 0
        })
    },
    // 添加趋势数据，如果没有提供则默认为空对象
    trendData: {
        type: Object,
        default: () => ({})
    }
})

// 格式化金额
const formatCurrency = (amount) => {
    return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount)
}

// 计算趋势方向和百分比
const getTrend = (key) => {
    if (!props.trendData || !props.trendData[key]) return null

    const trend = props.trendData[key]
    return {
        direction: trend > 0 ? 'up' : trend < 0 ? 'down' : 'neutral',
        percentage: Math.abs(trend)
    }
}

// 获取各指标对应的图标 (使用mso图标)
const getIcon = (key) => {
    const icons = {
        todayRev: 'mso-today',
        yesterdayRev: 'mso-event',
        thisMonthRev: 'mso-calendar_month',
        lastMonthRev: 'mso-calendar_today',
        sumRev: 'mso-paid'
    }
    return icons[key] || 'mso-attach_money'
}
</script>

<template>
    <div
        class="bg-backgroundCardPrimary border border-backgroundBorder rounded-2xl p-5 sm:p-6 shadow-sm transition-all duration-300 ease-in-out flex flex-col h-full hover:translate-y-[-5px] hover:shadow-md">
        <div class="flex justify-between items-center mb-5 pb-4 border-b border-opacity-5">
            <div class="flex items-center gap-3 text-xl sm:text-2xl font-semibold text-textPrimary">
                <div class="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-xl bg-primary shadow-sm">
                    <VaIcon name="mso-payments" size="large" color="#fff" />
                </div>
                收入统计
            </div>
        </div>
        <div class="grid  xl:grid-cols-2 sm:grid-cols-1 gap-4 sm:gap-5 flex-grow">
            <div
                class="metric-item success-item flex flex-col justify-between p-4 sm:p-5 rounded-xl bg-backgroundPrimary border border-backgroundBorder transition-all duration-300 relative overflow-hidden min-h-[120px] sm:min-h-[140px] shadow-sm">
                <div class="metric-label flex items-center text-sm sm:text-base font-medium text-textPrimary mb-2 z-10">
                    <VaIcon :name="getIcon('todayRev')" size="medium" class="icon text-success mr-2" />
                    今日收入
                </div>
                <div class="metric-value text-xl sm:text-2xl md:text-[25px] font-bold text-success mb-2 z-10">
                    ${{ formatCurrency(revenueData.todayRev) }}
                </div>
                <div class="flex justify-between items-center w-full z-10">
                    <div class="text-xs sm:text-sm text-textPrimary opacity-80">每日实时收入</div>
                    <div class="trend-indicator px-2 py-1 rounded-xl text-xs font-semibold" v-if="getTrend('todayRev')">
                        <div :class="['trend-' + getTrend('todayRev').direction, 'flex items-center']">
                            <VaIcon v-if="getTrend('todayRev').direction === 'up'" name="mso-trending_up"
                                size="small" />
                            <VaIcon v-else-if="getTrend('todayRev').direction === 'down'" name="mso-trending_down"
                                size="small" />
                            {{ getTrend('todayRev').percentage }}%
                        </div>
                    </div>
                </div>
            </div>

            <div
                class="metric-item primary-item flex flex-col justify-between p-4 sm:p-5 rounded-xl bg-backgroundPrimary border border-backgroundBorder transition-all duration-300 relative overflow-hidden min-h-[120px] sm:min-h-[140px] shadow-sm">
                <div class="metric-label flex items-center text-sm sm:text-base font-medium text-textPrimary mb-2 z-10">
                    <VaIcon :name="getIcon('yesterdayRev')" size="medium" class="icon text-primary mr-2" />
                    昨日收入
                </div>
                <div class="metric-value text-xl sm:text-2xl md:text-[25px] font-bold text-primary mb-2 z-10">
                    ${{ formatCurrency(revenueData.yesterdayRev) }}
                </div>
                <div class="flex justify-between items-center w-full z-10">
                    <div class="text-xs sm:text-sm text-textPrimary opacity-80">昨日收入统计</div>
                    <div class="trend-indicator px-2 py-1 rounded-xl text-xs font-semibold"
                        v-if="getTrend('yesterdayRev')">
                        <div :class="['trend-' + getTrend('yesterdayRev').direction, 'flex items-center']">
                            <VaIcon v-if="getTrend('yesterdayRev').direction === 'up'" name="mso-trending_up"
                                size="small" />
                            <VaIcon v-else-if="getTrend('yesterdayRev').direction === 'down'" name="mso-trending_down"
                                size="small" />
                            {{ getTrend('yesterdayRev').percentage }}%
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="metric-item info-item flex flex-col justify-between p-4 sm:p-5 rounded-xl bg-backgroundPrimary border border-backgroundBorder transition-all duration-300 relative overflow-hidden min-h-[120px] sm:min-h-[140px] shadow-sm">
                <div class="metric-label flex items-center text-sm sm:text-base font-medium text-textPrimary mb-2 z-10">
                    <VaIcon :name="getIcon('thisMonthRev')" size="medium" class="icon text-info mr-2" />
                    本月收入
                </div>
                <div class="metric-value text-xl sm:text-2xl md:text-[25px] font-bold text-info mb-2 z-10">
                    ${{ formatCurrency(revenueData.thisMonthRev) }}
                </div>
                <div class="flex justify-between items-center w-full z-10">
                    <div class="text-xs sm:text-sm text-textPrimary opacity-80">本月累计收入</div>
                    <div class="trend-indicator px-2 py-1 rounded-xl text-xs font-semibold"
                        v-if="getTrend('thisMonthRev')">
                        <div :class="['trend-' + getTrend('thisMonthRev').direction, 'flex items-center']">
                            <VaIcon v-if="getTrend('thisMonthRev').direction === 'up'" name="mso-trending_up"
                                size="small" />
                            <VaIcon v-else-if="getTrend('thisMonthRev').direction === 'down'" name="mso-trending_down"
                                size="small" />
                            {{ getTrend('thisMonthRev').percentage }}%
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="metric-item secondary-item flex flex-col justify-between p-4 sm:p-5 rounded-xl bg-backgroundPrimary border border-backgroundBorder transition-all duration-300 relative overflow-hidden min-h-[120px] sm:min-h-[140px] shadow-sm">
                <div class="metric-label flex items-center text-sm sm:text-base font-medium text-textPrimary mb-2 z-10">
                    <VaIcon :name="getIcon('lastMonthRev')" size="medium" class="icon text-secondary mr-2" />
                    上月收入
                </div>
                <div class="metric-value text-xl sm:text-2xl md:text-[25px] font-bold text-secondary mb-2 z-10">
                    ${{ formatCurrency(revenueData.lastMonthRev) }}
                </div>
                <div class="flex justify-between items-center w-full z-10">
                    <div class="text-xs sm:text-sm text-textPrimary opacity-80">上月累计收入</div>
                    <div class="trend-indicator px-2 py-1 rounded-xl text-xs font-semibold"
                        v-if="getTrend('lastMonthRev')">
                        <div :class="['trend-' + getTrend('lastMonthRev').direction, 'flex items-center']">
                            <VaIcon v-if="getTrend('lastMonthRev').direction === 'up'" name="mso-trending_up"
                                size="small" />
                            <VaIcon v-else-if="getTrend('lastMonthRev').direction === 'down'" name="mso-trending_down"
                                size="small" />
                            {{ getTrend('lastMonthRev').percentage }}%
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="metric-item warning-item flex flex-col justify-between p-4 sm:p-5 rounded-xl bg-backgroundPrimary border border-backgroundBorder transition-all duration-300 relative overflow-hidden min-h-[120px] sm:min-h-[140px] shadow-sm">
                <div class="metric-label flex items-center text-sm sm:text-base font-medium text-textPrimary mb-2 z-10">
                    <VaIcon :name="getIcon('sumRev')" size="medium" class="icon text-warning mr-2" />
                    总收入
                </div>
                <div class="metric-value text-xl sm:text-2xl md:text-[25px] font-bold text-warning mb-2 z-10">
                    ${{ formatCurrency(revenueData.sumRev) }}
                </div>
                <div class="flex justify-between items-center w-full z-10">
                    <div class="text-xs sm:text-sm text-textPrimary opacity-80">历史累计总收入</div>
                    <div class="trend-indicator px-2 py-1 rounded-xl text-xs font-semibold" v-if="getTrend('sumRev')">
                        <div :class="['trend-' + getTrend('sumRev').direction, 'flex items-center']">
                            <VaIcon v-if="getTrend('sumRev').direction === 'up'" name="mso-trending_up" size="small" />
                            <VaIcon v-else-if="getTrend('sumRev').direction === 'down'" name="mso-trending_down"
                                size="small" />
                            {{ getTrend('sumRev').percentage }}%
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* 保留必要的伪元素和特殊样式 */
.metric-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 100%;
    opacity: 0.7;
}

.metric-item {
    position: relative;
    max-height: 140px;
}

.success-item::before {
    background: var(--va-success);
}

.secondary-item::before {
    background: var(--va-secondary);
}

.info-item::before {
    background: var(--va-info);
}

.warning-item::before {
    background: var(--va-warning);
}

.primary-item::before {
    background: var(--va-primary);
}

.danger-item::before {
    background: var(--va-danger);
}

.success-item:hover {
    background: linear-gradient(145deg, rgba(var(--va-success-rgb), 0.05), rgba(var(--va-success-rgb), 0.15));
}

.secondary-item:hover {
    background: linear-gradient(145deg, rgba(var(--va-secondary-rgb), 0.05), rgba(var(--va-secondary-rgb), 0.15));
}

.info-item:hover {
    background: linear-gradient(145deg, rgba(var(--va-info-rgb), 0.05), rgba(var(--va-info-rgb), 0.15));
}

.warning-item:hover {
    background: linear-gradient(145deg, rgba(var(--va-warning-rgb), 0.05), rgba(var(--va-warning-rgb), 0.15));
}

.primary-item:hover {
    background: linear-gradient(145deg, rgba(var(--va-primary-rgb), 0.05), rgba(var(--va-primary-rgb), 0.15));
}

.danger-item:hover {
    background: linear-gradient(145deg, rgba(var(--va-danger-rgb), 0.05), rgba(var(--va-danger-rgb), 0.15));
}

/* 趋势指示器样式 */
.trend-up {
    color: var(--va-success);
}

.trend-down {
    color: var(--va-danger);
}

.trend-neutral {
    color: var(--va-gray);
}
</style>
<script setup>
import { defineProps, onMounted, ref, watch, onUnmounted } from 'vue'
import Chart from 'chart.js/auto'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
    adsInsightsVoList: {
        type: Array,
        required: true,
        default: () => []
    }
})

const chartContainer = ref(null)
let chartInstance = null
let resizeObserver = null

// 时间戳转换为日期格式
const formatDate = (timestamp) => {
    const date = new Date(timestamp * 1000)
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${month}/${day}`
}

// 格式化数字带千分位
const formatNumber = (num) => {
    return new Intl.NumberFormat('zh-CN').format(num)
}

// 格式化百分比
const formatPercent = (value) => {
    return value ? value.toFixed(2) + '%' : '0.00%'
}

// 处理窗口大小变化，重新渲染图表
const handleResize = () => {
    if (chartInstance) {
        // Chart.js 的 resize 是自动的，这里我们只需要延迟重绘
        setTimeout(() => {
            chartInstance.update();
        }, 100);
    }
}

// 自定义工具提示回调函数
const customTooltip = {
    callbacks: {
        title: (tooltipItems) => {
            return tooltipItems[0].label;
        },
        label: (context) => {
            let label = context.dataset.label || '';
            if (label) {
                label += ': ';
            }

            const value = context.parsed.y;

            // 根据不同的数据集应用不同的格式化
            if (label.includes('adsClick') || label.includes('clickRate')) {
                label += formatNumber(value);
            } else if (label.includes('Rate')) {
                label += formatPercent(value);
            } else {
                label += formatNumber(value);
            }

            return label;
        },
        footer: (tooltipItems) => {
            // 获取当前悬停的数据点的索引
            const dataIndex = tooltipItems[0].dataIndex;
            const dataPoint = props.adsInsightsVoList[dataIndex];

            if (!dataPoint) return '';

            // 添加额外的指标信息
            const lines = [];
            if (dataPoint.clickRate !== undefined) {
                lines.push(`${t('dashboard.charts.adsLine.clickRate')}: ${formatPercent(dataPoint.clickRate)}`);
            }
            if (dataPoint.matchRate !== undefined) {
                lines.push(`${t('dashboard.charts.adsLine.matchRate')}: ${formatPercent(dataPoint.matchRate)}`);
            }
            if (dataPoint.impressionRate !== undefined) {
                lines.push(`${t('dashboard.charts.adsLine.impressionRate')}: ${formatPercent(dataPoint.impressionRate)}`);
            }
            if (dataPoint.estimateRevenue !== undefined) {
                lines.push(`${t('dashboard.charts.adsLine.estimatedRevenue')}: $${dataPoint.estimateRevenue.toFixed(2)}`);
            }

            return lines;
        }
    }
}

// 初始化图表
const initChart = () => {
    // 确保容器和数据都存在
    if (!chartContainer.value || props.adsInsightsVoList.length === 0) return

    // 获取canvas元素
    const canvas = document.getElementById('ads-chart-canvas')
    if (!canvas) return

    // 清理之前的图表实例
    if (chartInstance) {
        chartInstance.destroy()
        chartInstance = null
    }

    // 标签映射
    const labelMap = {
        'adsDisplay': t('dashboard.charts.adsLine.adDisplay'),
        'adsClick': t('dashboard.charts.adsLine.adClick'),
        'adsRequest': t('dashboard.charts.adsLine.adRequest'),
        'adsMatch': t('dashboard.charts.adsLine.adMatch'),
        'adsImpression': t('dashboard.charts.adsLine.adImpression')
    }

    // 创建新的图表
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    chartInstance = new Chart(ctx, {
        type: 'line',
        data: {
            labels: props.adsInsightsVoList.map(item => formatDate(item.statTime)),
            datasets: [
                {
                    label: labelMap.adsDisplay,
                    data: props.adsInsightsVoList.map(item => item.adsDisplay),
                    borderColor: 'rgba(138, 43, 226, 1)', // 紫色
                    backgroundColor: 'rgba(138, 43, 226, 0.1)',
                    borderWidth: 1.5, // 边框宽度
                    tension: 0.3, // 曲线平滑度
                    pointRadius: 3, // 点的大小
                    pointHoverRadius: 5, // 悬停时点的大小
                    pointBackgroundColor: 'rgba(138, 43, 226, 1)',
                },
                {
                    label: labelMap.adsClick,
                    data: props.adsInsightsVoList.map(item => item.adsClick),
                    borderColor: 'rgba(59, 130, 246, 1)', // 保持蓝色不变
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 1.5,
                    tension: 0.3,
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    pointBackgroundColor: 'rgba(59, 130, 246, 1)',
   
                },
                {
                    label: labelMap.adsRequest,
                    data: props.adsInsightsVoList.map(item => item.adsRequest),
                    borderColor: 'rgba(34, 197, 94, 1)', // 绿色
                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                    borderWidth: 1.5,
                    tension: 0.3,
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    pointBackgroundColor: 'rgba(34, 197, 94, 1)',
                },
                {
                    label: labelMap.adsMatch,
                    data: props.adsInsightsVoList.map(item => item.adsMatch),
                    borderColor: 'rgba(249, 115, 22, 1)', // 橙色
                    backgroundColor: 'rgba(249, 115, 22, 0.1)',
                    borderWidth: 1.5,
                    tension: 0.3,
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    pointBackgroundColor: 'rgba(249, 115, 22, 1)',
                },
                {
                    label: labelMap.adsImpression,
                    data: props.adsInsightsVoList.map(item => item.adsImpression),
                    borderColor: 'rgba(239, 68, 68, 1)', // 红色 
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    borderWidth: 1.5,
                    tension: 0.3,
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    pointBackgroundColor: 'rgba(239, 68, 68, 1)',
                }
            ],

        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index', // 显示同一x轴上的所有点的tooltip
                intersect: false
            },
            scales: {
                y: {
                    beginAtZero: true, // 设置y轴从0开始
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                 
                    ticks: {
                        callback: function (value) {
                            if (value >= 1000000) {
                                return (value / 1000000).toFixed(1) + 'M';
                            } else if (value >= 1000) {
                                return (value / 1000).toFixed(1) + 'K';
                            }
                            return value;
                        }
                    },
    
                 
                },
        
                x: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                 
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        pointStyle: 'circle',
                        padding: 20,
                        font: {
                            size: 10
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.75)',
                    titleColor: 'white',
                    titleFont: {
                        size: 14,
                        weight: 'bold'
                    },
                    bodyColor: 'white',
                    bodyFont: {
                        size: 13
                    },
                    lineHeight: 3.5,
                    bodySpacing: 8,
                    titleMarginBottom: 10,
                    footerMarginTop: 10,
                    footerColor: 'rgba(255, 255, 255, 0.8)',
                    footerFont: {
                        size: 12,
                        style: 'italic'
                    },
                    padding: 14,
                    cornerRadius: 8,
                    displayColors: true,
                    borderColor: 'rgba(255, 255, 255, 0.2)',
                    borderWidth: 1,
                    callbacks: {
                        title: (tooltipItems) => {
                            return t('dashboard.charts.adsLine.date') + ': ' + tooltipItems[0].label;
                        },
                        label: (context) => {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }

                            const value = context.parsed.y;
                            label += formatNumber(value);

                            return label;
                        },
                        afterLabel: (context) => {
                            // 获取当前悬停的数据点的索引和数据集索引
                            const dataIndex = context.dataIndex;
                            const datasetIndex = context.datasetIndex;
                            const datasetLabel = context.dataset.label;
                            const dataPoint = props.adsInsightsVoList[dataIndex];

                            if (!dataPoint) return '';

                            // 根据不同的数据集显示相关的额外信息
                            const additionalInfo = [];

                            // 如果是广告点击数据集，显示点击率
                            // if (datasetLabel === labelMap.adsClick && dataPoint.clickRate !== undefined) {
                            //     additionalInfo.push(`${t('dashboard.charts.adsLine.clickRate')}: ${formatPercent(dataPoint.clickRate)}`);
                            // }

                            // 如果是广告匹配数据集，显示匹配率
                            if (datasetLabel === labelMap.adsMatch && dataPoint.matchRate !== undefined) {
                                additionalInfo.push(`${t('dashboard.charts.adsLine.matchRate')}: ${formatPercent(dataPoint.matchRate)}`);
                            }

                            // 如果是广告展现数据集，显示展现率
                            if (datasetLabel === labelMap.adsImpression && dataPoint.impressionRate !== undefined) {
                                additionalInfo.push(`${t('dashboard.charts.adsLine.impressionRate')}: ${formatPercent(dataPoint.impressionRate)}`);
                            }

                            return additionalInfo;
                        },
                        footer: (tooltipItems) => {
                            // 获取当前悬停的数据点的索引
                            const dataIndex = tooltipItems[0].dataIndex;
                            const dataPoint = props.adsInsightsVoList[dataIndex];

                            if (!dataPoint || !dataPoint.estimateRevenue) return '';

                            // 添加额外的收益信息
                            return [`${t('dashboard.charts.adsLine.estRev')}: $${formatNumber(dataPoint.estimateRevenue)}`,
                            dataPoint.ecpm ? `${t('dashboard.charts.adsLine.ecpm')}: $${dataPoint.ecpm.toFixed(2)}` : ''];
                        }
                    }
                }
            }
        }
    })
}

// 监听数据变化
watch(() => props.adsInsightsVoList, () => {
    // 使用setTimeout确保DOM已更新
    setTimeout(() => {
        initChart()
    }, 100)
}, { deep: true })

// 组件挂载时初始化图表
onMounted(() => {
    // 使用setTimeout确保DOM已完全渲染
    setTimeout(() => {
        initChart()
    }, 300)

    // 创建一个ResizeObserver来监听容器大小变化
    resizeObserver = new ResizeObserver(handleResize)
    resizeObserver.observe(chartContainer.value)
})

// 组件卸载时清理ResizeObserver
onUnmounted(() => {
    if (resizeObserver) {
        resizeObserver.disconnect()
    }
})
</script>

<template>
    <div class="chart-card">
        <div v-if="props.adsInsightsVoList.length === 0" class="chart-empty">
            <div class="empty-message">
                <VaIcon name="mso-bar_chart" size="large" color="primary" />
                <span>{{ t('dashboard.noData') }}</span>
            </div>
        </div>
        <div v-else class="chart-container" ref="chartContainer">
            <canvas id="ads-chart-canvas"></canvas>
            <div class="chart-footnote">* {{ t('dashboard.charts.adsLine.hoverToViewMore') }}</div>
        </div>
    </div>
</template>

<style scoped>
.chart-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 18px;
    padding: 25px;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    margin-bottom: 25px;
}

.chart-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.chart-header {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-bottom: 20px;
}

.chart-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--va-primary);
    margin-bottom: 5px;
}

.chart-subtitle {
    font-size: 14px;
    color: var(--va-text-secondary);
}

.chart-footnote {
    font-size: 12px;
    color: var(--va-text-secondary);
    font-style: italic;
    text-align: right;
    margin-bottom: 20px;
}

.chart-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
    border: 1px dashed var(--va-border);
    border-radius: 10px;
    background-color: rgba(var(--va-primary-rgb), 0.05);
}

.empty-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    color: var(--va-text-secondary);
}

.chart-actions {
    display: flex;
    gap: 10px;
}

.chart-btn {
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 6px 12px;
    font-size: 14px;
    color: var(--secondary-text);
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-btn:hover {
    background: white;
    color: var(--accent-color);
    border-color: rgba(255, 127, 80, 0.3);
}

.chart-container {
    height: 300px;
    position: relative;
    width: 100%;
    transition: all 0.3s ease;
}

.chart-container:hover {
    transform: scale(1.01);
}

canvas {
    display: block;
    width: 100% !important;
    height: 100% !important;
}

@media (max-width: 768px) {
    .chart-card {
        padding: 15px;
    }

    .chart-title {
        font-size: 16px;
    }

    .chart-subtitle {
        font-size: 12px;
    }

    .chart-container {
        height: 250px;
    }
}
</style>
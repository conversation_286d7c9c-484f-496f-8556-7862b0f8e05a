<script setup>
import { defineProps, ref, onMounted, computed } from 'vue'
import { VaBadge, VaIcon } from 'vuestic-ui'
import { useUserStore } from '@/stores/user-store'
const userStore = useUserStore()
const isPermission = ref(false)

onMounted(() => {
    isPermission.value = userStore.roleInfo.name != '广告渠道'
})

// 域名统计概览
const props = defineProps({
    domainData: {
        type: Object,
        required: true,
        default: () => ({
            sumCount: 0,
            privateCount: 0,
            applyInCount: 0,
            passCount: 0,
            useCount: 0,
            recycleCount: 0,
            secondDomainCount: 0
        })
    },
    // 添加趋势数据，如果没有提供则默认为空对象
    trendData: {
        type: Object,
        default: () => ({})
    }
})

// 格式化数字带千分位
const formatNumber = (num) => {
    return new Intl.NumberFormat('zh-CN').format(num)
}

// 计算趋势方向和百分比
const getTrend = (key) => {
    if (!props.trendData || !props.trendData[key]) return null

    const trend = props.trendData[key]
    return {
        direction: trend > 0 ? 'up' : trend < 0 ? 'down' : 'neutral',
        percentage: Math.abs(trend)
    }
}

// 获取各指标对应的图标 (使用mso图标)
const getIcon = (key) => {
    const icons = {
        sumCount: 'mso-analytics',
        useCount: 'mso-rocket_launch',
        privateCount: 'mso-add_circle',
        applyInCount: 'mso-pending',
        passCount: 'mso-verified',
        recycleCount: 'mso-recycling'
    }
    return icons[key] || 'mso-domain'
}
</script>

<template>
    <div
        class="bg-backgroundCardPrimary border border-backgroundBorder rounded-2xl p-5 sm:p-6 shadow-sm transition-all duration-300 ease-in-out flex flex-col h-full hover:translate-y-[-5px] hover:shadow-md">
        <div class="flex justify-between items-center mb-5 pb-4 border-b border-opacity-5">
            <div class="flex items-center gap-3 text-xl sm:text-2xl font-semibold text-textPrimary">
                <div class="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-xl bg-primary shadow-sm">
                    <VaIcon name="mso-language" size="large" color="#fff" />
                </div>
                域名统计概览
            </div>
        </div>
        <div class="grid  xl:grid-cols-2 sm:grid-cols-1 gap-4 sm:gap-5 flex-grow">
            <div
                class="metric-item success-item flex flex-col justify-between p-4 sm:p-5 rounded-xl bg-backgroundPrimary border border-backgroundBorder transition-all duration-300 relative overflow-hidden min-h-[120px] sm:min-h-[140px] shadow-sm">
                <div class="metric-label flex items-center text-sm sm:text-base font-medium text-textPrimary mb-2 z-10">
                    <VaIcon :name="getIcon('sumCount')" size="medium" class="icon text-success mr-2" />
                    总域名数
                </div>
                <div class="metric-value text-xl sm:text-2xl md:text-[25px] font-bold text-success mb-2 z-10">
                    {{ formatNumber(domainData.sumCount) }}
                </div>
                <div class="flex justify-between items-center w-full z-10">
                    <div class="text-xs sm:text-sm text-textPrimary opacity-80">{{ isPermission ? '历史累计总数' : '渠道域名总数' }}
                    </div>
                    <div class="trend-indicator px-2 py-1 rounded-xl text-xs font-semibold" v-if="getTrend('sumCount')">
                        <div :class="['trend-' + getTrend('sumCount').direction, 'flex items-center']">
                            <VaIcon v-if="getTrend('sumCount').direction === 'up'" name="mso-trending_up"
                                size="small" />
                            <VaIcon v-else-if="getTrend('sumCount').direction === 'down'" name="mso-trending_down"
                                size="small" />
                            {{ getTrend('sumCount').percentage }}%
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="isPermission"
                class="metric-item secondary-item flex flex-col justify-between p-4 sm:p-5 rounded-xl bg-backgroundPrimary border border-backgroundBorder transition-all duration-300 relative overflow-hidden min-h-[120px] sm:min-h-[140px] shadow-sm">
                <div class="metric-label flex items-center text-sm sm:text-base font-medium text-textPrimary mb-2 z-10">
                    <VaIcon :name="getIcon('useCount')" size="medium" class="icon text-secondary mr-2" />
                    推广中
                </div>
                <div class="metric-value text-xl sm:text-2xl md:text-[25px] font-bold text-secondary mb-2 z-10">
                    {{ formatNumber(domainData.useCount) }}
                </div>
                <div class="flex justify-between items-center w-full z-10">
                    <div class="text-xs sm:text-sm text-textPrimary opacity-80">活跃推广域名</div>
                    <div class="trend-indicator px-2 py-1 rounded-xl text-xs font-semibold" v-if="getTrend('useCount')">
                        <div :class="['trend-' + getTrend('useCount').direction, 'flex items-center']">
                            <VaIcon v-if="getTrend('useCount').direction === 'up'" name="mso-trending_up"
                                size="small" />
                            <VaIcon v-else-if="getTrend('useCount').direction === 'down'" name="mso-trending_down"
                                size="small" />
                            {{ getTrend('useCount').percentage }}%
                        </div>
                    </div>
                </div>
            </div>
            <div class="metric-item info-item flex flex-col justify-between p-4 sm:p-5 rounded-xl bg-backgroundPrimary border border-backgroundBorder transition-all duration-300 relative overflow-hidden min-h-[120px] sm:min-h-[140px] shadow-sm"
                v-if="isPermission">
                <div class="metric-label flex items-center text-sm sm:text-base font-medium text-textPrimary mb-2 z-10">
                    <VaIcon :name="getIcon('privateCount')" size="medium" class="icon text-info mr-2" />
                    本周新增
                </div>
                <div class="metric-value text-xl sm:text-2xl md:text-[25px] font-bold text-info mb-2 z-10">
                    {{ formatNumber(domainData.privateCount) }}
                </div>
                <div class="flex justify-between items-center w-full z-10">
                    <div class="text-xs sm:text-sm text-textPrimary opacity-80">本周上新</div>
                    <div class="trend-indicator px-2 py-1 rounded-xl text-xs font-semibold"
                        v-if="getTrend('privateCount')">
                        <div :class="['trend-' + getTrend('privateCount').direction, 'flex items-center']">
                            <VaIcon v-if="getTrend('privateCount').direction === 'up'" name="mso-trending_up"
                                size="small" />
                            <VaIcon v-else-if="getTrend('privateCount').direction === 'down'" name="mso-trending_down"
                                size="small" />
                            {{ getTrend('privateCount').percentage }}%
                        </div>
                    </div>
                </div>
            </div>
            <div class="metric-item warning-item flex flex-col justify-between p-4 sm:p-5 rounded-xl bg-backgroundPrimary border border-backgroundBorder transition-all duration-300 relative overflow-hidden min-h-[120px] sm:min-h-[140px] shadow-sm"
                v-if="isPermission">
                <div class="metric-label flex items-center text-sm sm:text-base font-medium text-textPrimary mb-2 z-10">
                    <VaIcon :name="getIcon('applyInCount')" size="medium" class="icon text-warning mr-2" />
                    申请数量
                </div>
                <div class="metric-value text-xl sm:text-2xl md:text-[25px] font-bold text-warning mb-2 z-10">
                    {{ formatNumber(domainData.applyInCount) }}
                </div>
                <div class="flex justify-between items-center w-full z-10">
                    <div class="text-xs sm:text-sm text-textPrimary opacity-80">待审核域名</div>
                    <div class="trend-indicator px-2 py-1 rounded-xl text-xs font-semibold"
                        v-if="getTrend('applyInCount')">
                        <div :class="['trend-' + getTrend('applyInCount').direction, 'flex items-center']">
                            <VaIcon v-if="getTrend('applyInCount').direction === 'up'" name="mso-trending_up"
                                size="small" />
                            <VaIcon v-else-if="getTrend('applyInCount').direction === 'down'" name="mso-trending_down"
                                size="small" />
                            {{ getTrend('applyInCount').percentage }}%
                        </div>
                    </div>
                </div>
            </div>
            <div class="metric-item primary-item flex flex-col justify-between p-4 sm:p-5 rounded-xl bg-backgroundPrimary border border-backgroundBorder transition-all duration-300 relative overflow-hidden min-h-[120px] sm:min-h-[140px] shadow-sm"
                v-if="isPermission">
                <div class="metric-label flex items-center text-sm sm:text-base font-medium text-textPrimary mb-2 z-10">
                    <VaIcon :name="getIcon('passCount')" size="medium" class="icon text-primary mr-2" />
                    过站域名
                </div>
                <div class="metric-value text-xl sm:text-2xl md:text-[25px] font-bold text-primary mb-2 z-10">
                    {{ formatNumber(domainData.passCount) }}
                </div>
                <div class="flex justify-between items-center w-full z-10">
                    <div class="text-xs sm:text-sm text-textPrimary opacity-80">通过率 75.6%</div>
                    <div class="trend-indicator px-2 py-1 rounded-xl text-xs font-semibold"
                        v-if="getTrend('passCount')">
                        <div :class="['trend-' + getTrend('passCount').direction, 'flex items-center']">
                            <VaIcon v-if="getTrend('passCount').direction === 'up'" name="mso-trending_up"
                                size="small" />
                            <VaIcon v-else-if="getTrend('passCount').direction === 'down'" name="mso-trending_down"
                                size="small" />
                            {{ getTrend('passCount').percentage }}%
                        </div>
                    </div>
                </div>
            </div>
            <div class="metric-item danger-item flex flex-col justify-between p-4 sm:p-5 rounded-xl bg-backgroundPrimary border border-backgroundBorder transition-all duration-300 relative overflow-hidden min-h-[120px] sm:min-h-[140px] shadow-sm"
                v-if="isPermission">
                <div class="metric-label flex items-center text-sm sm:text-base font-medium text-textPrimary mb-2 z-10">
                    <VaIcon :name="getIcon('recycleCount')" size="medium" class="icon text-danger mr-2" />
                    回收域名
                </div>
                <div class="metric-value text-xl sm:text-2xl md:text-[25px] font-bold text-danger mb-2 z-10">
                    {{ formatNumber(domainData.recycleCount) }}
                </div>
                <div class="flex justify-between items-center w-full z-10">
                    <div class="text-xs sm:text-sm text-textPrimary opacity-80">已回收处理</div>
                    <div class="trend-indicator px-2 py-1 rounded-xl text-xs font-semibold"
                        v-if="getTrend('recycleCount')">
                        <div :class="['trend-' + getTrend('recycleCount').direction, 'flex items-center']">
                            <VaIcon v-if="getTrend('recycleCount').direction === 'up'" name="mso-trending_up"
                                size="small" />
                            <VaIcon v-else-if="getTrend('recycleCount').direction === 'down'" name="mso-trending_down"
                                size="small" />
                            {{ getTrend('recycleCount').percentage }}%
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* 保留必要的伪元素和特殊样式 */
.metric-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 100%;
    opacity: 0.7;
}

.success-item::before {
    background: var(--va-success);
}

.secondary-item::before {
    background: var(--va-secondary);
}

.info-item::before {
    background: var(--va-info);
}

.warning-item::before {
    background: var(--va-warning);
}

.primary-item::before {
    background: var(--va-primary);
}

.danger-item::before {
    background: var(--va-danger);
}

.success-item:hover {
    background: linear-gradient(145deg, rgba(var(--va-success-rgb), 0.05), rgba(var(--va-success-rgb), 0.15));
}

.secondary-item:hover {
    background: linear-gradient(145deg, rgba(var(--va-secondary-rgb), 0.05), rgba(var(--va-secondary-rgb), 0.15));
}

.info-item:hover {
    background: linear-gradient(145deg, rgba(var(--va-info-rgb), 0.05), rgba(var(--va-info-rgb), 0.15));
}

.warning-item:hover {
    background: linear-gradient(145deg, rgba(var(--va-warning-rgb), 0.05), rgba(var(--va-warning-rgb), 0.15));
}

.primary-item:hover {
    background: linear-gradient(145deg, rgba(var(--va-primary-rgb), 0.05), rgba(var(--va-primary-rgb), 0.15));
}

.metric-item {
    position: relative;
    max-height: 140px;
}

.danger-item:hover {
    background: linear-gradient(145deg, rgba(var(--va-danger-rgb), 0.05), rgba(var(--va-danger-rgb), 0.15));
}

/* 趋势指示器样式 */
.trend-up {
    color: var(--va-success);
}

.trend-down {
    color: var(--va-danger);
}

.trend-neutral {
    color: var(--va-gray);
}
</style>
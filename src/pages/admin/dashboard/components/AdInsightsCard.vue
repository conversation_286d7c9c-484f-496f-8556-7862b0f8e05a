<script setup>
import { defineProps, ref, computed } from 'vue'
import { VaBadge, VaIcon } from 'vuestic-ui'

const props = defineProps({
    adsInsightsData: {
        type: Object,
        required: true,
        default: () => ({
            adsRequest: 0,
            adsMatch: 0,
            matchRate: 0,
            adsImpression: 0,
            adsDisplay: 0,
            adsClick: 0,
            clickRate: 0,
            estimateRevenue: 0,
            ecpm: 0,
            activeViewRate: 0,
            impressionRate: 0,
            ecpc: 0,
            originalRevenue: 0
        })
    },
    // 添加趋势数据，如果没有提供则默认为空对象
    trendData: {
        type: Object,
        default: () => ({})
    }
})

// 检查是否显示原始收益
const hasOriginalRevenue = computed(() => {
    return props.adsInsightsData &&
        props.adsInsightsData.originalRevenue !== undefined &&
        props.adsInsightsData.originalRevenue !== null
})

// 格式化数字带千分位
const formatNumber = (num) => {
    return new Intl.NumberFormat('zh-CN').format(num)
}

// 格式化金额
const formatCurrency = (amount) => {
    return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount)
}

// 格式化百分比
const formatPercent = (value) => {
    if (!value) return '0.00%'
    return Number(value).toFixed(2) + '%'
}

// 计算趋势方向和百分比
const getTrend = (key) => {
    if (!props.trendData || !props.trendData[key]) return null

    const trend = props.trendData[key]
    return {
        direction: trend > 0 ? 'up' : trend < 0 ? 'down' : 'neutral',
        percentage: Math.abs(trend)
    }
}

// 获取各指标对应的图标 (使用mso图标)
const getIcon = (key) => {
    const icons = {
        adsRequest: 'mso-ads_click',
        adsImpression: 'mso-visibility',
        adsClick: 'mso-touch_app',
        estimateRevenue: 'mso-attach_money',
        originalRevenue: 'mso-monetization_on',
        activeViewRate: 'mso-monitoring',
        ecpc: 'mso-price_check'
    }
    return icons[key] || 'mso-insert_chart'
}
</script>

<template>
    <div
        class="bg-backgroundCardPrimary border border-backgroundBorder rounded-2xl p-5 sm:p-6 shadow-sm transition-all duration-300 ease-in-out flex flex-col h-full hover:translate-y-[-5px] hover:shadow-md">
        <div class="flex justify-between items-center mb-5 pb-4 border-b border-opacity-5">
            <div class="flex items-center gap-3 text-xl sm:text-2xl font-semibold text-textPrimary">
                <div class="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-xl bg-primary shadow-sm">
                    <VaIcon name="mso-query_stats" size="large" color="#fff" />
                </div>
                实时广告数据
            </div>
        </div>
        <div class="grid  xl:grid-cols-2 sm:grid-cols-1 gap-4 sm:gap-5 flex-grow">
            <div
                class="metric-item info-item flex flex-col justify-between p-4 sm:p-5 rounded-xl bg-backgroundPrimary border border-backgroundBorder transition-all duration-300 relative overflow-hidden min-h-[120px] sm:min-h-[140px] shadow-sm">
                <div class="metric-label flex items-center text-sm   font-medium text-textPrimary mb-2 z-10">
                    <VaIcon :name="getIcon('adsRequest')" size="medium" class="icon text-info mr-2" />
                    Ad Request
                </div>
                <div class="metric-value text-xl sm:text-2xl md:text-[25px] font-bold text-info mb-2 z-10">
                    {{ formatNumber(adsInsightsData.adsRequest) }}
                </div>
                <div class="flex justify-between items-center w-full z-10">
                    <div class="text-xs sm:text-sm text-textPrimary opacity-80">Match Rate: {{
                        formatPercent(adsInsightsData.matchRate) }}</div>
                    <div class="trend-indicator px-2 py-1 rounded-xl text-xs font-semibold"
                        v-if="getTrend('adsRequest')">
                        <div :class="['trend-' + getTrend('adsRequest').direction, 'flex items-center']">
                            <VaIcon v-if="getTrend('adsRequest').direction === 'up'" name="mso-trending_up"
                                size="small" />
                            <VaIcon v-else-if="getTrend('adsRequest').direction === 'down'" name="mso-trending_down"
                                size="small" />
                            {{ getTrend('adsRequest').percentage }}%
                        </div>
                    </div>
                </div>
            </div>

            <div
                class="metric-item primary-item flex flex-col justify-between p-4 sm:p-5 rounded-xl bg-backgroundPrimary border border-backgroundBorder transition-all duration-300 relative overflow-hidden min-h-[120px] sm:min-h-[140px] shadow-sm">
                <div class="metric-label flex items-center text-sm   font-medium text-textPrimary mb-2 z-10">
                    <VaIcon :name="getIcon('adsImpression')" size="medium" class="icon text-primary mr-2" />
                    Ad Impression
                </div>
                <div class="metric-value text-xl sm:text-2xl md:text-[25px] font-bold text-primary mb-2 z-10">
                    {{ formatNumber(adsInsightsData.adsImpression) }}
                </div>
                <div class="flex justify-between items-center w-full z-10">
                    <div class="text-xs sm:text-sm text-textPrimary opacity-80">Impression Rate: {{
                        formatPercent(adsInsightsData.impressionRate) }}</div>
                    <div class="trend-indicator px-2 py-1 rounded-xl text-xs font-semibold"
                        v-if="getTrend('adsImpression')">
                        <div :class="['trend-' + getTrend('adsImpression').direction, 'flex items-center']">
                            <VaIcon v-if="getTrend('adsImpression').direction === 'up'" name="mso-trending_up"
                                size="small" />
                            <VaIcon v-else-if="getTrend('adsImpression').direction === 'down'" name="mso-trending_down"
                                size="small" />
                            {{ getTrend('adsImpression').percentage }}%
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="metric-item secondary-item flex flex-col justify-between p-4 sm:p-5 rounded-xl bg-backgroundPrimary border border-backgroundBorder transition-all duration-300 relative overflow-hidden min-h-[120px] sm:min-h-[140px] shadow-sm">
                <div class="metric-label flex items-center text-sm   font-medium text-textPrimary mb-2 z-10">
                    <VaIcon :name="getIcon('adsClick')" size="medium" class="icon text-secondary mr-2" />
                    Ad Click
                </div>
                <div class="metric-value text-xl sm:text-2xl md:text-[25px] font-bold text-secondary mb-2 z-10">
                    {{ formatNumber(adsInsightsData.adsClick) }}
                </div>
                <div class="flex justify-between items-center w-full z-10">
                    <div class="text-xs sm:text-sm text-textPrimary opacity-80">Click Rate: {{
                        formatPercent(adsInsightsData.clickRate) }}</div>
                    <div class="trend-indicator px-2 py-1 rounded-xl text-xs font-semibold" v-if="getTrend('adsClick')">
                        <div :class="['trend-' + getTrend('adsClick').direction, 'flex items-center']">
                            <VaIcon v-if="getTrend('adsClick').direction === 'up'" name="mso-trending_up"
                                size="small" />
                            <VaIcon v-else-if="getTrend('adsClick').direction === 'down'" name="mso-trending_down"
                                size="small" />
                            {{ getTrend('adsClick').percentage }}%
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="metric-item success-item flex flex-col justify-between p-4 sm:p-5 rounded-xl bg-backgroundPrimary border border-backgroundBorder transition-all duration-300 relative overflow-hidden min-h-[120px] sm:min-h-[140px] shadow-sm">
                <div class="metric-label flex items-center text-sm   font-medium text-textPrimary mb-2 z-10">
                    <VaIcon :name="getIcon('estimateRevenue')" size="medium" class="icon text-success mr-2" />
                    Estimated Revenue
                </div>
                <div class="metric-value text-xl sm:text-2xl md:text-[25px] font-bold text-success mb-2 z-10">
                    ${{ formatCurrency(adsInsightsData.estimateRevenue) }}
                </div>
                <div class="flex justify-between items-center w-full z-10">
                    <div class="text-xs sm:text-sm text-textPrimary opacity-80">
                        <template v-if="hasOriginalRevenue">
                            Original Revenue: ${{ formatCurrency(adsInsightsData.originalRevenue) }}
                        </template>
                        <template v-else>
                            ECPM: ${{ adsInsightsData.ecpm }}
                        </template>
                    </div>
                    <div class="trend-indicator px-2 py-1 rounded-xl text-xs font-semibold"
                        v-if="getTrend('estimateRevenue')">
                        <div :class="['trend-' + getTrend('estimateRevenue').direction, 'flex items-center']">
                            <VaIcon v-if="getTrend('estimateRevenue').direction === 'up'" name="mso-trending_up"
                                size="small" />
                            <VaIcon v-else-if="getTrend('estimateRevenue').direction === 'down'"
                                name="mso-trending_down" size="small" />
                            {{ getTrend('estimateRevenue').percentage }}%
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="metric-item warning-item flex flex-col justify-between p-4 sm:p-5 rounded-xl bg-backgroundPrimary border border-backgroundBorder transition-all duration-300 relative overflow-hidden min-h-[120px] sm:min-h-[140px] shadow-sm">
                <div class="metric-label flex items-center text-sm sm:text-sm font-medium text-textPrimary mb-2 z-10">
                    <VaIcon :name="getIcon('activeViewRate')" size="medium" class="icon text-warning mr-2" />
                    Active View Rate
                </div>
                <div class="metric-value text-xl sm:text-2xl md:text-[25px] font-bold text-warning mb-2 z-10">
                    {{ formatPercent(adsInsightsData.activeViewRate) }}
                </div>
                <div class="flex justify-between items-center w-full z-10">
                    <div class="text-xs sm:text-sm text-textPrimary opacity-80">Active View Rate</div>
                    <div class="trend-indicator px-2 py-1 rounded-xl text-xs font-semibold"
                        v-if="getTrend('activeViewRate')">
                        <div :class="['trend-' + getTrend('activeViewRate').direction, 'flex items-center']">
                            <VaIcon v-if="getTrend('activeViewRate').direction === 'up'" name="mso-trending_up"
                                size="small" />
                            <VaIcon v-else-if="getTrend('activeViewRate').direction === 'down'" name="mso-trending_down"
                                size="small" />
                            {{ getTrend('activeViewRate').percentage }}%
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="metric-item danger-item flex flex-col justify-between p-4 sm:p-5 rounded-xl bg-backgroundPrimary border border-backgroundBorder transition-all duration-300 relative overflow-hidden min-h-[120px] sm:min-h-[140px] shadow-sm">
                <div class="metric-label flex items-center text-sm   font-medium text-textPrimary mb-2 z-10">
                    <VaIcon :name="getIcon('ecpc')" size="medium" class="icon text-danger mr-2" />
                    ECPC
                </div>
                <div class="metric-value text-xl sm:text-2xl md:text-[25px] font-bold text-danger mb-2 z-10">
                    ${{ adsInsightsData.ecpc }}
                </div>
                <div class="flex justify-between items-center w-full z-10">
                    <div class="text-xs sm:text-sm text-textPrimary opacity-80">Enhanced Cost Per Click</div>
                    <div class="trend-indicator px-2 py-1 rounded-xl text-xs font-semibold" v-if="getTrend('ecpc')">
                        <div :class="['trend-' + getTrend('ecpc').direction, 'flex items-center']">
                            <VaIcon v-if="getTrend('ecpc').direction === 'up'" name="mso-trending_up" size="small" />
                            <VaIcon v-else-if="getTrend('ecpc').direction === 'down'" name="mso-trending_down"
                                size="small" />
                            {{ getTrend('ecpc').percentage }}%
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* 保留必要的伪元素和特殊样式 */
.metric-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 100%;

    opacity: 0.7;
}



.success-item::before {
    background: var(--va-success);
}

.secondary-item::before {
    background: var(--va-secondary);
}

.info-item::before {
    background: var(--va-info);
}

.warning-item::before {
    background: var(--va-warning);
}

.primary-item::before {
    background: var(--va-primary);
}

.danger-item::before {
    background: var(--va-danger);
}

.success-item:hover {
    background: linear-gradient(145deg, rgba(var(--va-success-rgb), 0.05), rgba(var(--va-success-rgb), 0.15));
}

.secondary-item:hover {
    background: linear-gradient(145deg, rgba(var(--va-secondary-rgb), 0.05), rgba(var(--va-secondary-rgb), 0.15));
}

.info-item:hover {
    background: linear-gradient(145deg, rgba(var(--va-info-rgb), 0.05), rgba(var(--va-info-rgb), 0.15));
}

.warning-item:hover {
    background: linear-gradient(145deg, rgba(var(--va-warning-rgb), 0.05), rgba(var(--va-warning-rgb), 0.15));
}

.primary-item:hover {
    background: linear-gradient(145deg, rgba(var(--va-primary-rgb), 0.05), rgba(var(--va-primary-rgb), 0.15));
}

.danger-item:hover {
    background: linear-gradient(145deg, rgba(var(--va-danger-rgb), 0.05), rgba(var(--va-danger-rgb), 0.15));
}

/* 趋势指示器样式 */
.trend-up {
    color: var(--va-success);
}

.trend-down {
    color: var(--va-danger);
}

.trend-neutral {
    color: var(--va-gray);
}
</style>
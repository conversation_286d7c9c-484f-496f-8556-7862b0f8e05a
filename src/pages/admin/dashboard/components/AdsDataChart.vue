<script setup>
import { defineProps, onMounted, ref, watch } from 'vue'
import Chart from 'chart.js/auto'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
    adsInsightsDomainList: {
        type: Array,
        required: true,
        default: () => []
    }
})
const chartContainer = ref(null)
let chartInstance = null

// 初始化图表
const initChart = () => {
    // 确保容器和数据都存在
    if (!chartContainer.value || !props.adsInsightsDomainList || props.adsInsightsDomainList.length === 0) {
        console.warn('图表容器或数据不存在，无法初始化图表');
        return;
    }

    // 获取canvas元素
    const canvas = document.getElementById('ads-bar-chart-canvas');
    if (!canvas) {
        console.error(t('dashboard.errors.canvasNotFound', { id: 'ads-bar-chart-canvas' }));
        return;
    }

    // 清理之前的图表实例
    if (chartInstance) {
        chartInstance.destroy();
        chartInstance = null;
    }

    // 创建新的图表
    const ctx = canvas.getContext('2d');
    if (!ctx) {
        console.error('无法获取canvas上下文');
        return;
    }

    chartInstance = new Chart(ctx, {
        type: 'bar', // 默认类型为柱状图
        data: {
            labels: props.adsInsightsDomainList.map(item => item.appName),
            datasets: [
                {
                    type: 'line', // 使用折线图展示点击率
                    label: t('dashboard.charts.ads.clickRate'),
                    data: props.adsInsightsDomainList.map(item => {
                        return item.clickRate || 0;
                    }),
                    borderColor: 'rgba(59, 130, 246, 1)', // 蓝色
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 2,
                    tension: 0.3, // 添加曲线平滑效果
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    fill: false,
                    yAxisID: 'y' // 使用左侧Y轴
                },
                {
                    type: 'line', // 使用折线图展示eCPM
                    label: t('dashboard.charts.ads.ecpm'),
                    data: props.adsInsightsDomainList.map(item => {
                        return item.ecpm || 0;
                    }),
                    borderColor: 'rgba(16, 185, 129, 1)', // 绿色
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    borderWidth: 2,
                    tension: 0.3,
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    fill: false,
                    yAxisID: 'y' // 使用左侧Y轴
                },
                {
                    type: 'line', // 使用折线图展示展示率
                    label: t('dashboard.charts.ads.impressionRate'),
                    data: props.adsInsightsDomainList.map(item => {
                        return item.impressionRate || 0;
                    }),
                    borderColor: 'rgba(249, 115, 22, 1)', // 橙色
                    backgroundColor: 'rgba(249, 115, 22, 0.1)',
                    borderWidth: 2,
                    tension: 0.3,
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    fill: false,
                    yAxisID: 'y' // 使用左侧Y轴
                },
                {
                    type: 'bar', // 明确指定使用柱状图展示收益
                    label: t('dashboard.charts.ads.estimateRevenue'),
                    data: props.adsInsightsDomainList.map(item => {
                        return item.estimateRevenue || 0;
                    }),
                    backgroundColor: 'rgba(139, 92, 246, 0.7)', // 紫色
                    borderColor: 'rgba(139, 92, 246, 1)',
                    borderWidth: 1,
                    // 使用独立Y轴以展示收益数据，因为可能数值范围差异较大
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    title: {
                        display: true,
                        text: t('dashboard.charts.ads.percentageAndEcpm')
                    }
                },
                y1: {
                    beginAtZero: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false
                    },
                    title: {
                        display: true,
                        text: t('dashboard.charts.ads.revenue')
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        pointStyle: function(context) {
                            // 根据图表类型显示不同的图例标记样式
                            if (!context || !context.chart || !context.chart.data || 
                                !context.chart.data.datasets || 
                                context.datasetIndex === undefined ||
                                !context.chart.data.datasets[context.datasetIndex]) {
                                return 'circle'; // 默认使用圆形
                            }
                            
                            const dataset = context.chart.data.datasets[context.datasetIndex];
                            // 检查dataset.type是否存在
                            return (dataset.type && dataset.type === 'line') ? 'circle' : 'rect';
                        },
                        padding: 15,
                        font: {
                            size: 12
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    titleColor: '#4A4A4A',
                    bodyColor: '#4A4A4A',
                    borderColor: 'rgba(0, 0, 0, 0.05)',
                    borderWidth: 1,
                    padding: 12,
                    boxPadding: 6,
                    callbacks: {
                        title: function(tooltipItems) {
                            return tooltipItems && tooltipItems.length > 0 ? tooltipItems[0].label : '';
                        },
                        label: function (context) {
                            if (!context || !context.dataset) return '';
                            
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed && context.parsed.y !== null) {
                                if (label.includes(t('dashboard.charts.ads.estimateRevenue'))) {
                                    label += new Intl.NumberFormat('zh-CN', {
                                        style: 'currency',
                                        currency: 'USD'
                                    }).format(context.parsed.y);
                                } else if (label.includes(t('dashboard.charts.ads.clickRate')) ||
                                    label.includes(t('dashboard.charts.ads.impressionRate'))) {
                                    label += context.parsed.y.toFixed(2) + '%';
                                } else {
                                    label += context.parsed.y.toFixed(2);
                                }
                            }
                            return label;
                        }
                    }
                }
            }
        }
    })
}

// 监听数据变化
watch(() => props.adsInsightsDomainList, () => {
    // 使用setTimeout确保DOM已更新
    setTimeout(() => {
        initChart()
    }, 100)
}, { deep: true })

// 组件挂载时初始化图表
onMounted(() => {
    // 使用setTimeout确保DOM已完全渲染
    setTimeout(() => {
        initChart()
    }, 300)
})
</script>

<template>
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">{{ t('dashboard.charts.ads.title') }}</div>
        </div>
        <div class="chart-container" ref="chartContainer">
            <canvas id="ads-bar-chart-canvas"></canvas>
        </div>
    </div>
</template>

<style scoped>
.chart-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 18px;
    padding: 25px;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    margin-bottom: 25px;
}

.chart-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-title {
    display: flex;
    justify-content: center;
    gap: 10px;
    width: 100%;
    font-size: 20px;
    font-weight: 600;
    color: var(--primary-text);
}

.chart-actions {
    display: flex;
    gap: 10px;
}

.chart-btn {
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 6px 12px;
    font-size: 14px;
    color: var(--secondary-text);
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-btn:hover {
    background: white;
    color: var(--accent-color);
    border-color: rgba(255, 127, 80, 0.3);
}

.chart-container {
    height: 300px;
    position: relative;
    width: 100%;
}

canvas {
    display: block;
    width: 100% !important;
    height: 100% !important;
}
</style>
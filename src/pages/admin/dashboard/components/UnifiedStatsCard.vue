<script setup>
import { defineProps, ref, computed, onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/stores/user-store'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
const router = useRouter()
const { t } = useI18n()
const props = defineProps({
    revenueData: {
        type: Object,
        default: () => ({ todayRev: 0, yesterdayRev: 0, thisMonthRev: 0, lastMonthRev: 0, sumRev: 0 })
    },
    adsInsightsData: {
        type: Object,
        default: () => ({ adsRequest: 0, adsMatch: 0, matchRate: 0, adsImpression: 0, adsDisplay: 0, adsClick: 0, clickRate: 0, estimateRevenue: 0, ecpm: 0, activeViewRate: 0, impressionRate: 0, ecpc: 0, originalRevenue: 0 })
    },
    domainData: {
        type: Object,
        default: () => ({ sumCount: 0, privateCount: 0, applyInCount: 0, passCount: 0, useCount: 0, recycleCount: 0, secondDomainCount: 0 })
    },
    trendData: {
        type: Object,
        default: () => ({})
    }
})

const userStore = useUserStore()
const isPermission = ref(false)

const layoutMode = ref('row')
const toggleLayoutMode = () => {
    layoutMode.value = layoutMode.value === 'row' ? 'list' : 'row'
    localStorage.setItem('statsLayoutMode', layoutMode.value)
}

const activeTab = ref('keyMetrics')

const tabs = computed(() => {
    const availableTabs = [
        { key: 'keyMetrics', name: t('dashboard.stats.tabs.keyMetrics') },
        { key: 'revenue', name: t('dashboard.stats.tabs.revenue') },
        { key: 'ads', name: t('dashboard.stats.tabs.ads') },
    ];

    if (props.domainData && props.domainData.sumCount > 0) {
        availableTabs.push({ key: 'domains', name: t('dashboard.stats.tabs.domains') });
    }
    return availableTabs;
});

onMounted(() => {
    isPermission.value = userStore.roleInfo?.name !== '广告渠道'
    const savedMode = localStorage.getItem('statsLayoutMode')
    if (savedMode && ['row', 'list'].includes(savedMode)) {
        layoutMode.value = savedMode
    }
    window.addEventListener('resize', updateGridColumns)
    window.addEventListener('mouseup', stopDragging)
    window.addEventListener('mousemove', handleDragging)
})

onUnmounted(() => {
    window.removeEventListener('resize', updateGridColumns)
    window.removeEventListener('mouseup', stopDragging)
    window.removeEventListener('mousemove', handleDragging)
})

// Computed property for responsive grid columns in 'list' mode
const gridColumns = computed(() => {
    if (typeof window === 'undefined') return 3
    const width = window.innerWidth
    if (width < 640) return 1
    if (width < 1024) return 2
    if (width < 1280) return 3
    return 4
})


const updateGridColumns = () => { }
const formatNumber = (num) => new Intl.NumberFormat('en-US').format(num)
const formatCurrency = (amount) => new Intl.NumberFormat('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(amount)
const formatPercent = (value) => !value ? '0.00%' : Number(value).toFixed(2) + '%'
const getTrend = (key) => {
    if (!props.trendData || props.trendData[key] === undefined) return null
    const trend = props.trendData[key]
    return {
        direction: trend > 0 ? 'up' : trend < 0 ? 'down' : 'neutral',
        percentage: Math.abs(trend)
    }
}
const getIcon = (key) => {
    const icons = { todayRev: 'mso-today', yesterdayRev: 'mso-event', thisMonthRev: 'mso-calendar_month', lastMonthRev: 'mso-calendar_today', sumRev: 'mso-paid', adsRequest: 'mso-ads_click', adsImpression: 'mso-visibility', adsClick: 'mso-touch_app', estimateRevenue: 'mso-attach_money', originalRevenue: 'mso-monetization_on', activeViewRate: 'mso-monitoring', ecpc: 'mso-price_check', sumCount: 'mso-analytics', useCount: 'mso-rocket_launch', privateCount: 'mso-add_circle', applyInCount: 'mso-pending', passCount: 'mso-verified', recycleCount: 'mso-recycling' }
    return icons[key] || 'mso-insert_chart'
}
const isClickFn = ()=>{
    if (isPermission.value) {
        router.push('/adv/aff-insights?mode=today')
    }
}

const scrollContainer = ref(null)
const isDragging = ref(false)
const startX = ref(0)
const scrollLeft = ref(0)
const startDragging = (e) => { if (!scrollContainer.value) return; isDragging.value = true; startX.value = e.pageX - scrollContainer.value.offsetLeft; scrollLeft.value = scrollContainer.value.scrollLeft; document.body.style.cursor = 'grabbing'; document.body.style.userSelect = 'none'; }
const handleDragging = (e) => { if (!isDragging.value || !scrollContainer.value) return; const x = e.pageX - scrollContainer.value.offsetLeft; const walk = (x - startX.value) * 1.5; scrollContainer.value.scrollLeft = scrollLeft.value - walk; }
const stopDragging = () => { isDragging.value = false; document.body.style.cursor = ''; document.body.style.userSelect = ''; }
const scrollLeft1 = () => { if (scrollContainer.value) { scrollContainer.value.scrollBy({ left: -300, behavior: 'smooth' }) } }
const scrollRight = () => { if (scrollContainer.value) { scrollContainer.value.scrollBy({ left: 300, behavior: 'smooth' }) } }

const groupedStatItems = computed(() => {
    const tooltips = {
        matchRate: t('dashboard.stats.tooltips.matchRate'),
        impressionRate: t('dashboard.stats.tooltips.impressionRate'),
        activeViewRate: t('dashboard.stats.tooltips.activeViewRate'),
        ecpm: t('dashboard.stats.tooltips.ecpm'),
        ecpc: t('dashboard.stats.tooltips.ecpc')
    };

    const revenue = [];
    if (props.revenueData) {
        revenue.push(
            { key: 'todayRev', title: t('dashboard.stats.revenue.todayRev'), value: `$${formatCurrency(props.revenueData.todayRev)}`, subtitle: t('dashboard.stats.revenue.todayRevSubtitle'), colorClass: 'success-item', trend: getTrend('todayRev'),isClick:true,isClickFn,},
            { key: 'yesterdayRev', title: t('dashboard.stats.revenue.yesterdayRev'), value: `$${formatCurrency(props.revenueData.yesterdayRev)}`, subtitle: t('dashboard.stats.revenue.yesterdayRevSubtitle'), colorClass: 'primary-item', trend: getTrend('yesterdayRev') },
            { key: 'thisMonthRev', title: t('dashboard.stats.revenue.thisMonthRev'), value: `$${formatCurrency(props.revenueData.thisMonthRev)}`, subtitle: t('dashboard.stats.revenue.thisMonthRevSubtitle'), colorClass: 'info-item', trend: getTrend('thisMonthRev') },
            { key: 'lastMonthRev', title: t('dashboard.stats.revenue.lastMonthRev'), value: `$${formatCurrency(props.revenueData.lastMonthRev)}`, subtitle: t('dashboard.stats.revenue.lastMonthRevSubtitle'), colorClass: 'secondary-item', trend: getTrend('lastMonthRev') },
            { key: 'sumRev', title: t('dashboard.stats.revenue.sumRev'), value: `$${formatCurrency(props.revenueData.sumRev)}`, subtitle: t('dashboard.stats.revenue.sumRevSubtitle'), colorClass: 'warning-item', trend: getTrend('sumRev') }
        )
    }

    const ads = [];
    if (props.adsInsightsData) {
        ads.push(
            { key: 'adsRequest', title: t('dashboard.stats.ads.adsRequest'), value: formatNumber(props.adsInsightsData.adsRequest), subtitle: `${t('dashboard.stats.ads.matchRate')}: ${formatPercent(props.adsInsightsData.matchRate)}`, colorClass: 'info-item', trend: getTrend('adsRequest'), tooltip: tooltips.matchRate },
            { key: 'adsImpression', title: t('dashboard.stats.ads.adsImpression'), value: formatNumber(props.adsInsightsData.adsImpression), subtitle: `${t('dashboard.stats.ads.impressionRate')}: ${formatPercent(props.adsInsightsData.impressionRate)}`, colorClass: 'primary-item', trend: getTrend('adsImpression'), tooltip: tooltips.impressionRate },
            { key: 'adsClick', title: t('dashboard.stats.ads.adsClick'), value: formatNumber(props.adsInsightsData.adsClick), subtitle: `${t('dashboard.stats.ads.clickRate')}: ${formatPercent(props.adsInsightsData.clickRate)}`, colorClass: 'secondary-item', trend: getTrend('adsClick') },
            { key: 'activeViewRate', title: t('dashboard.stats.ads.activeViewRate'), value: formatPercent(props.adsInsightsData.activeViewRate), subtitle: t('dashboard.stats.ads.activeViewRate'), colorClass: 'warning-item', trend: getTrend('activeViewRate'), tooltip: tooltips.activeViewRate },
            { key: 'ecpc', title: t('dashboard.stats.ads.ecpc'), value: `$${props.adsInsightsData.ecpc}`, subtitle: t('dashboard.stats.ads.ecpcSubtitle'), colorClass: 'danger-item', trend: getTrend('ecpc'), tooltip: tooltips.ecpc },
            { key: 'ecpm', title: t('dashboard.stats.ads.ecpm'), value: `$${props.adsInsightsData.ecpm}`, subtitle: t('dashboard.stats.ads.ecpmSubtitle'), colorClass: 'info-item', trend: getTrend('ecpm'), tooltip: tooltips.ecpm }
        )
    }

    const domains = [];
    if (props.domainData) {
        domains.push({ key: 'sumCount', title: t('dashboard.stats.domains.sumCount'), value: formatNumber(props.domainData.sumCount), subtitle: isPermission.value ? t('dashboard.stats.domains.sumCountSubtitle') : t('dashboard.stats.domains.channelDomainCount'), colorClass: 'success-item', trend: getTrend('sumCount') });
        if (isPermission.value) {
            domains.push(
                { key: 'useCount', title: t('dashboard.stats.domains.useCount'), value: formatNumber(props.domainData.useCount), subtitle: t('dashboard.stats.domains.useCountSubtitle'), colorClass: 'secondary-item', trend: getTrend('useCount') },
                { key: 'privateCount', title: t('dashboard.stats.domains.privateCount'), value: formatNumber(props.domainData.privateCount), subtitle: t('dashboard.stats.domains.privateCountSubtitle'), colorClass: 'info-item', trend: getTrend('privateCount') },
                { key: 'applyInCount', title: t('dashboard.stats.domains.applyInCount'), value: formatNumber(props.domainData.applyInCount), subtitle: t('dashboard.stats.domains.applyInCountSubtitle'), colorClass: 'warning-item', trend: getTrend('applyInCount') },
                { key: 'passCount', title: t('dashboard.stats.domains.passCount'), value: formatNumber(props.domainData.passCount), subtitle: t('dashboard.stats.domains.passCountSubtitle'), colorClass: 'primary-item', trend: getTrend('passCount') },
                { key: 'recycleCount', title: t('dashboard.stats.domains.recycleCount'), value: formatNumber(props.domainData.recycleCount), subtitle: t('dashboard.stats.domains.recycleCountSubtitle'), colorClass: 'danger-item', trend: getTrend('recycleCount') }
            );
        }
    }

    // 关键指标
    const keyMetrics = [
        revenue.length > 0 ? revenue[0] : null,     // Today's Revenue
        ads.length > 0 ? ads[5] : null,     // ecpm
        ads.length > 0 ? ads[0] : null,             // Estimated Revenue
        ads.length > 2 ? ads[2] : null,             // Ad Impression
        domains.length > 0 ? domains[0] : null      // Total Domains
    ].filter(Boolean); // Filter out nulls if a category is empty

    return { keyMetrics, revenue, ads, domains };
});

// MODIFIED: The list of items to display now depends on the active tab
const allStatItems = computed(() => {
    return groupedStatItems.value[activeTab.value] || [];
});

// --- Scroll Button Visibility Logic ---
const showLeftScroll = ref(false)
const showRightScroll = ref(true)
const handleScroll = () => {
    if (!scrollContainer.value) return;
    showLeftScroll.value = scrollContainer.value.scrollLeft > 5;
    const maxScrollLeft = scrollContainer.value.scrollWidth - scrollContainer.value.clientWidth;
    showRightScroll.value = scrollContainer.value.scrollLeft < maxScrollLeft - 5;
}
</script>

<template>
    <div class="stats-card border border-backgroundBorder rounded-2xl p-4 sm:p-5 shadow-sm">
        <!-- Card Header -->
        <div class="flex justify-between items-center mb-4">
            <div class="flex items-center gap-3 text-lg sm:text-xl font-semibold text-textPrimary">
                <div class="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 rounded-lg bg-primary shadow-sm">
                    <VaIcon name="mso-dashboard" size="medium" color="#fff" />
                </div>
                {{ t('dashboard.stats.title') }}
            </div>

            <!-- Control Buttons -->
            <div class="flex items-center gap-2">
                <button class="control-button" @click="toggleLayoutMode"
                    :aria-label="layoutMode === 'row' ? t('dashboard.stats.controls.switchToList') : t('dashboard.stats.controls.switchToRow')"
                    :title="layoutMode === 'row' ? t('dashboard.stats.controls.switchToList') : t('dashboard.stats.controls.switchToRow')">
                    <VaIcon :name="layoutMode === 'row' ? 'mso-view_module' : 'mso-view_carousel'" />
                </button>
                <transition-group name="fade">
                    <template v-if="layoutMode === 'row'">
                        <button v-show="showLeftScroll" @click="scrollLeft1" key="left-scroll" class="control-button"
                            :aria-label="t('dashboard.stats.controls.scrollLeft')">
                            <VaIcon name="mso-chevron_left" />
                        </button>
                        <button v-show="showRightScroll" @click="scrollRight" key="right-scroll" class="control-button"
                            :aria-label="t('dashboard.stats.controls.scrollRight')">
                            <VaIcon name="mso-chevron_right" />
                        </button>
                    </template>
                </transition-group>
            </div>
        </div>

        <!-- NEW: Tabs Navigation -->
        <div class="border-b border-backgroundBorder mb-4">
            <div class="flex gap-1 sm:gap-2 -mb-px">
                <button v-for="tab in tabs" :key="tab.key" @click="activeTab = tab.key"
                    :class="['tab-button', { 'active-tab': activeTab === tab.key }]">
                    {{ tab.name }}
                </button>
            </div>
        </div>

        <!-- Content Area with transition -->
        <transition name="layout-switch" mode="out-in">
            <!-- Row Mode -->
            <div v-if="layoutMode === 'row'" ref="scrollContainer" class="scroll-container" @mousedown="startDragging"
                @scroll="handleScroll" :key="`${activeTab}-row`">
                <transition-group name="stat-item-anim" tag="div" class="flex gap-3 pb-2">
                    <div v-for="(item, index) in allStatItems" :key="item.key" :class="['stat-item', item.colorClass]"
                        :style="{ '--i': index }" @click="item.isClick ? item.isClickFn() : null">
                        <div class="stat-header">
                            <VaIcon :name="getIcon(item.key)" size="small"
                                :class="'icon mr-1.5 text-' + item.colorClass.split('-')[0]" />
                            <span>{{ item.title }}</span>
                            <!-- NEW: Tooltip Icon -->
                            <VaPopover v-if="item.tooltip" :message="item.tooltip" placement="top" class="ml-auto">
                                <VaIcon name="mso-help_outline" size="16px" class="text-gray-400 cursor-help" />
                            </VaPopover>
                        </div>
                        <div class="stat-value">{{ item.value }}</div>
                        <div class="stat-footer">
                            <span class="stat-subtitle">{{ item.subtitle }}</span>
                            <div class="trend-indicator" v-if="item.trend">
                                <div :class="['trend-' + item.trend.direction, 'flex items-center']">
                                    <VaIcon v-if="item.trend.direction === 'up'" name="mso-trending_up"
                                        size="x-small" />
                                    <VaIcon v-else-if="item.trend.direction === 'down'" name="mso-trending_down"
                                        size="x-small" />
                                    {{ item.trend.percentage }}%
                                </div>
                            </div>
                        </div>
                    </div>
                </transition-group>
            </div>

            <!-- List Mode -->
            <div v-else class="list-container" :key="`${activeTab}-list`">
                <div class="stats-grid" :style="{ gridTemplateColumns: `repeat(${gridColumns}, 1fr)` }">
                    <transition-group name="stat-item-anim">
                        <div v-for="(item, index) in allStatItems" :key="item.key"
                            :class="['stat-item-list', item.colorClass]" :style="{ '--i': index }">
                            <div class="stat-header">
                                <VaIcon :name="getIcon(item.key)" size="small"
                                    :class="'icon mr-1.5 text-' + item.colorClass.split('-')[0]" />
                                <span>{{ item.title }}</span>
                                <!-- NEW: Tooltip Icon -->
                                <VaPopover v-if="item.tooltip" :message="item.tooltip" placement="top" class="ml-auto">
                                    <VaIcon name="mso-help_outline" size="16px" class="text-gray-400 cursor-help" />
                                </VaPopover>
                            </div>
                            <div class="stat-value">{{ item.value }}</div>
                            <div class="stat-footer">
                                <span class="stat-subtitle">{{ item.subtitle }}</span>
                                <div class="trend-indicator" v-if="item.trend">
                                    <div :class="['trend-' + item.trend.direction, 'flex items-center']">
                                        <VaIcon v-if="item.trend.direction === 'up'" name="mso-trending_up"
                                            size="x-small" />
                                        <VaIcon v-else-if="item.trend.direction === 'down'" name="mso-trending_down"
                                            size="x-small" />
                                        {{ item.trend.percentage }}%
                                    </div>
                                </div>
                            </div>
                        </div>
                    </transition-group>
                </div>
            </div>
        </transition>
    </div>
</template>

<style scoped>
/* Base Styles */
.stats-card {
    display: flex;
    flex-direction: column;
    background-color: var(--va-background-secondary);
    transition: all 0.3s ease;
}

/* NEW: Control Button Style */
.control-button {
    padding: 8px;
    border-radius: 50%;
    background-color: var(--va-background-element);
    color: var(--va-text-primary);
    transition: all 0.2s ease;
}

.control-button:hover {
    background-color: var(--va-background-border);
    transform: scale(1.1);
}

.control-button:active {
    transform: scale(0.95);
}


/* NEW: Tab Styles */
.tab-button {
    padding: 4px 8px;
    font-size: 0.875rem;
    /* 14px */
    font-weight: 600;
    border-radius: 6px 6px 0 0;
    border-bottom: 2px solid transparent;
    color: var(--va-text-secondary);
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

.tab-button:hover {
    color: var(--va-primary);
    background-color: var(--va-background-element);
}

.tab-button.active-tab {
    color: var(--va-primary);
    background-color: var(--va-background-primary);
    border-color: var(--va-primary);
}

@media (min-width: 640px) {
    .tab-button {
        padding: 8px 16px;
        font-size: 1rem;
        /* 16px */
    }
}


/* Row Mode Styles */
.scroll-container {
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none;
    -ms-overflow-style: none;
    cursor: grab;
    padding: 4px;
    scroll-snap-type: x mandatory;
}

.scroll-container::-webkit-scrollbar {
    display: none;
}

.scroll-container:active {
    cursor: grabbing;
}

/* List Mode Styles */
.list-container {
    width: 100%;
    transition: all 0.3s ease;
    padding: 4px;
}

.stats-grid {
    display: grid;
    grid-gap: 12px;
    width: 100%;
}

/* Common Stat Item Styles */
.stat-item,
.stat-item-list {
    min-height: 120px;
    border-radius: 12px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
    background-color: var(--va-background-primary);
    border: 1px solid var(--va-background-border);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1), box-shadow 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    cursor: pointer;
}

.stat-item {
    flex: 0 0 auto;
    width: 220px;
    scroll-snap-align: start;
}

/* Enhanced hover effect */
.stat-item:hover,
.stat-item-list:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.stat-item::before,
.stat-item-list::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    border-radius: 0 4px 4px 0;
    opacity: 0.9;
}

/* Stat Item Content Styles */
.stat-header {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--va-text-primary);
    margin-bottom: 8px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 4px 0;
    color: var(--va-text-primary);
}

.stat-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
}

.stat-subtitle {
    font-size: 0.75rem;
    color: var(--va-text-secondary);
}

.trend-indicator {
    padding: 2px 6px;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* Color Styles */
.success-item::before {
    background: var(--va-success);
}

.secondary-item::before {
    background: var(--va-secondary);
}

.info-item::before {
    background: var(--va-info);
}

.warning-item::before {
    background: var(--va-warning);
}

.primary-item::before {
    background: var(--va-primary);
}

.danger-item::before {
    background: var(--va-danger);
}

.success-item .stat-value {
    color: var(--va-success);
}

.danger-item .stat-value {
    color: var(--va-danger);
}

/* Trend Indicator Styles */
.trend-up {
    color: var(--va-success);
    background-color: rgba(var(--va-success-rgb), 0.1);
}

.trend-down {
    color: var(--va-danger);
    background-color: rgba(var(--va-danger-rgb), 0.1);
}

.trend-neutral {
    color: var(--va-gray);
    background-color: rgba(var(--va-gray-rgb), 0.1);
}

/* Animations */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

.layout-switch-enter-active,
.layout-switch-leave-active {
    transition: all 0.4s ease;
}

.layout-switch-enter-from,
.layout-switch-leave-to {
    opacity: 0;
    transform: translateY(20px);
}

.stat-item-anim-enter-active,
.stat-item-anim-leave-active {
    transition: all 0.4s ease;
    transition-delay: calc(0.05s * var(--i, 0));
}

.stat-item-anim-leave-active {
    position: absolute;
}

.stat-item-anim-enter-from {
    opacity: 0;
    transform: translateY(20px);
}

.stat-item-anim-leave-to {
    opacity: 0;
    transform: scale(0.9);
}
</style>

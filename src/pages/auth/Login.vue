<template>
  <VaForm ref="form" @submit.prevent="submit" tag="form" class="login-form">
    <!-- <p class="text-base mb-4 leading-5">
      {{ $t('user.login.noAccount') }}
      <RouterLink :to="{ name: 'signup' }" class="font-semibold text-primary">{{ $t('user.login.register') }}</RouterLink>
    </p> -->

    <div v-if="!isTwoFa">
      <VaInput v-model="formData.username" :rules="[validators.required]" class="mb-4" type="text"
        :placeholder="$t('user.login.username')" />
      <VaValue v-slot="isPasswordVisible" :default-value="false">
        <VaInput v-model="formData.password" :rules="[validators.required]"
          :type="isPasswordVisible.value ? 'text' : 'password'" class="mb-4" :placeholder="$t('user.login.password')"
          @clickAppendInner.stop="isPasswordVisible.value = !isPasswordVisible.value">
          <template #appendInner>
            <VaIcon :name="isPasswordVisible.value ? 'mso-visibility_off' : 'mso-visibility'" class="cursor-pointer"
              color="secondary" />
          </template>
        </VaInput>
      </VaValue>

      <!-- 验证码 -->
      <div v-if="captchaEnabled" class="mb-4 flex w-full">
        <div class="flex gap-2 items-center w-full">
          <VaInput v-model="formData.code" class="flex-1" :placeholder="$t('user.login.captcha')" />
          <img :src="codeUrl" class="h-10 cursor-pointer rounded" alt="验证码" @click="getAuthImg" />
        </div>
      </div>

      <div class="auth-layout__options flex flex-col sm:flex-row items-start sm:items-center justify-between">
        <VaCheckbox v-model="formData.keepLoggedIn" class="mb-2 sm:mb-0" :label="$t('user.login.rememberMe')" />
        <!-- <RouterLink :to="{ name: 'recover-password' }" class="mt-2 sm:mt-0 sm:ml-1 font-semibold text-primary">
          {{ $t('user.login.forgotPassword') }}
        </RouterLink> -->
      </div>
    </div>
    <!-- 双因素认证 -->
    <div v-if="isTwoFa" class="mb-4">
      <VaInput v-model="twofaVerify" :rules="[validators.required]" :label="$t('user.login.twoFa.code')" :placeholder="$t('user.login.twoFa.placeholder')" />
    </div>

    <div class="flex justify-center mt-4">
      <VaButton class="w-full" :loading="loading" @click.prevent="isTwoFa ? handleTwoFa() : submit()" type="submit">
        {{ isTwoFa ? $t('user.login.verifyButton') : $t('user.login.loginButton') }}
      </VaButton>
    </div>
  </VaForm>
</template>
<script setup>
import { reactive, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useForm, useToast } from 'vuestic-ui'
import { login, getCodeImg, isTwofaVerify, getUserInfo } from '../../api/auth/index.js'
import { encrypt, decrypt, setToken, validators } from '../../utils/index.js'
import { useUserStore } from '../../stores/user-store'
import { usePermissionStore } from '../../stores/permission'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const { validate } = useForm('form')
const { push } = useRouter()
const { init } = useToast()
const userStore = useUserStore()
const permissionStore = usePermissionStore()
// 添加请求状态标志
const isAuthImgRequested = ref(false)
const loading = ref(false)
const captchaEnabled = ref(true)
const isTwoFa = ref(false)
const codeUrl = ref('')
const twofaVerify = ref('')

const formData = reactive({
  formData: '',
  password: '',
  keepLoggedIn: false,
  username: '',
  code: '',
  uuid: '',
})

const getAuthImg = async () => {
  loading.value = true
  try {
    const res = (await getCodeImg())
    captchaEnabled.value = res.data?.captchaEnabled === undefined ? true : res.data.captchaEnabled
    if (captchaEnabled.value) {
      codeUrl.value = 'data:image/gif;base64,' + res.data.img
      formData.uuid = res.data.uuid
    }
  } catch (error) {
    // 如果请求失败，重置标志以允许重试
    isAuthImgRequested.value = false
  } finally {
    loading.value = false
  }
}

const setCachedCredentials = (data) => {
  // 加密密码后再存储
  const secureData = {
    username: data.username,
    password: encrypt(data.password), // 加密密码
    rememberMe: data.rememberMe,
  }
  localStorage.setItem('loginInfo', JSON.stringify(secureData))
}

const getCachedCredentials = () => {
  const loginInfo = localStorage.getItem('loginInfo')
  if (loginInfo) {
    const { username, password, rememberMe } = JSON.parse(loginInfo)
    formData.username = username
    // 解密密码
    formData.password = password ? decrypt(password) : ''
    formData.keepLoggedIn = rememberMe
  }
}

// 获取用户信息
const getUserInfoFn = async () => {
  try {
    const res = (await getUserInfo())
    if (res.code === 200 && res.data) {
      // 保存用户信息到store
      userStore.setUserInfoFromAPI(res.data)
      // 保存用户名到localStorage
      localStorage.setItem('userName', res.data.name || res.data.account || formData.username)
      // 检查是否启用了双因素认证
      if (res.data.verifyTwoSecret) {
        userStore.openTwoFa()
      }
      return res.data
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
  return null
}

const submit = async () => {
  if (validate()) {
    loading.value = true
    try {
      const res = (await login(formData.username, formData.password, formData.code, formData.uuid))
      if (res.code == 102) {
        // 代表需要双因素认证
        isTwoFa.value = true
        loading.value = false
        return
      }
      if (res.code == 200) {
        // 保存登录状态
        setToken(res.data)

        // 获取用户信息
        await getUserInfoFn()
        // 获取路由信息
        await getRoutersFn()
        // 保存记住密码
        if (formData.keepLoggedIn) {
          setCachedCredentials({
            username: formData.username,
            password: formData.password,
            rememberMe: formData.keepLoggedIn,
          })
        } else {
          localStorage.removeItem('loginInfo')
        }

        init({ message: t('user.login.loginSuccess'), color: 'success', duration: 5000 })
        push({ name: 'dashboard' })
      }
    } catch (error) {
      // 刷新验证码
      if (captchaEnabled.value) {
        await getAuthImg()
      }
    } finally {
      loading.value = false
    }
  }
}
const getRoutersFn = async () => {
  // 调用 permission store 的 GenerateRoutes 方法处理动态路由
  await permissionStore.GenerateRoutes()
}

const handleTwoFa = async () => {
  if (!twofaVerify.value) {
    init({ message: t('user.login.twoFa.placeholder'), color: 'warning' })
    return
  }
  loading.value = true
  try {
    // 先验证双因素认证码
    const twoFaRequest = {
      userName: formData.username.trim(),
      code: twofaVerify.value,
    }
    const res = await isTwofaVerify(twoFaRequest)
    // 保存登录状态
    setToken(res.data)
    // 获取用户信息
    await getUserInfoFn()

    // 保存记住密码
    if (formData.keepLoggedIn) {
      setCachedCredentials({
        username: formData.username,
        password: formData.password,
        rememberMe: formData.keepLoggedIn,
      })
    } else {
      localStorage.removeItem('loginInfo')
    }

    init({ message: t('user.login.loginSuccess'), color: 'success', duration: 5000 })
    push({ name: 'dashboard' })
  } catch (error) {
    console.error('双因素认证失败:', error)
    init({ message: error.message || t('user.login.twoFa.verifyFailed'), color: 'danger' })
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  getCachedCredentials()
  await getAuthImg()
})
</script>
<style scoped></style>
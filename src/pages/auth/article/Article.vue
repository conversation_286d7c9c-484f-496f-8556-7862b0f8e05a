<template>
    <div class="container">
        <!-- <VaProgressBar v-if="isLoading" indeterminate /> -->

        <!-- 文章生成AI对话界面 -->
        <div class="chat-container">


            <!-- 文章内容展示区 -->
            <div class="content-container">
                <div v-if="!content && !isGenerating" class="empty-state">
                    <VaIcon name="mso-description" size="large" />
                    <h3>暂无生成内容</h3>
                    <p>请输入主题后点击"开始生成"按钮，系统将为您生成{{ generateOutline ? '文章大纲' : '文章内容' }}</p>
                </div>

                <div v-else-if="isGenerating" class="generating-state">
                    <div class="status-bar">
                        <div class="status-indicator">
                            <div class="status-badge" :class="isPaused ? 'status-paused' : 'status-generating'">
                                {{ isPaused ? '已暂停' : '生成中' }}
                            </div>
                        </div>
                    </div>

                    <div class="article-content" ref="contentBox">
                        <div class="markdown-content" v-html="formattedContent"></div>
                        <span v-if="!isPaused" class="typing-cursor"></span>
                    </div>
                </div>

                <div v-else class="complete-state">
                    <div class="status-bar">
                        <div class="status-indicator">
                            <div class="status-badge status-completed">已完成</div>
                        </div>
                        <div class="actions">
                            <VaButton size="small" color="info" icon="mso-content_copy"
                                @click="copyToClipboard(content)">
                                复制{{ generateOutline.value ? '大纲' : '全文' }}
                            </VaButton>
                            <VaButton size="small" color="success" icon="mso-file_download" @click="downloadContent">
                                下载{{ generateOutline.value ? '大纲' : '文章' }}
                            </VaButton>
                        </div>
                    </div>

                    <div class="article-content">
                        <div class="markdown-content" v-html="formattedContent"></div>
                    </div>
                </div>
            </div>
            <!-- 配置区域 -->
            <div class="config-panel">
                <div class="action-buttons">

                    <VaButton v-if="isGenerating" @click="pauseOrResumeGeneration"
                        :color="isPaused ? 'success' : 'warning'" :icon="isPaused ? 'mso-play_arrow' : 'mso-pause'">
                        {{ isPaused ? '继续生成' : '暂停生成' }}
                    </VaButton>
                    <VaButton v-if="isGenerating" color="danger" @click="cancelGeneration" icon="mso-stop">
                        停止生成
                    </VaButton>
                    <VaButton v-if="content" @click="regenerateContent" color="info" icon="mso-refresh">
                        重新生成
                    </VaButton>
           
                </div>
                <div class="form-group flex items-center gap-1 mb-2">
                        <VaSwitch v-model="generateOutline" :disabled="isGenerating">
                            内容增强
                        </VaSwitch>
                        <div class="option-description">
                            <VaPopover message="开启后，系统将会根据主题生成大纲内容，并根据大纲内容生成文章，等待时间较长">
                                <VaIcon name="mso-help" />
                            </VaPopover>
                        </div>
                    </div>
                <div class="config-form">
                    <div class="form-row">
                        <div class="form-group">
                            <VaInput id="topic" v-model="topic" placeholder="输入文章主题或关键词" class="input-style"
                                :disabled="isGenerating">
                                <template #appendInner>
                                    <VaButton color="primary" @click="startGeneration" :loading="isStarting"
                                        :disabled="!topic || isGenerating" icon="mso-search">

                                    </VaButton>
                                </template>
                            </VaInput>
                        </div>
                    </div>


                </div>
            </div>
        </div>

    </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch, onUnmounted } from 'vue';
import { buildArticleUrl, buildOutlineUrl, buildContentUrl } from '@/api/ai/article';
import { useToast } from 'vuestic-ui';
import MarkdownIt from 'markdown-it';

// 创建markdown-it实例并配置
const md = new MarkdownIt({
    html: true,        // 允许HTML标签
    breaks: true,      // 将\n转换为<br>
    linkify: true,     // 自动转换URL为链接
    typographer: true  // 启用一些语言中立的替换和引号

});

const { init: showToast } = useToast();

// 表单数据
const topic = ref('');
const length = ref('medium');
const style = ref('informative');
const generateOutline = ref(false); // 默认生成大纲

// 状态管理
const isLoading = ref(false);
const isStarting = ref(false);
const isGenerating = ref(false);
const isPaused = ref(false);
const progress = ref(0);
const content = ref('');
const title = ref('');
const contentBox = ref(null);

// 事件源
let eventSource = null;

// 选项数据
const lengthOptions = [
    { value: 'short', text: '短篇 (300字左右)' },
    { value: 'medium', text: '中篇 (600字左右)' },
    { value: 'long', text: '长篇 (1000字以上)' }
];

const styleOptions = [
    { value: 'informative', text: '信息型' },
    { value: 'persuasive', text: '说服型' },
    { value: 'narrative', text: '叙述型' },
    { value: 'creative', text: '创意型' }
];

// 格式化内容
const formattedContent = computed(() => {
    if (!content.value) return '';
    return md.render(content.value);
});

// 监听内容变化，自动滚动到底部
watch(content, async () => {
    if (contentBox.value) {
        await nextTick();
        contentBox.value.scrollTop = contentBox.value.scrollHeight;
    }
});

// 开始生成文章
const startGeneration = async () => {
    if (!topic.value) {
        showToast({
            message: '请输入文章主题',
            color: 'warning',
            duration: 3000,
        });
        return;
    }

    isStarting.value = true;

    try {
        // 重置状态
        content.value = '';
        title.value = '';
        progress.value = 0;
        isPaused.value = false;
        isGenerating.value = true;

        // 构建API请求参数
        const params = {
            topic: topic.value,
            length: length.value,
            style: style.value
        };

        // 获取API URL
        const apiUrl = generateOutline.value ? buildOutlineUrl(params) : buildContentUrl(params);

        // 创建EventSource连接
        eventSource = new EventSource(apiUrl);

        eventSource.onmessage = (event) => {
            try {
                // 检查是否是结束标记
                if (event.data.includes('event:end')) {
                    completeGeneration();
                    return;
                }

                // 解析流式数据
                // 移除可能的 "data:" 前缀
                let jsonStr = event.data;
                if (jsonStr.startsWith('data:')) {
                    jsonStr = jsonStr.substring(5);
                }

                const data = JSON.parse(jsonStr);

                if (data.choices && data.choices.length > 0 && data.choices[0].delta) {
                    const newContent = data.choices[0].delta.content;

                    // 更新内容
                    if (newContent) {
                        content.value += newContent;

                        // 从内容中提取标题（第一行）
                        if (!title.value && content.value.includes('\n')) {
                            title.value = content.value.split('\n')[0].substring(0, 50);
                        }

                        // 更新进度（根据内容长度估算）
                        const targetLength = length.value === 'short' ? 300 :
                            length.value === 'long' ? 1000 : 600;
                        progress.value = Math.min(100, Math.floor(content.value.length / (targetLength / 100)));
                    }
                }
            } catch (e) {
                console.error('解析数据失败:', e, event.data);
            }
        };

        // 监听结束事件
        eventSource.addEventListener('end', () => {
            completeGeneration();
        });

        eventSource.onerror = (error) => {
            console.error('EventSource错误:', error);
            if (isGenerating.value && !isPaused.value) {
                isPaused.value = true;

                showToast({
                    message: '生成任务连接中断，已自动暂停',
                    color: 'warning',
                    duration: 3000,
                });
            }
            eventSource.close();
            eventSource = null;
        };

        // 如果长时间没有完成，也标记为完成（防止卡住）
        setTimeout(() => {
            if (isGenerating.value && !isPaused.value) {
                completeGeneration();
            }
        }, 60000); // 60秒超时
    } catch (error) {
        console.error('生成失败:', error);
        showToast({
            message: '生成失败，请重试',
            color: 'danger',
            duration: 3000,
        });
        isGenerating.value = false;
    } finally {
        isStarting.value = false;
    }
};

// 完成生成
const completeGeneration = () => {
    if (!isGenerating.value) return;

    isGenerating.value = false;
    isPaused.value = false;
    progress.value = 100;

    if (eventSource) {
        eventSource.close();
        eventSource = null;
    }

    // 只有当真正完成生成时才显示toast
    if (content.value) {
        showToast({
            message: `${generateOutline.value ? '大纲' : '文章'}生成完成`,
            color: 'success',
            duration: 2000,
        });
    }
};

// 暂停/继续生成
const pauseOrResumeGeneration = () => {
    if (!isGenerating.value) return;

    if (isPaused.value) {
        // 继续生成
        startGeneration(); // 重新开始生成
        showToast({
            message: '已继续生成',
            color: 'success',
            duration: 1500,
        });
    } else {
        // 暂停生成
        isPaused.value = true;

        if (eventSource) {
            eventSource.close();
            eventSource = null;
        }

        showToast({
            message: '已暂停生成',
            color: 'info',
            duration: 1500,
        });
    }
};

// 取消生成
const cancelGeneration = () => {
    if (!isGenerating.value) return;

    isGenerating.value = false;
    isPaused.value = false;

    if (eventSource) {
        eventSource.close();
        eventSource = null;
    }

    showToast({
        message: '已停止生成',
        color: 'info',
        duration: 1500,
    });
};

// 重新生成
const regenerateContent = () => {
    content.value = '';
    title.value = '';
    progress.value = 0;
    startGeneration();
};

// 复制到剪贴板
const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
        showToast({
            message: `文章内容已复制到剪贴板`,
            color: 'success',
            duration: 2000,
        });
    }).catch(err => {
        console.error('复制失败:', err);
        showToast({
            message: '复制失败，请手动选择内容复制',
            color: 'danger',
            duration: 3000,
        });
    });
};

// 下载内容
const downloadContent = () => {
    if (!content.value) {
        showToast({
            message: '没有内容可下载',
            color: 'warning',
            duration: 2000,
        });
        return;
    }

    // 创建下载
    const fileName = title.value ?
        `${title.value.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '_')}.md` :
        `文章_${new Date().toLocaleDateString()}.md`;

    const blob = new Blob([content.value], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showToast({
        message: `文章已下载`,
        color: 'success',
        duration: 2000,
    });
};

// 组件卸载时清理资源
onMounted(() => {
    // 不需要在onMounted中返回清理函数，应该在onUnmounted中进行清理
});

// 添加onUnmounted钩子进行资源清理
onUnmounted(() => {
    if (eventSource) {
        eventSource.close();
        eventSource = null;
    }
});
</script>

<style scoped>
* {
    box-sizing: border-box;
}

.container {
    max-width: 100%;
    width: 100%;
    min-height: calc(100vh - 80px);
    margin: 0 auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.chat-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex: 1;
    max-width: 1000px;
    margin: 0 auto;
    width: 100%;
}

/* 配置区域样式 */
.config-panel {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.config-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.config-icon {
    background: linear-gradient(135deg, var(--va-primary), #3498db);
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    box-shadow: 0 2px 5px rgba(52, 152, 219, 0.3);
}

.config-header h2 {
    font-size: 1.3rem;
    color: #2c3e50;
    font-weight: 600;
    margin: 0;
}

.config-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-row {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.form-group {
    flex: 1;
    min-width: 200px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #34495e;
}

.input-style {
    width: 100%;
    border-radius: 20px;
}

.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-bottom: 10px;
}

/* 内容展示区域样式 */
.content-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    flex: 1;
    min-height: 500px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.empty-state {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    height: 100%;
    padding: 30px;
    color: #7f8c8d;
}

.empty-state .va-icon {
    font-size: 3rem;
    color: #bdc3c7;
    margin-bottom: 15px;
    opacity: 0.7;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #2c3e50;
}

.empty-state p {
    max-width: 450px;
    margin: 0 auto;
    line-height: 1.6;
}

.generating-state,
.complete-state {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.status-bar {
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-indicator {
    display: flex;
    align-items: center;
}

.status-badge {
    font-size: 0.85rem;
    font-weight: 500;
    padding: 4px 12px;
    border-radius: 50px;
    display: flex;
    align-items: center;
}

.status-generating {
    background: rgba(52, 152, 219, 0.15);
    color: #2980b9;
    position: relative;
    padding-left: 24px;
}

.status-generating::before {
    content: '';
    position: absolute;
    left: 10px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #3498db;
    animation: blink 1s infinite;
}

.status-paused {
    background: rgba(241, 196, 15, 0.15);
    color: #f39c12;
}

.status-completed {
    background: rgba(46, 204, 113, 0.15);
    color: #27ae60;
}

.progress-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.progress-container {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 180px;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: #edf2f7;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--va-primary), #2ecc71);
    transition: width 0.3s ease;
    border-radius: 3px;
}

.progress-text {
    font-size: 0.85rem;
    color: #64748b;
    min-width: 40px;
    text-align: right;
}

.actions {
    display: flex;
    gap: 8px;
}

.article-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    position: relative;
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.article-content::-webkit-scrollbar {
    width: 6px;
}

.article-content::-webkit-scrollbar-track {
    background: transparent;
}

.article-content::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.typing-cursor {
    display: inline-block;
    width: 2px;
    height: 1.2em;
    background: var(--va-primary);
    margin-left: 2px;
    vertical-align: middle;
    animation: blink 1s infinite;
}

@keyframes blink {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0;
    }
}

.markdown-content {
    line-height: 1.6;
    color: #34495e;
    width: 100%;
}

/* Markdown样式 */
.markdown-content>>>h1,
.markdown-content>>>h2,
.markdown-content>>>h3,
.markdown-content>>>h4,
.markdown-content>>>h5,
.markdown-content>>>h6 {
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    font-weight: 600;
    line-height: 1.25;
}

.markdown-content>>>h1 {
    font-size: 1.75em;
}

.markdown-content>>>h2 {
    font-size: 1.5em;
    padding-bottom: 0.3em;
    border-bottom: 1px solid #eaecef;
}

.markdown-content>>>h3 {
    font-size: 1.3em;
}

.markdown-content>>>h4 {
    font-size: 1.15em;
}

.markdown-content>>>p {
    margin-top: 0;
    margin-bottom: 1em;
}

.markdown-content>>>ul,
.markdown-content>>>ol {
    padding-left: 2em;
    margin-bottom: 1em;
}

.markdown-content>>>li {
    margin-bottom: 0.5em;
}

.markdown-content>>>code {
    padding: 0.2em 0.4em;
    margin: 0;
    font-size: 85%;
    background-color: rgba(27, 31, 35, 0.05);
    border-radius: 3px;
    font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, monospace;
}

.markdown-content>>>pre {
    padding: 16px;
    overflow: auto;
    font-size: 85%;
    line-height: 1.45;
    background-color: #f6f8fa;
    border-radius: 3px;
    margin-bottom: 1em;
}

.markdown-content>>>pre code {
    padding: 0;
    margin: 0;
    background-color: transparent;
    border: 0;
}

.markdown-content>>>blockquote {
    padding: 0 1em;
    color: #6a737d;
    border-left: 0.25em solid #dfe2e5;
    margin-left: 0;
    margin-right: 0;
}

.markdown-content>>>a {
    color: var(--va-primary);
    text-decoration: none;
}

.markdown-content>>>a:hover {
    text-decoration: underline;
}

.markdown-content>>>table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 1em;
    overflow-x: auto;
    display: block;
}

.markdown-content>>>table th,
.markdown-content>>>table td {
    padding: 6px 13px;
    border: 1px solid #dfe2e5;
}

.markdown-content>>>table tr {
    background-color: #fff;
    border-top: 1px solid #c6cbd1;
}

.markdown-content>>>table tr:nth-child(2n) {
    background-color: #f6f8fa;
}

.markdown-content>>>img {
    max-width: 100%;
    box-sizing: content-box;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 10px;
    }

    .form-group {
        min-width: 100%;
    }

    .action-buttons {
        flex-direction: column;
    }

    .action-buttons>* {
        width: 100%;
    }

    .content-container {
        min-height: 350px;
    }
}

.option-description {
    font-size: 0.85rem;
    color: #64748b;

}
</style>
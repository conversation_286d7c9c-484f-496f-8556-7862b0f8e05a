<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量文章生成系统</title>
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@7.2.96/css/materialdesignicons.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap"
        rel="stylesheet">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Noto Sans SC', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #e4edf5 100%);
            min-height: 100vh;
            padding: 20px;
            color: #2c3e50;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        header {
            text-align: center;
            padding: 30px 0;
            margin-bottom: 30px;
        }

        h1 {
            font-size: 2.5rem;
            color: #3498db;
            margin-bottom: 10px;
            position: relative;
            display: inline-block;
        }

        h1:after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            border-radius: 2px;
        }

        .subtitle {
            font-size: 1.1rem;
            color: #7f8c8d;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .dashboard {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 25px;
            margin-bottom: 30px;
        }

        @media (max-width: 900px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
        }

        .control-panel {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            padding: 25px;
            position: sticky;
            top: 20px;
        }

        .panel-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }

        .panel-header i {
            background: #3498db;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.2rem;
        }

        .panel-header h2 {
            font-size: 1.5rem;
            color: #2c3e50;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #34495e;
        }

        input,
        select {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #e0e6ed;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s;
            background: #f8fafc;
        }

        input:focus,
        select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }

        .btn-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 25px;
        }

        button {
            padding: 14px 20px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        button i {
            margin-right: 8px;
            font-size: 1.2rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #2c3e50;
            border: 1px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        .tasks-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }

        .tasks-header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 20px 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .tasks-header h2 {
            font-size: 1.4rem;
            font-weight: 500;
        }

        .stats {
            display: flex;
            gap: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 1.3rem;
            font-weight: 700;
        }

        .stat-label {
            font-size: 0.85rem;
            opacity: 0.8;
        }

        .task-list {
            padding: 25px;
            max-height: 600px;
            overflow-y: auto;
        }

        .task-item {
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s;
        }

        .task-item:hover {
            border-color: #3498db;
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.1);
        }

        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .task-title {
            font-weight: 600;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
        }

        .task-title i {
            margin-right: 10px;
            font-size: 1.2rem;
        }

        .task-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .status-processing {
            background: rgba(52, 152, 219, 0.15);
            color: #2980b9;
        }

        .status-completed {
            background: rgba(46, 204, 113, 0.15);
            color: #27ae60;
        }

        .status-waiting {
            background: rgba(241, 196, 15, 0.15);
            color: #f39c12;
        }

        .status-paused {
            background: rgba(149, 165, 166, 0.15);
            color: #7f8c8d;
        }

        .task-actions {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-resume {
            background: rgba(46, 204, 113, 0.15);
            color: #27ae60;
        }

        .btn-resume:hover {
            background: rgba(46, 204, 113, 0.25);
        }

        .btn-pause {
            background: rgba(241, 196, 15, 0.15);
            color: #f39c12;
        }

        .btn-pause:hover {
            background: rgba(241, 196, 15, 0.25);
        }

        .btn-cancel {
            background: rgba(231, 76, 60, 0.15);
            color: #c0392b;
        }

        .btn-cancel:hover {
            background: rgba(231, 76, 60, 0.25);
        }

        .task-content {
            border-top: 1px dashed #e2e8f0;
            padding-top: 15px;
            max-height: 200px;
            overflow-y: auto;
            line-height: 1.6;
            color: #34495e;
        }

        .typing-cursor {
            display: inline-block;
            width: 8px;
            height: 1.2em;
            background: #3498db;
            margin-left: 2px;
            vertical-align: middle;
            animation: blink 1s infinite;
        }

        @keyframes blink {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0;
            }
        }

        .progress-container {
            margin-top: 15px;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .progress-bar {
            height: 10px;
            background: #edf2f7;
            border-radius: 5px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            border-radius: 5px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            transition: width 0.3s ease;
        }

        .footer-actions {
            padding: 20px;
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
        }

        .batch-controls {
            display: flex;
            gap: 10px;
        }

        .btn-export {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }

        .btn-export:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(46, 204, 113, 0.4);
        }

        .empty-state {
            text-align: center;
            padding: 50px 20px;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 4rem;
            color: #e0e6ed;
            margin-bottom: 20px;
        }

        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .empty-state p {
            max-width: 500px;
            margin: 0 auto;
            line-height: 1.6;
        }
    </style>
</head>

<body>
    <div id="app" class="container">
        <header>
            <h1>批量文章生成系统</h1>
            <p class="subtitle">使用AI技术高效生成多篇文章内容，支持实时预览与进度监控</p>
        </header>

        <div class="dashboard">
            <!-- 控制面板 -->
            <div class="control-panel">
                <div class="panel-header">
                    <VaIcon name="mso-settings" color="white" />
                    <h2>生成配置</h2>
                </div>

                <div class="form-group">
                    <label for="topic">文章主题</label>
                    <input type="text" id="topic" v-model="topic" placeholder="输入文章主题或关键词">
                </div>

                <div class="form-group">
                    <label for="count">生成数量</label>
                    <input type="number" id="count" v-model.number="count" min="1" max="10">
                </div>

                <div class="form-group">
                    <label for="length">文章长度</label>
                    <select id="length" v-model="length">
                        <option value="short">短篇 (300字左右)</option>
                        <option value="medium" selected>中篇 (600字左右)</option>
                        <option value="long">长篇 (1000字以上)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="style">写作风格</label>
                    <select id="style" v-model="style">
                        <option value="informative">信息型</option>
                        <option value="persuasive">说服型</option>
                        <option value="narrative">叙述型</option>
                        <option value="creative">创意型</option>
                    </select>
                </div>

                <div class="btn-group">
                    <button class="btn-primary" @click="startBatch">
                        <i class="mdi mdi-play"></i> 开始生成
                    </button>
                    <button class="btn-secondary" @click="resetForm">
                        <i class="mdi mdi-autorenew"></i> 重置
                    </button>
                </div>
            </div>

            <!-- 任务面板 -->
            <div class="tasks-container">
                <div class="tasks-header">
                    <h2>生成任务</h2>
                    <div class="stats">
                        <div class="stat-item">
                            <div class="stat-value">{{ completedCount }}</div>
                            <div class="stat-label">已完成</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">{{ processingCount }}</div>
                            <div class="stat-label">进行中</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">{{ tasks.length }}</div>
                            <div class="stat-label">总任务</div>
                        </div>
                    </div>
                </div>

                <div class="task-list">
                    <div v-if="tasks.length === 0" class="empty-state">
                        <i class="mdi mdi-file-document-multiple-outline"></i>
                        <h3>暂无生成任务</h3>
                        <p>请配置文章参数后点击"开始生成"按钮，系统将为您批量创建文章内容</p>
                    </div>

                    <div v-for="(task, index) in tasks" :key="task.id" class="task-item">
                        <div class="task-header">
                            <div class="task-title">
                                <i class="mdi mdi-file-document-outline"></i>
                                文章 {{ index + 1 }}: {{ task.title || '生成中...' }}
                            </div>
                            <div class="task-status" :class="'status-' + task.status">
                                {{ statusText[task.status] }}
                            </div>
                        </div>

                        <div class="task-content">
                            {{ task.content }}
                            <span v-if="task.status === 'processing'" class="typing-cursor"></span>
                        </div>

                        <div class="progress-container">
                            <div class="progress-header">
                                <span>生成进度</span>
                                <span>{{ task.progress }}%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" :style="{ width: task.progress + '%' }"></div>
                            </div>
                        </div>

                        <div class="task-actions">
                            <div v-if="task.status === 'processing'" class="action-btn btn-pause"
                                @click="pauseTask(task.id)">
                                <i class="mdi mdi-pause"></i> 暂停
                            </div>
                            <div v-if="task.status === 'paused'" class="action-btn btn-resume"
                                @click="resumeTask(task.id)">
                                <i class="mdi mdi-play"></i> 继续
                            </div>
                            <div v-if="task.status !== 'completed'" class="action-btn btn-cancel"
                                @click="cancelTask(task.id)">
                                <i class="mdi mdi-close"></i> 取消
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="tasks.length > 0" class="footer-actions">
                    <div class="batch-controls">
                        <button class="btn-secondary" @click="pauseAll">
                            <i class="mdi mdi-pause"></i> 暂停全部
                        </button>
                        <button class="btn-secondary" @click="resumeAll">
                            <i class="mdi mdi-play"></i> 继续全部
                        </button>
                        <button class="btn-secondary" @click="cancelAll">
                            <i class="mdi mdi-close"></i> 取消全部
                        </button>
                    </div>

                    <button class="btn-export" @click="exportArticles">
                        <i class="mdi mdi-download"></i> 导出完成项
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, computed, onMounted } = Vue;

        createApp({
            setup() {
                // 表单数据
                const topic = ref('人工智能');
                const count = ref(3);
                const length = ref('medium');
                const style = ref('informative');

                // 任务状态文本
                const statusText = {
                    'waiting': '等待中',
                    'processing': '生成中',
                    'paused': '已暂停',
                    'completed': '已完成',
                    'canceled': '已取消'
                };

                // 任务数据
                const tasks = ref([]);
                const eventSources = ref({});

                // 计算属性
                const completedCount = computed(() => {
                    return tasks.value.filter(t => t.status === 'completed').length;
                });

                const processingCount = computed(() => {
                    return tasks.value.filter(t => t.status === 'processing').length;
                });

                // 初始化
                onMounted(() => {
                    // 尝试从本地存储加载任务
                    const savedTasks = localStorage.getItem('articleTasks');
                    if (savedTasks) {
                        try {
                            tasks.value = JSON.parse(savedTasks);
                            // 恢复任务状态
                            tasks.value.forEach(task => {
                                if (task.status === 'processing') {
                                    task.status = 'paused';
                                }
                            });
                        } catch (e) {
                            console.error('加载任务失败:', e);
                        }
                    }
                });

                // 开始批量生成
                const startBatch = () => {
                    if (!topic.value) {
                        alert('请输入文章主题');
                        return;
                    }

                    // 创建任务
                    for (let i = 0; i < count.value; i++) {
                        const taskId = 'task-' + Date.now() + '-' + i;
                        tasks.value.push({
                            id: taskId,
                            title: '',
                            content: '',
                            progress: 0,
                            status: 'waiting',
                            params: {
                                topic: topic.value,
                                length: length.value,
                                style: style.value
                            }
                        });
                    }

                    // 启动任务
                    startNextTasks();
                    saveTasksToLocalStorage();
                };

                // 启动下一个任务
                const startNextTasks = () => {
                    // 检查并发数量（最多同时处理2个任务）
                    const processingTasks = tasks.value.filter(t => t.status === 'processing');
                    if (processingTasks.length >= 2) return;

                    // 查找等待中的任务
                    const waitingTask = tasks.value.find(t => t.status === 'waiting');
                    if (waitingTask) {
                        startTask(waitingTask);
                        // 递归调用以启动更多任务
                        setTimeout(startNextTasks, 500);
                    }
                };

                // 启动单个任务
                const startTask = (task) => {
                    task.status = 'processing';

                    // 构建API URL
                    const apiUrl = `http://192.168.31.189:9200/v1/article/outline?topic=${encodeURIComponent(task.params.topic)}`;

                    // 创建EventSource连接
                    const eventSource = new EventSource(apiUrl);
                    eventSources.value[task.id] = eventSource;

                    eventSource.onmessage = (event) => {
                        try {
                            // 解析流式数据
                            const data = JSON.parse(event.data.replace('data:', ''));

                            if (data.choices && data.choices.length > 0) {
                                const content = data.choices[0].delta.content;

                                // 更新任务内容
                                if (content) {
                                    task.content += content;

                                    // 从内容中提取标题（第一行）
                                    if (!task.title && task.content.includes('\n')) {
                                        task.title = task.content.split('\n')[0].substring(0, 50);
                                    }

                                    // 更新进度（根据内容长度估算）
                                    const targetLength = task.params.length === 'short' ? 300 :
                                        task.params.length === 'long' ? 1000 : 600;
                                    task.progress = Math.min(100, Math.floor(task.content.length / (targetLength / 100)));
                                }
                            }
                        } catch (e) {
                            console.error('解析数据失败:', e);
                        }
                    };

                    eventSource.onerror = (error) => {
                        console.error('EventSource错误:', error);
                        if (task.status === 'processing') {
                            task.status = 'paused';
                        }
                        eventSource.close();
                    };

                    // 检查任务是否完成（模拟）
                    const checkCompletion = () => {
                        if (task.status === 'processing') {
                            const targetLength = task.params.length === 'short' ? 300 :
                                task.params.length === 'long' ? 1000 : 600;

                            if (task.content.length >= targetLength) {
                                completeTask(task.id);
                            } else {
                                setTimeout(checkCompletion, 1000);
                            }
                        }
                    };

                    setTimeout(checkCompletion, 5000);
                };

                // 完成任务
                const completeTask = (taskId) => {
                    const taskIndex = tasks.value.findIndex(t => t.id === taskId);
                    if (taskIndex !== -1) {
                        tasks.value[taskIndex].status = 'completed';
                        tasks.value[taskIndex].progress = 100;

                        // 关闭EventSource
                        if (eventSources.value[taskId]) {
                            eventSources.value[taskId].close();
                            delete eventSources.value[taskId];
                        }

                        // 启动下一个任务
                        startNextTasks();
                        saveTasksToLocalStorage();
                    }
                };

                // 暂停任务
                const pauseTask = (taskId) => {
                    const taskIndex = tasks.value.findIndex(t => t.id === taskId);
                    if (taskIndex !== -1 && tasks.value[taskIndex].status === 'processing') {
                        tasks.value[taskIndex].status = 'paused';

                        // 关闭EventSource
                        if (eventSources.value[taskId]) {
                            eventSources.value[taskId].close();
                            delete eventSources.value[taskId];
                        }

                        saveTasksToLocalStorage();
                    }
                };

                // 继续任务
                const resumeTask = (taskId) => {
                    const taskIndex = tasks.value.findIndex(t => t.id === taskId);
                    if (taskIndex !== -1 && tasks.value[taskIndex].status === 'paused') {
                        tasks.value[taskIndex].status = 'processing';
                        startTask(tasks.value[taskIndex]);
                        saveTasksToLocalStorage();
                    }
                };

                // 取消任务
                const cancelTask = (taskId) => {
                    const taskIndex = tasks.value.findIndex(t => t.id === taskId);
                    if (taskIndex !== -1) {
                        // 关闭EventSource
                        if (eventSources.value[taskId]) {
                            eventSources.value[taskId].close();
                            delete eventSources.value[taskId];
                        }

                        tasks.value.splice(taskIndex, 1);
                        saveTasksToLocalStorage();
                    }
                };

                // 批量操作
                const pauseAll = () => {
                    tasks.value.forEach(task => {
                        if (task.status === 'processing') {
                            pauseTask(task.id);
                        }
                    });
                };

                const resumeAll = () => {
                    tasks.value.forEach(task => {
                        if (task.status === 'paused') {
                            resumeTask(task.id);
                        }
                    });
                };

                const cancelAll = () => {
                    tasks.value.forEach(task => {
                        if (task.status !== 'completed') {
                            cancelTask(task.id);
                        }
                    });
                };

                // 导出文章
                const exportArticles = () => {
                    const completedTasks = tasks.value.filter(t => t.status === 'completed');
                    if (completedTasks.length === 0) {
                        alert('没有已完成的任务可导出');
                        return;
                    }

                    let exportContent = '';
                    completedTasks.forEach((task, index) => {
                        exportContent += `=== 文章 ${index + 1}: ${task.title} ===\n\n`;
                        exportContent += task.content + '\n\n\n';
                    });

                    // 创建下载
                    const blob = new Blob([exportContent], { type: 'text/plain' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `生成文章_${new Date().toLocaleDateString()}.txt`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                };

                // 重置表单
                const resetForm = () => {
                    topic.value = '';
                    count.value = 3;
                    length.value = 'medium';
                    style.value = 'informative';
                };

                // 保存任务到本地存储
                const saveTasksToLocalStorage = () => {
                    localStorage.setItem('articleTasks', JSON.stringify(tasks.value));
                };

                return {
                    topic,
                    count,
                    length,
                    style,
                    tasks,
                    statusText,
                    completedCount,
                    processingCount,
                    startBatch,
                    pauseTask,
                    resumeTask,
                    cancelTask,
                    pauseAll,
                    resumeAll,
                    cancelAll,
                    exportArticles,
                    resetForm
                };
            }
        }).mount('#app');
    </script>
</body>

</html>
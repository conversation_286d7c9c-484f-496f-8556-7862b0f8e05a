<template>
    <div>


        <VaCard class="filter-card">
            <!-- 筛选区域标题和控制按钮 -->
            <div class="filter-header flex justify-between items-center  pb-2">
                <div class="flex items-center gap-2">
                    <VaIcon name="mso-filter_list" color="primary" />
                    <h2 class="text-lg font-medium">{{ t('affInsight.filter.title') }}</h2>
                </div>
                <div class="flex gap-2">
                    <!-- 仅在移动设备和平板上显示展开/折叠按钮 -->
                    <VaButton v-if="isMobileView" preset="secondary" border-color="primary" size="small"
                        class="filter-toggle" @click="toggleFilter"
                        :icon="isFilterExpanded ? 'mso-expand_less' : 'mso-expand_more'"
                        :aria-label="isFilterExpanded ? t('affInsight.filter.collapseFilter') : t('affInsight.filter.expandFilter')">
                        {{ isFilterExpanded ? t('affInsight.filter.collapse') : t('affInsight.filter.expand') }}
                    </VaButton>
                </div>
            </div>

            <!-- 筛选区域内容 - 使用JS动画 -->
            <div ref="filterContent" class="filter-content" :style="{
                ...getContentStyles(),

            }">
                <!-- 筛选表单 -->
                <div class="filter-form" v-show="isFilterExpanded">
                    <!-- 筛选条件网格 -->
                    <div class="filter-grid">
                        <!-- 日期范围筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <div class="flex items-center">
                                    <label class="filter-label">{{ t('affInsight.filter.dateRange') }}</label>
                                    <VaButtonDropdown preset="plain">
                                        <div class="flex items-center flex-col gap-2 date-shortcuts">

                                            <VaButton size="small" preset="secondary" border-color="primary"
                                                @click="handleLastThreeDays">{{ t('affInsight.buttons.lastThreeDays') }}
                                            </VaButton>
                                            <VaButton size="small" preset="secondary" border-color="primary"
                                                @click="handleLastWeek">{{ t('affInsight.buttons.lastWeek') }}
                                            </VaButton>
                                            <VaButton size="small" preset="secondary" border-color="primary"
                                                @click="handleLastMonth">{{ t('affInsight.buttons.lastMonth') }}
                                            </VaButton>
                                        </div>
                                    </VaButtonDropdown>
                                </div>
                            </div>
                                <div class="lg:w-[245px] md:w-full fiter-item">
                                    <Datepicker v-model="dateRange" range locale="en-US" format="yyyy/MM/dd"
                                :enable-time-picker="false" auto-apply
                                :placeholder="t('affInsight.filter.dateRangePlaceholder')" week-numbers="iso"
                                week-num-name="We" @update:model-value="onDateRangeChange" now-button-label="Today"
                                :dark="isDark" :clearable="false" class="w-full" />
                                </div>
                        </div>
                        <!-- 渠道筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">{{ t('affInsight.filter.channel') }}</label>
                            </div>
                            <VaSelect v-model="queryParams.affIds"
                                :placeholder="t('affInsight.filter.channelPlaceholder')" :options="affList"
                                text-by="name" value-by="id" :clearable="true" searchable multiple
                                class="filter-input" />
                        </div>
                        <!-- 域名筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">{{ t('affInsight.filter.domain') }}</label>
                            </div>
                            <VaSelect v-model="queryParams.appName"
                                :placeholder="t('affInsight.filter.domainPlaceholder')" :options="domainList"
                                text-by="appName" value-by="appName" :clearable="true" searchable
                                class="filter-input" />
                        </div>


                        <!-- 广告主筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">{{ t('affInsight.filter.advertiser') }}</label>
                            </div>
                            <VaSelect v-model="queryParams.advIds"
                                :placeholder="t('affInsight.filter.advertiserPlaceholder')" :options="advList"
                                text-by="name" value-by="id" :clearable="true" searchable multiple
                                class="filter-input" />
                        </div>

                        <!-- 广告位筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">{{ t('affInsight.filter.adUnit') }}</label>
                            </div>
                            <VaInput v-model="queryParams.adsName"
                                :placeholder="t('affInsight.filter.adUnitPlaceholder')" class="filter-input" />
                        </div>

                    </div>

                    <!-- Group By 选择 -->
                    <div class="group-by-section mt-2 mb-2 flex flex-col md:flex-row md:justify-between md:items-center flex-wrap">
                        <div class="flex gap-2 items-center flex-wrap mb-2 md:mb-0">
                            <label class="filter-label">{{ t('affInsight.filter.groupBy') }}</label>
                            <div class="group-by-container">
                                <VaCheckbox v-for="item in groupByList" :key="item.value"
                                    :model-value="checkList.includes(item.label)" :label="item.label"
                                    @update:model-value="(checked) => handleCheckboxChange(item.label, checked)"
                                    :disabled="item.label === t('affInsight.group.date')" class="group-by-checkbox" />
                            </div>
                        </div>
                        <div class="filter-actions">
                          
                            <VaButton :loading="syncLoading" @click="handleRefresh" preset="secondary"
                                border-color="primary"
                                :color="syncResult.status === 'success' ? 'success' : (syncResult.status === 'error' ? 'danger' : 'primary')"
                                :size="buttonSize">
                                <div class="flex items-center gap-1">
                                    <VaIcon name="mso-sync" />
                                    <span>
                                        {{ 
                                            syncResult.status === 'success' ? t('affInsight.sync.success') :
                                            syncResult.status === 'error' ? t('affInsight.sync.error') :
                                            syncLoading ? t('affInsight.sync.inProgress') :
                                            t('affInsight.sync.modalTitle')
                                        }}
                                    </span>
                                </div>
                                <VaPopover v-if="syncResult.status === 'error'" placement="bottom">
                                    {{ syncResult.message }}
                                </VaPopover>
                            </VaButton>



                            <!-- 设置按钮 -->
                            <VaButton preset="secondary" border-color="primary" icon="mso-settings"
                                @click="openSettings" :size="buttonSize">
                                {{ t('affInsight.buttons.settings') }}
                            </VaButton>
                            <!-- 重置按钮 -->
                            <VaButton preset="secondary" border-color="primary" icon="mso-refresh" @click="resetQuery" :size="buttonSize">
                                {{ t('affInsight.buttons.reset') }}
                            </VaButton>
                              <!-- 文件上传按钮 -->
                              <VaFileUpload  icon="mso-upload" class="inline-flex" file-types=".xls,.xlsx" :model-value="[]"
                                preset="secondary" border-color="primary" :upload-button-text="uploadResult.status === 'success' ? t('affInsight.upload.success') :
                                        uploadResult.status === 'error' ? t('affInsight.upload.error') :
                                            uploadLoading ? t('affInsight.upload.inProgress') :
                                                t('affInsight.upload.buttonText')
                                    "
                                :color="uploadResult.status === 'success' ? 'success' : (uploadResult.status === 'error' ? 'danger' : 'primary')"
                                :disabled="uploadLoading" :hide-file-list="true" @file-added="handleFileAdded" :size="buttonSize">
                                <VaButton preset="secondary" border-color="primary" icon="mso-upload"
                                :size="buttonSize">
                                {{ t('affInsight.upload.buttonText') }}
                            </VaButton>
                   
                            </VaFileUpload>
                            <!-- 查询按钮 -->
                            <VaButton color="primary"  icon="mso-search" @click="handleQuery" :size="buttonSize">
                                {{ t('affInsight.buttons.search') }}
                            </VaButton>
                        </div>
                    </div>
                    <!-- 同步日期选择器（内联） -->
                    <div v-if="showSyncDatePicker" class="sync-date-picker mt-4 p-3 border border-primary rounded">
                        <p class="text-sm text-primary mb-2 text-right">{{ t('affInsight.sync.dateRangeHint') }}</p>
                        <div class="flex flex-col md:flex-row gap-3 items-center md:items-center justify-end">
                            <div class="left w-[250px]">
                                <Datepicker 
                                v-model="syncDateRange" 
                                range 
                                locale="en-US" 
                                format="yyyy/MM/dd"
                                :enable-time-picker="false" 
                                auto-apply
                                :placeholder="t('affInsight.filter.dateRangePlaceholder')" 
                                :dark="isDark" 
                                :clearable="false"
                                class="flex-grow"
                            />
                            </div>
                            <div class="flex gap-2">
                                <VaButton preset="secondary" border-color="primary" :size="buttonSize" @click="handleSyncCancel">
                                    {{ t('affInsight.buttons.cancel') }}
                                </VaButton>
                                <VaButton color="primary" :size="buttonSize" :loading="syncLoading" @click="handleSyncConfirm">
                                    {{ t('affInsight.sync.confirm') }}
                                </VaButton>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </VaCard>

        <VaCard>
            <!-- 表格区域 -->
            <VaDataTable v-model:sort-by="sortBy" v-model:sorting-order="sortingOrder" :columns="columns" :items="list"
                :loading="loading" @update:sorting-order="handleSortChange" bordered hoverable>
                <template #cell(adsImpression)="{ rowData }">
                    {{ formatNumber(rowData.adsImpression) }}
                </template>
                <template #cell(affName)="{ rowData }">
                    {{ rowData.affName || '--' }}
                </template>
                <template #cell(newUser)="{ rowData }">
                    {{ rowData.newUser || '--' }}
                </template>
                <template #cell(totalUser)="{ rowData }">
                    {{ formatNumber(rowData.totalUser) || '--' }}
                </template>
                <template #cell(pageViews)="{ rowData }">
                    {{ formatNumber(rowData.pageViews) || '--' }}
                </template>
                <template #cell(activeViewRate)="{ rowData }">
                    <span :style="shouldHighlight('activeViewRate', rowData.activeViewRate) ? getHighlightStyle('activeViewRate') : {}">
                        {{ rowData.activeViewRate !== undefined && rowData.activeViewRate !== null ? rowData.activeViewRate
                            + '%' : '--' }}
                    </span>
                </template>
                <template #cell(impressionRate)="{ rowData }">
                    <span :style="shouldHighlight('impressionRate', rowData.impressionRate) ? getHighlightStyle('impressionRate') : {}">
                        {{ rowData.impressionRate !== undefined && rowData.impressionRate !== null ? rowData.impressionRate
                            + '%' : '--' }}
                    </span>
                </template>
                <template #cell(bounceRate)="{ rowData }">
                    {{ rowData.bounceRate !== undefined && rowData.bounceRate !== null ? rowData.bounceRate + '%' : '--'
                    }}
                </template>
                <template #cell(adsMatch)="{ rowData }">
                    {{ formatNumber(rowData.adsMatch) || '--' }}
                </template>
                <template #cell(matchRate)="{ rowData }">
                    <span :style="shouldHighlight('matchRate', rowData.matchRate) ? getHighlightStyle('matchRate') : {}">
                        {{ rowData.matchRate !== undefined && rowData.matchRate !== null ? rowData.matchRate + '%' : '--' }}
                    </span>
                </template>
                <template #cell(adsDisplay)="{ rowData }">
                    {{ formatNumber(rowData.adsDisplay) }}
                </template>
                <template #cell(adsRequest)="{ rowData }">
                    {{ formatNumber(rowData.adsRequest) || '--' }}
                </template>
                <template #cell(advName)="{ rowData }">
                    {{ rowData.advName || '--' }}
                </template>
                <template #cell(clickRate)="{ rowData }">
                    <span :style="shouldHighlight('clickRate', rowData.clickRate) ? getHighlightStyle('clickRate') : {}">
                        {{ rowData.clickRate !== undefined && rowData.clickRate !== null ? (rowData.clickRate) + '%' : '--'
                        }}
                    </span>
                </template>
                <template #cell(originalRevenue)="{ rowData }">
                    <template v-if="rowData.originalRevenue">
                        {{ '$' + rowData.originalRevenue }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(adsClick)="{ rowData }">
                    {{ formatNumber(rowData.adsClick) }}
                </template>
                <template #cell(ecpm)="{ rowData }">
                    <template v-if="rowData.ecpm !== undefined && rowData.ecpm !== null">
                        {{ '$' + rowData.ecpm }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(ecpc)="{ rowData }">
                    <template v-if="rowData.ecpc !== undefined && rowData.ecpc !== null">
                        {{ '$' + rowData.ecpc }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(gamEmail)="{ rowData }">
                    <template v-if="rowData.gamEmail !== undefined && rowData.gamEmail !== null">
                        {{ rowData.gamEmail }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(estimateRevenue)="{ rowData }">

                    <template v-if="rowData.affShare">
                        <div class="flex items-center gap-1 justify-start ">
                            <span>
                                {{ '$' + formatNumber(rowData.estimateRevenue) }}
                            </span>

                            <VaPopover
                                :message="`${'$' + formatNumber(rowData.originalRevenue)} * ${rowData.affShare * 100 + '%'} = ${'$' + formatNumber(rowData.estimateRevenue)}`"
                                placement="top">
                                <VaIcon name="mso-help_outline" size="16px" class="text-gray-400 cursor-help" />
                            </VaPopover>
                        </div>
                    </template>
                    <template v-else-if="rowData.estimateRevenue">
                        {{ '$' + formatNumber(rowData.estimateRevenue) }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
            </VaDataTable>
            <!-- 分页 -->
            <div class="flex justify-end mt-4 gap-2 filter-actions1 mx-4">
                <div>
                    <b>{{ total }} {{ t('affInsight.table.results') }}</b>
                    {{ t('affInsight.table.perPage') }}
                    <VaSelect v-model="queryParams.pageSize" class="!w-20 inline-block" :options="[10, 20, 50, 100]"
                        @update:model-value="getList" />
                </div>
                <VaPagination v-model="queryParams.pageNum" :pages="totalPages" :visible-pages="5"
                    buttons-preset="secondary" gapped border-color="primary" class="justify-center sm:justify-start"
                    @update:modelValue="getList" />
            </div>
        </VaCard>

        <!-- 列设置弹窗 -->
        <VaModal v-model="settingsModalOpen" :title="t('affInsight.modal.tableColumnSettings')" size="medium"
            hide-default-actions close-button mobile-fullscreen>
            <h3 class="text-lg font-semibold mb-4">{{ t('affInsight.modal.selectColumns') }}</h3>
            <!-- 按类别分组展示所有列 -->
            <div v-for="(columns, category) in columnsByCategory" :key="category" class="mb-6">
                <h4 class="font-semibold mb-2 text-primary">{{ getCategoryTranslation(category) }}</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    <VaCheckbox v-for="column in columns" :key="column.key" :label="column.label"
                        :model-value="columnsVisibility[column.key]"
                        @update:model-value="(checked) => toggleColumnVisibility(column.key, checked)"
                        :disabled="column.groupName === 'dataName'" />
                </div>
            </div>
            <!-- 操作按钮 -->
            <div class="flex justify-end mt-6 gap-4">
                <VaButton preset="secondary" @click="closeSettings" border-color="primary">{{
                    t('affInsight.buttons.cancel') }}
                </VaButton>
                <VaButton preset="secondary" border-color="primary" @click="resetToDefault">{{
                    t('affInsight.buttons.default')
                    }}</VaButton>
                <VaButton color="primary" @click="applySettings">{{ t('affInsight.buttons.apply') }}</VaButton>
            </div>

        </VaModal>

        <!-- 删除同步数据模态框 -->
    </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, computed } from 'vue'
import { useAffInsights } from './useAffInsights.js'
import { formatNumber } from '@/utils'
import { useTransition } from '@/composables/useTransition.js'
import { useAdHighlight } from '@/composables/useAdHighlight.js'
import Datepicker from '@vuepic/vue-datepicker'
import { useI18n } from 'vue-i18n'
import { useGlobalStore } from '@/stores/global-store'
import { useRoute } from 'vue-router'
const route = useRoute();
// 使用 global store
const globalStore = useGlobalStore()

// 根据设备类型计算按钮大小
const buttonSize = computed(() => {
  return globalStore.deviceType === 'mobile' || globalStore.deviceType === 'tablet' ? 'small' : 'medium'
})

// 使用 i18n
const { t } = useI18n()

// 使用广告高亮Hook
const { shouldHighlight, getHighlightStyle } = useAdHighlight()

// 使用过渡动画功能
const {
    isExpanded: isFilterExpanded,
    contentRef: filterContent,
    isMobileView,
    toggle: toggleFilter,
    getContentStyles,
    init: initTransition,
    cleanup: cleanupTransition
} = useTransition({
    defaultExpanded: true,  // PC端默认展开
    breakpoint: 992,        // 小于992px为移动设备
    animationDuration: 300  // 动画持续时间
})

const {
    // 状态
    loading,
    list,
    dateRange,
    checkList,
    sortBy,
    isDark,
    sortingOrder,
    advList,
    affList,
    domainList,
    queryParams,
    groupByList,
    columns,
    total,
    totalPages,
    settingsModalOpen,
    columnsVisibility,
    columnsByCategory,
    allColumnsConfig,
    syncLoading,
    syncResult,
    syncProgress,
    syncModalOpen,
    syncDateRange,
    showSyncDatePicker,
    uploadLoading,
    uploadResult,
    uploadModalOpen,
    // 方法
    handleCheckboxChange,
    getList,
    handleQuery,
    resetQuery,
    init,
    openSettings,
    closeSettings,
    applySettings,
    toggleColumnVisibility,
    handleSortChange,
    handleRefresh,
    handleSyncConfirm,
    handleSyncCancel,
    handleFileAdded,
    handleUploadCancel
} = useAffInsights()




// 处理日期范围变化
const onDateRangeChange = (value) => {
    if (value && value.length === 2) {
        // 当选择了有效的日期范围时
        handleQuery(); // 触发查询
    }
}

// 快捷选择按钮的点击事件处理函数
const handleLastThreeDays = () => {
    const today = new Date()
    const lastWeek = new Date()
    lastWeek.setDate(today.getDate() - 3) // 最近三天
    dateRange.value = [lastWeek, today]
    handleQuery()
}

const handleLastWeek = () => {
    const today = new Date()
    const lastWeek = new Date()
    lastWeek.setDate(today.getDate() - 6) // 最近一周是今天和前6天
    dateRange.value = [lastWeek, today]
    handleQuery()
}

const handleLastMonth = () => {
    const today = new Date()
    const lastMonth = new Date()
    lastMonth.setDate(today.getDate() - 29) // 最近一月是今天和前29天
    dateRange.value = [lastMonth, today]
    handleQuery()
}

// 重置为默认设置
const resetToDefault = () => {
    allColumnsConfig.forEach(column => {
        toggleColumnVisibility(column.key, column.visible)
    })
}

// 获取分类的翻译
const getCategoryTranslation = (category) => {
    const categoryMap = {
        '分组列': 'affInsight.modal.categoryBasic',
        '用户数据': 'affInsight.modal.categoryUser',
        '广告数据': 'affInsight.modal.categoryAd',
        '收益数据': 'affInsight.modal.categoryRevenue'
    }
    return t(categoryMap[category] || category)
}

onMounted(() => {
    init()
    initTransition() // 初始化过渡动画相关逻辑
    if (route.query.mode && route.query.mode=='today') {
        // 设置日期为今天
        const today = new Date()
        // 将日期范围设置为今天到今天
        dateRange.value = [today, today]
        // 执行查询
        handleQuery()
    }
})

// 移除事件监听
onUnmounted(() => {
    cleanupTransition() // 清理过渡动画相关事件监听
})
</script>

<style scoped>
/* 基础样式 */
.primary-label {
    color: var(--va-primary);
    font-size: 12px;
    font-weight: 600;
}

.filter-label {
    color: var(--va-primary);
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
}

.va-data-table__sortable-icon {
    opacity: 0.6;
}

.va-data-table__sortable-icon--active {
    opacity: 1;
}

/* 筛选表单布局 */
.filter-form {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

/* 筛选网格布局 */
.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 12px;
    margin-top: 10px;
}

.filter-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-bottom: 8px;
}

.filter-item-header {
    display: flex;
    align-items: center;
}

.filter-input {
    width: 100%;
}

/* 日期选择器相关样式 */
.date-filter {
    grid-column: span 2;
}

.date-shortcuts {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 4px;
}

.date-shortcuts .va-button {
    font-size: 12px;
    padding: 4px 8px;
}

/* 同步日期选择器样式 */
.sync-date-picker {
    transition: all 0.3s ease;
    background-color: var(--va-background-element);
    margin-top: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.sync-date-picker .dp__main {
    width: 100%;
}

.group-by-container {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;
}

.group-by-checkbox {
    margin: 0 !important;
}

/* 操作按钮区域 */
.filter-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 10px;
    margin: 8px 0;
}

.filter-actions1 {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    gap: 10px;
    margin: 8px 0;
}

/* 响应式样式 */
@media (max-width: 991px) {
    .filter-toggle {
        display: flex;
    }

    .filter-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 2fr));
    }

    .date-filter {
        grid-column: span 1;
    }

    .group-by-container {
        justify-content: flex-start;
    }
    
    .sync-date-picker {
        padding: 0.75rem;
    }
    
    /* 移动端按钮布局优化 */
    .filter-actions {
        gap: 6px;
        justify-content: center;
        width: 100%;
        flex-wrap: wrap;
    }
    
    .filter-actions > * {
        flex: 0 0 auto;
        min-width: calc(33.3% - 6px); /* 三列布局，考虑间距 */
        max-width: calc(50% - 6px); /* 最大宽度不超过一半 */
    }
    
    /* 确保每个按钮内的文字不会溢出 */
    .filter-actions .va-button {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    /* 为了更好的视觉效果，调整按钮内部间距 */
    .filter-actions .va-button__content {
        padding: 0 8px;
    }
    
    /* 使组按钮更紧凑 */
    .group-by-section {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .group-by-section > div:first-child {
        margin-bottom: 8px;
        width: 100%;
    }
}
/* 响应式样式 */
@media (max-width: 768px) {

    /* 移动端按钮布局优化 */
    .filter-actions {
        gap: 6px;
        justify-content: start;
        width: 100%;
        flex-wrap: wrap;
    }
    
    .filter-actions > * {
        flex: 0 0 auto;
        min-width: unset; /* 三列布局，考虑间距 */
        max-width: unset; /* 最大宽度不超过一半 */
    }
}

@media (min-width: 992px) {
    .filter-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }
    
    /* 桌面端按钮间距稍微增加，提高可读性 */
    .filter-actions {
        gap: 12px;
    }
}
</style>
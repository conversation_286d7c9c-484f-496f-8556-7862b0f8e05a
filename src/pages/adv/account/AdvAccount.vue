<template>
    <div>


        <!-- 筛选区域卡片 -->
        <VaCard class="filter-card">
            <!-- 筛选区域标题和控制按钮 -->
            <div class="filter-header flex justify-between items-center pb-2">
                <div class="flex items-center gap-2">
                    <VaIcon name="mso-filter_list" color="primary" />
                    <h2 class="text-lg font-medium">{{ t('account.filter.title') }}</h2>
                </div>
                <div class="flex gap-2">
                    <!-- 仅在移动设备和平板上显示展开/折叠按钮 -->
                    <VaButton v-if="isMobileView" preset="secondary" border-color="primary" size="small"
                        class="filter-toggle" @click="toggleFilter"
                        :icon="isFilterExpanded ? 'mso-expand_less' : 'mso-expand_more'"
                        :aria-label="isFilterExpanded ? t('account.filter.collapseFilter') : t('account.filter.expandFilter')">
                        {{ isFilterExpanded ? t('account.filter.collapse') : t('account.filter.expand') }}
                    </VaButton>
                </div>
            </div>

            <!-- 筛选区域内容 - 使用JS动画 -->
            <div ref="filterContent" class="filter-content" :style="getContentStyles()">
                <!-- 筛选表单 -->
                <div class="filter-form" v-show="isFilterExpanded">
                    <!-- 筛选条件网格 -->
                    <div class="filter-grid">
                        <!-- 名称筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">{{ t('account.filter.name') }}</label>
                            </div>
                            <VaInput v-model="queryParams.name" :placeholder="t('account.filter.namePlaceholder')"
                                class="filter-input" />
                        </div>

                        <!-- 广告来源筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">{{ t('account.filter.adSource') }}</label>
                            </div>
                            <VaSelect v-model="queryParams.adSource"
                                :placeholder="t('account.filter.adSourcePlaceholder')" class="filter-input"
                                :options="adSourceList" track-by="value" :text-by="(option) => option.text"
                                :value-by="(option) => option.value" />
                        </div>

                        <!-- 管理人员筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">{{ t('account.filter.salesman') }}</label>
                            </div>
                            <VaSelect v-model="queryParams.salesman"
                                :placeholder="t('account.filter.salesmanPlaceholder')" class="filter-input"
                                :options="salesmanList" text-by="text" value-by="value" />
                        </div>
                    </div>

                    <!-- 操作按钮区域 -->
                    <div class="filter-actions">
                        <!-- 新增按钮 -->
                        <VaButton preset="secondary" border-color="primary" icon="mso-add" @click="handleAdd">
                            {{ t('account.buttons.add') }}
                        </VaButton>
                        <!-- 重置按钮 -->
                        <VaButton preset="secondary" border-color="primary" icon="mso-refresh" @click="resetQuery">
                            {{ t('account.buttons.reset') }}
                        </VaButton>
                        <!-- 查询按钮 -->
                        <VaButton color="primary" icon="mso-search" @click="handleQuery">
                            {{ t('account.buttons.search') }}
                        </VaButton>
                    </div>
                </div>
            </div>
        </VaCard>

        <VaCard>
            <!-- 表格区域 -->
            <VaDataTable v-model:sort-by="sortBy" v-model:sorting-order="sortingOrder" :columns="columns" :items="list"
                :loading="loading">
                <!-- 自定义列内容 -->
                <template #cell(adSource)="{ rowData }">
                    <VaBadge :text="rowData.adSource" :color="getAdSourceColor(rowData.adSource)" />
                </template>

                <template #cell(createTime)="{ rowData }">
                    {{ formatTime(rowData.createTime) }}
                </template>

                <template #cell(updateTime)="{ rowData }">
                    {{ formatTime(rowData.updateTime) }}
                </template>

                <template #cell(sharePoint)="{ rowData }">
                    {{ formatSharePoint(rowData.sharePoint) }}
                </template>

                <template #cell(actions)="{ rowData }">
                    <div class="flex gap-2 justify-start">
                        <VaButton preset="primary" size="small" icon="mso-edit" :aria-label="t('account.buttons.edit')"
                            @click="handleUpdate(rowData)" />
                        <VaButton preset="primary" size="small" color="danger" icon="mso-delete" :aria-label="t('account.buttons.delete')"
                            @click="handleDelete(rowData)" />
                    </div>
                </template>
            </VaDataTable>

            <!-- 分页 -->
            <div class="flex justify-end mt-4 gap-2 filter-actions1 mx-4">
                <div>
                    <b>{{ total }} {{ t('account.table.results') }}</b>
                    {{ t('account.table.perPage') }}
                    <VaSelect v-model="queryParams.pageSize" class="!w-20 inline-block" :options="[10, 20, 50, 100]"
                        @update:model-value="getList" />
                </div>
                <VaPagination v-model="queryParams.pageNum" :pages="totalPages" :visible-pages="5"
                    buttons-preset="secondary" gapped border-color="primary" class="justify-center sm:justify-start"
                    @update:modelValue="getList" />
            </div>
        </VaCard>

        <!-- 编辑/添加弹窗 -->
        <VaModal v-model="open" :title="title" max-width="600px" @cancel="cancel" hide-default-actions close-button
            mobile-fullscreen>
            <VaForm v-slot="{ isValid }" ref="advForm"
                class="flex-col justify-start items-start gap-4 inline-flex w-full">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
                    <VaInput v-model="form.name" :label="t('account.form.advertiserName')"
                        :placeholder="t('account.form.advertiserNamePlaceholder')" class="w-full"
                        :rules="[v => !!v || t('account.validation.advertiserNameRequired')]" requiredMark />

                    <VaInput v-model="form.gamEmail" :label="t('account.form.gam')"
                        :placeholder="t('account.form.gamPlaceholder')" class="w-full" />

                    <VaInput v-model="form.gamId" :label="t('account.form.gamId')"
                        :placeholder="t('account.form.gamIdPlaceholder')" class="w-full" />
                    <VaInput v-model="form.sharePoint" :label="t('account.form.sharePoint')"
                        :placeholder="t('account.form.sharePointPlaceholder')" class="w-full"
                        :rules="[v => !!v || t('account.validation.sharePointRequired'), v => v >= 0 && v <= 100 || t('account.validation.sharePointRange')]"
                        requiredMark />


                    <VaSelect v-model="form.adSource" :label="t('account.form.adSource')"
                        :placeholder="t('account.form.adSourcePlaceholder')" class="w-full"
                        :options="adSourceList.filter(item => item.value !== null)" text-by="text" value-by="value"
                        :rules="[v => !!v || t('account.validation.adSourceRequired')]" :clearable="false"
                        requiredMark />

                    <VaSelect v-model="form.salesman" :label="t('account.form.assignTo')"
                        :placeholder="t('account.form.assignToPlaceholder')" class="w-full"
                        :options="salesmanList.filter(item => item.value !== null)" text-by="text" value-by="value"
                        :rules="[v => !!v || t('account.validation.assignToRequired')]" :clearable="false"
                        requiredMark />
                    <VaInput v-model="form.companyName" :label="t('account.form.companyName')"
                        :placeholder="t('account.form.companyNamePlaceholder')" class="w-full" />

                    <VaInput v-model="form.companyAddress" :label="t('account.form.companyAddress')"
                        :placeholder="t('account.form.companyAddressPlaceholder')" class="w-full" />

                    <VaInput v-model="form.contactMan" :label="t('account.form.contactPerson')"
                        :placeholder="t('account.form.contactPersonPlaceholder')" class="w-full" />

                    <VaInput v-model="form.contactInfo" :label="t('account.form.contactInfo')"
                        :placeholder="t('account.form.contactInfoPlaceholder')" class="w-full" />

                    <VaInput v-model="form.contactEmail" :label="t('account.form.contactEmail')"
                        :placeholder="t('account.form.contactEmailPlaceholder')" class="w-full" />

                    <VaInput v-model="form.billingEmail" :label="t('account.form.billingEmail')"
                        :placeholder="t('account.form.billingEmailPlaceholder')" class="w-full" />

                    <VaInput v-model="form.billingAddress" :label="t('account.form.billingAddress')"
                        :placeholder="t('account.form.billingAddressPlaceholder')" class="w-full" />



                    <VaSelect v-model="form.payCycle" :label="t('account.form.payCycle')"
                        :placeholder="t('account.form.payCyclePlaceholder')" class="w-full" :options="payCycleList"
                        text-by="value" value-by="value" />

                    <div class="col-span-1 md:col-span-2 ">
                        <VaTextarea v-model="form.note" :label="t('account.form.note')"
                            :placeholder="t('account.form.notePlaceholder')" class="w-full" rows="3" />
                    </div>
                </div>

                <div class="flex justify-end gap-2 mt-6 w-full">
                    <VaButton preset="secondary" @click="cancel">{{ t('account.buttons.cancel') }}</VaButton>
                    <VaButton :disabled="!isValid" type="submit" @click="submitForm">{{ t('account.buttons.confirm') }}
                    </VaButton>
                </div>
            </VaForm>
        </VaModal>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useModal, defineVaDataTableColumns, useForm, useToast } from 'vuestic-ui'
import { list as advList, add, update, remove } from "../../../api/ads/adv.js"
import { useTransition } from '@/composables/useTransition.js'
import { useI18n } from 'vue-i18n'

// 使用 i18n
const { t } = useI18n()

// 初始化toast通知
const { init: toast } = useToast()

// 使用过渡动画功能
const {
    isExpanded: isFilterExpanded,
    contentRef: filterContent,
    isMobileView,
    toggle: toggleFilter,
    getContentStyles,
    init: initTransition,
    cleanup: cleanupTransition
} = useTransition({
    defaultExpanded: true,  // PC端默认展开
    breakpoint: 992,        // 小于992px为移动设备
    animationDuration: 300  // 动画持续时间
})

// 表格列定义
const columns = defineVaDataTableColumns([
    { key: 'id', label: t('account.table.id'), sortable: false },
    { key: 'name', label: t('account.table.name'), sortable: false },
    { key: 'gamId', label: t('account.table.gamId'), sortable: false },
    { key: 'gamEmail', label: t('account.table.gamEmail'), sortable: false },
    { key: 'adSource', label: t('account.table.adSource'), sortable: false },
    { key: 'salesman', label: t('account.table.salesman'), sortable: false },
    { key: 'payCycle', label: t('account.table.payCycle'), sortable: false },
    { key: 'sharePoint', label: t('account.table.sharePoint'), sortable: false },
    { key: 'note', label: t('account.table.note'), sortable: false },
    { key: 'createTime', label: t('account.table.createTime'), sortable: false },
    { key: 'actions', label: t('account.table.actions') }
])

// 状态管理
const loading = ref(false)
const open = ref(false)
const title = ref('')
const list = ref([])
const total = ref(0)
const sumPage = ref(0) // 添加总页数变量
const form = ref({
    id: undefined,
    name: undefined,
    gamId: undefined,
    gamEmail: undefined,
    adSource: undefined,
    salesman: undefined,
    payCycle: undefined,
    sharePoint: undefined,
    note: undefined,
    companyName: undefined,
    companyAddress: undefined,
    contactMan: undefined,
    contactInfo: undefined,
    contactEmail: undefined,
})
const sortBy = ref('id')
const sortingOrder = ref('desc')

// 表单校验
const formRef = useForm('advForm')

// 移除独立的rules对象，因为我们已经在各输入组件中定义了规则

// 查询参数
const queryParams = ref({
    pageNum: 1,
    pageSize: 20,
    name: undefined,
    adSource: undefined,
    salesman: undefined,
    orderByColumn: 'createTime',
    isAsc: 'descending'
})

// 计算总页数
const totalPages = computed(() => {
    // 如果API返回了sumPage并且有效，则使用它
    if (sumPage.value > 0) {
        return sumPage.value
    }
    // 否则使用计算的总页数
    return Math.ceil(total.value / queryParams.value.pageSize)
})

// 下拉选项
const salesmanList = [
    { text: t('account.salesmanOptions.all'), value: null },
    { text: t('account.salesmanOptions.ruby'), value: 'Ruby' },
    { text: t('account.salesmanOptions.company'), value: 'company' }
]

const adSourceList = [
    { text: t('account.adSourceOptions.all'), value: null },
    { text: t('account.adSourceOptions.adx'), value: 'ADX' },
    { text: t('account.adSourceOptions.outbrain'), value: 'OUTBRAIN' },
    { text: t('account.adSourceOptions.taboola'), value: 'TABOOLA' },
    { text: t('account.adSourceOptions.native'), value: 'NATIVE' },
    { text: t('account.adSourceOptions.adsense'), value: 'ADSENSE' },
    { text: t('account.adSourceOptions.search'), value: 'SEARCH' }
]

const payCycleList = [
    { value: "Net 25" },
    { value: "Net 30" },
    { value: "Net 45" },
    { value: "Net 55" },
    { value: "Net 60" }
]

const { confirm } = useModal()

// 生命周期
onMounted(() => {
    getList()
    initTransition() // 初始化过渡动画相关逻辑
})

// 移除事件监听
onUnmounted(() => {
    cleanupTransition() // 清理过渡动画相关事件监听
})

// 获取列表数据
const getList = () => {
    loading.value = true
    advList(queryParams.value)
        .then(response => {
            // 根据新的API响应结构更新数据获取逻辑
            if (response.code === 200) {
                list.value = response.data.pageList
                total.value = response.data.total
                sumPage.value = response.data.sumPage // 更新总页数
            } else {
                // 错误处理
                list.value = []
                total.value = 0
                sumPage.value = 0
                console.error('获取数据失败:', response.msg)
                toast({
                    color: 'danger',
                    message: t('account.messages.fetchFailed', { msg: response.msg || '' }),
                })
            }
        })
        .catch(error => {
            console.error(t('account.messages.apiError'), error)
            list.value = []
            total.value = 0
            sumPage.value = 0
        })
        .finally(() => {
            loading.value = false
        })
}

// 格式化时间
const formatTime = (timestamp) => {
    if (!timestamp) return '-'
    return new Date(timestamp * 1000).toLocaleString('zh-CN')
}

// 格式化分成比例
const formatSharePoint = (value) => {
    if (!value) return '-'
    return parseFloat(value) * 100 + '%'
}

// 获取广告来源颜色
const getAdSourceColor = (source) => {
    switch (source) {
        case 'ADX':
            return 'primary'
        case 'OUTBRAIN':
            return 'success'
        case 'TABOOLA':
            return 'info'
        case 'NATIVE':
            return 'warning'
        case 'ADSENSE':
            return 'secondary'
        case 'SEARCH':
            return 'danger'
        default:
            return 'gray'
    }
}

// 搜索按钮
const handleQuery = () => {
    queryParams.value.pageNum = 1
    getList()
}

// 重置按钮
const resetQuery = () => {
    queryParams.value = {
        pageNum: 1,
        pageSize: 20,
        name: undefined,
        adSource: undefined,
        salesman: undefined
    }
    getList()
}

// 取消按钮
const cancel = () => {
    open.value = false
    resetForm()
}

// 表单重置
const resetForm = () => {
    form.value = {
        id: undefined,
        name: undefined,
        gamId: undefined,
        gamEmail: undefined,
        companyName: undefined,
        companyAddress: undefined,
        adSource: undefined,
        salesman: undefined,
        contactMan: undefined,
        contactInfo: undefined,
        contactEmail: undefined,
        billingEmail: undefined,
        billingAddress: undefined,
        payCycle: undefined,
        sharePoint: undefined,
        note: undefined
    }
}

// 新增按钮
const handleAdd = () => {
    resetForm()
    open.value = true
    title.value = t('account.modal.addAdvertiser')
}

// 修改按钮
const handleUpdate = (row) => {
    resetForm()
    form.value = { ...row, sharePoint: row.sharePoint * 100 }
    open.value = true
    title.value = t('account.modal.editAdvertiser')
}

// 删除按钮
const handleDelete = async (row) => {
    const agreed = await confirm({
        title: t('account.modal.deleteAdvertiser'),
        message: t('account.modal.deleteConfirm', { name: row.name }),
        okText: t('account.buttons.delete'),
        cancelText: t('account.buttons.cancel'),
        size: 'small'
    })

    if (agreed) {
        remove(row.id).then(response => {
            if (response.code === 200) {
                getList()
                toast({
                    color: 'success',
                    message: t('account.messages.deleteSuccess'),
                })
            }
        }).catch(error => {
            console.error(t('account.messages.apiError'), error)
        })
    }
}

// 提交表单
const submitForm = () => {
    // 表单验证通过后提交
    if (formRef.validate()) {
        if (form.value.id) {
            update({ ...form.value, sharePoint: form.value.sharePoint / 100 }).then(response => {
                if (response.code === 200) {
                    open.value = false
                    getList()
                    toast({
                        color: 'success',
                        message: t('account.messages.updateSuccess'),
                    })
                }
            }).catch(error => {
                console.error(t('account.messages.apiError'), error)
            })
        } else {
            add({ ...form.value, sharePoint: form.value.sharePoint / 100 }).then(response => {
                if (response.code === 200) {
                    open.value = false
                    getList()
                    toast({
                        color: 'success',
                        message: t('account.messages.addSuccess'),
                    })
                }
            }).catch(error => {
                console.error(t('account.messages.apiError'), error)
            })
        }
    }
}
</script>

<style scoped>
/* 基础样式 */
.primary-label {
    color: var(--va-primary);
    font-size: 12px;
    font-weight: 600;
}

.filter-label {
    color: var(--va-primary);
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
}

.va-data-table__sortable-icon {
    opacity: 0.6;
}

.va-data-table__sortable-icon--active {
    opacity: 1;
}

/* 筛选表单布局 */
.filter-form {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

/* 筛选网格布局 */
.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 12px;
    margin-top: 10px;
}

.filter-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-bottom: 8px;
}

.filter-item-header {
    display: flex;
    align-items: center;
}

.filter-input {
    width: 100%;
}

/* 操作按钮区域 */
.filter-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 10px;
    margin: 8px 0;
}

.filter-actions1 {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    gap: 10px;
    margin: 8px 0;
}

/* 响应式样式 */
@media (max-width: 991px) {
    .filter-toggle {
        display: flex;
    }

    .filter-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

@media (min-width: 992px) {
    .filter-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }
}




::deep(.va-input-wrapper__fieldset .va-input-wrapper__size-keeper) {
    display: flex;
    flex-direction: column;
    align-items: center;
}
</style>
<template>
    <div>


        <!-- 筛选区域卡片 -->
        <VaCard class="filter-card">
            <!-- 筛选区域标题和控制按钮 -->
            <div class="filter-header flex justify-between items-center pb-2">
                <div class="flex items-center gap-2">
                    <VaIcon name="mso-filter_list" color="primary" />
                    <h2 class="text-lg font-medium">{{ t('channel.filter.title') }}</h2>
                </div>
                <div class="flex gap-2">
                    <!-- 仅在移动设备和平板上显示展开/折叠按钮 -->
                    <VaButton v-if="isMobileView" preset="secondary" border-color="primary" size="small"
                        class="filter-toggle" @click="toggleFilter"
                        :icon="isFilterExpanded ? 'mso-expand_less' : 'mso-expand_more'"
                        :aria-label="isFilterExpanded ? t('channel.filter.collapseFilter') : t('channel.filter.expandFilter')">
                        {{ isFilterExpanded ? t('channel.filter.collapse') : t('channel.filter.expand') }}
                    </VaButton>
                </div>
            </div>

            <!-- 筛选区域内容 - 使用JS动画 -->
            <div ref="filterContent" class="filter-content" :style="getContentStyles()">
                <!-- 筛选表单 -->
                <div class="filter-form" v-show="isFilterExpanded">
                    <!-- 筛选条件网格 -->
                    <div class="filter-grid">
                        <!-- 名称筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">{{ t('channel.filter.name') }}</label>
                            </div>
                            <VaInput v-model="queryParams.name" :placeholder="t('channel.filter.namePlaceholder')" class="filter-input" />
                        </div>

                        <!-- 类型筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">{{ t('channel.filter.type') }}</label>
                            </div>
                            <VaSelect v-model="queryParams.type" :placeholder="t('channel.filter.typePlaceholder')" class="filter-input"
                                :options="typeList" text-by="value" value-by="value" />
                        </div>
                    </div>

                    <!-- 操作按钮区域 -->
                    <div class="filter-actions">
                        <!-- 新增按钮 -->
                        <VaButton preset="secondary" border-color="primary" icon="mso-add" @click="handleAdd">
                            {{ t('channel.buttons.add') }}
                        </VaButton>
                        <!-- 重置按钮 -->
                        <VaButton preset="secondary" border-color="primary" icon="mso-refresh" @click="resetQuery">
                            {{ t('channel.buttons.reset') }}
                        </VaButton>
                        <!-- 查询按钮 -->
                        <VaButton color="primary" icon="mso-search" @click="handleQuery">
                            {{ t('channel.buttons.search') }}
                        </VaButton>
                    </div>
                </div>
            </div>
        </VaCard>

        <VaCard>
           <!-- 表格区域 -->
           <VaDataTable v-model:sort-by="sortBy" v-model:sorting-order="sortingOrder" :columns="columns"
                    :items="list" :loading="loading">
                    <!-- 自定义列内容 -->
                    <template #cell(type)="{ rowData }">
                        <VaBadge :text="rowData.type" :color="getTypeColor(rowData.type)" />
                    </template>

                    <template #cell(affShare)="{ rowData }">
                        {{ formatSharePoint(rowData.affShare) }}
                    </template>
                    <template #cell(promoteStartTime)="{ rowData }">
                        {{ formatTime(rowData.promoteStartTime) }}
                    </template>
                    <template #cell(isCreateSysUser)="{ rowData }">
                        <VaBadge :text="rowData.isCreateSysUser ? t('channel.status.linked') : t('channel.status.unlinked')"
                            :color="rowData.isCreateSysUser ? 'success' : 'danger'" />
                    </template>
                    <template #cell(promoteEndTime)="{ rowData }">
                        {{ formatTime(rowData.promoteEndTime) }}
                    </template>
                    <template #cell(createTime)="{ rowData }">
                        {{ formatTime(rowData.createTime) }}
                    </template>

                    <template #cell(updateTime)="{ rowData }">
                        {{ formatTime(rowData.updateTime) }}
                    </template>

                    <template #cell(actions)="{ rowData }">
                        <div class="flex gap-2 justify-start">
                            <VaButton preset="primary" size="small" icon="mso-edit" :aria-label="t('channel.buttons.edit')"
                                @click="handleUpdate(rowData)" />
                            <VaButton preset="primary" size="small" icon="mso-delete" color="danger" :aria-label="t('channel.buttons.delete')"
                                @click="handleDelete(rowData)" />
                            <VaButton preset="primary" size="small" icon="mso-add" :aria-label="t('channel.buttons.assignData')"
                                @click="handleQueryApp(rowData)" :loading="assignLoading" />
                        </div>
                    </template>
                </VaDataTable>
                <!-- 分页-->
                <div class="flex justify-end mt-4 gap-2">
                    <div>
                        <b>{{ total }} {{ t('channel.table.results') }}</b>
                        {{ t('channel.table.perPage') }}
                        <VaSelect v-model="queryParams.pageSize" class="!w-20 inline-block" :options="[10, 20, 50, 100]"
                            @update:model-value="getList" />
                    </div>
                    <VaPagination v-model="queryParams.pageNum" :pages="totalPages" :visible-pages="5"
                        buttons-preset="secondary" gapped border-color="primary" class="justify-center sm:justify-start"
                        @update:modelValue="getList" />
                </div>
        </VaCard>

        <!-- 编辑/添加弹窗 -->
        <VaModal v-model="open" :title="title" max-width="600px" @cancel="cancel" hide-default-actions close-button
            mobile-fullscreen>
            <VaForm v-slot="{ isValid }" ref="channelForm"
                class="flex-col justify-start items-start gap-4 inline-flex w-full">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
                    <VaSelect v-model="form.type" :label="t('channel.form.type')" :placeholder="t('channel.form.typePlaceholder')" class="w-full" :options="typeList"
                        text-by="value" value-by="value" :rules="[v => !!v || t('channel.validation.typeRequired')]" requiredMark />

                    <VaInput v-model="form.name" :label="t('channel.form.name')" :placeholder="t('channel.form.namePlaceholder')" class="w-full"
                        :rules="[v => !!v || t('channel.validation.nameRequired')]" requiredMark />

                    <VaInput v-model="form.company" :label="t('channel.form.company')" :placeholder="t('channel.form.companyPlaceholder')" class="w-full" />

                    <VaInput v-model="form.contact" :label="t('channel.form.contact')" :placeholder="t('channel.form.contactPlaceholder')" class="w-full"
                        :disabled="!!form.id" :rules="[v => !!v || t('channel.validation.contactRequired')]" requiredMark />

                    <VaInput v-model="form.contactMail" :label="t('channel.form.contactMail')" :placeholder="t('channel.form.contactMailPlaceholder')" class="w-full" :rules="[
                        v => !!v || t('channel.validation.contactMailRequired')
                    ]" requiredMark />

                    <VaInput v-model="form.affShare" :label="t('channel.form.affShare')" :placeholder="t('channel.form.affSharePlaceholder')" :min="0" :max="100"
                        :step="1" class="w-full" :rules="[v => (v >= 0 && v <= 100) || t('channel.validation.affShareRange')]"
                        requiredMark />

                    <div class="col-span-1 md:col-span-2">
                        <label class="primary-label mb-1">{{ t('channel.form.promoteTime') }}</label>
                        <div class="flex gap-4 w-full">
                            <VaDateInput v-model="form.promoteStartTime" :placeholder="t('channel.form.promoteStartPlaceholder')" class="w-full"
                                :format="formatFn" />
                            <VaDateInput v-model="form.promoteEndTime" clearable :placeholder="t('channel.form.promoteEndPlaceholder')" class="w-full"
                                :format="formatFn" :rules="[
                                    v => !form.promoteStartTime || !v || new Date(v) >= new Date(form.promoteStartTime) || t('channel.validation.endTimeAfterStart')
                                ]" />
                        </div>
                    </div>

                    <div class="col-span-1 md:col-span-2">
                        <label class="va-input-label mb-1">{{ t('channel.form.createSysUser') }}</label>
                        <div class="flex gap-4">
                            <VaRadio v-model="form.isCreateSysUser" :options="[
                                {
                                    text: t('channel.form.yes'),
                                    value: true,
                                },
                                {
                                    text: t('channel.form.no'),
                                    value: false,
                                },
                            ]" value-by="value" />
                        </div>
                    </div>
                    <div v-if="form.isCreateSysUser === true" class="col-span-1 md:col-span-2">
                        <VaInput v-model="password" :label="t('channel.form.password')" :placeholder="t('channel.form.passwordPlaceholder')" class="w-full" />
                    </div>
                </div>

                <div class="flex justify-end gap-2 mt-6 w-full">
                    <VaButton preset="secondary" @click="cancel">{{ t('channel.buttons.cancel') }}</VaButton>
                    <VaButton :disabled="!isValid" type="submit" @click="submitForm">{{ t('channel.buttons.confirm') }}</VaButton>
                </div>
            </VaForm>
        </VaModal>

        <!-- 分配数据弹窗 -->
        <VaModal v-model="openApp" :title="titleApp" max-width="1100px" @cancel="cancelApp" hide-default-actions
            close-button mobile-fullscreen>
            <div class="p-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- 左侧：已分配域名列表 -->
                    <div class="md:col-span-2">
                        <div class="flex justify-between mb-2 gap-2 align-center">
                            <div class="flex justify-start gap-2">
                                <h3 class="text-sm font-bold text-primary">{{ t('channel.domain.assignedDomains') }}</h3>
                            </div>
                            <div class="flex justify-end gap-2">
                                <VaButton preset="secondary" border-color="primary" icon="mso-add" color="primary"
                                    size="small" @click="showAddDomainForm">{{ t('channel.domain.addDomain') }}</VaButton>
                                <!-- <VaButton preset="secondary" border-color="danger" icon="mso-delete" color="danger" size="small"
                                    @click="handleBatchDelete" :disabled="selectedItemIds.length === 0">批量删除</VaButton> -->
                            </div>
                        </div>
                        <VaDataTable :loading="loadingDomains" :items="assignedDomains" :columns="domainColumns"
                            class="border rounded" hoverable select-mode="multiple"
                            @selection-change="handleSelectionChange" :wrapper-size="450" :item-size="10"
                            virtual-scroller sticky-header>
                            <template #cell(domain)="{ rowData }">
                                <div class="flex items-center">
                                    <VaIcon name="web" size="small" class="mr-2" />
                                    {{ rowData.appName }}
                                </div>
                            </template>

                            <template #cell(startTime)="{ rowData }">
                                {{ formatTime(rowData.startTime) }}
                            </template>
                            <template #cell(lastTime)="{ rowData }">
                                {{ formatTime(rowData.lastTime) }}
                            </template>

                            <template #cell(affShare)="{ rowData }">
                                {{ formatSharePoint(rowData.affShare) }}
                            </template>
                            <template #cell(deductScale)="{ rowData }">
                                {{ formatSharePoint(rowData.deductScale) }}
                            </template>
                            <template #cell(actions)="{ rowData }">
                                <div class="flex gap-2">
                                    <VaButton preset="secondary" border-color="primary" icon="mso-edit" color="primary"
                                        size="small" @click="handleUpdateDomain(rowData)" />
                                    <VaButton preset="secondary" border-color="danger" icon="mso-delete" color="danger"
                                        size="small" @click="handleRemoveDomain(rowData)"
                                        :loading="removingDomainId === rowData.id" />
                                </div>
                            </template>
                        </VaDataTable>
                    </div>
                    <!-- 右侧：添加/编辑域名表单 -->
                    <div v-if="showDomainForm" class="md:col-span-1 border rounded p-4 ">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-sm font-bold">{{ formMode === 'add' ? t('channel.domain.domainCount', { count: domainList ? domainList.length : 0 })
                                : t('channel.domain.editDomain') }}
                            </h3>
                            <VaButton icon="mso-close" size="small" flat @click="hideDomainForm" />
                        </div>

                        <!-- 域名分配表单 -->
                        <VaForm v-slot="{ isValid }" ref="appForm" class="flex flex-col gap-4">
                            <!-- 域名选择 -->
                            <div class="flex flex-col gap-2">
                                <label class="primary-label">{{ t('channel.domain.domain') }}</label>
                                <VaSelect v-model="queryParamsApp.appName" :placeholder="t('channel.domain.domainPlaceholder')" class="w-full"
                                    :options="domainList" text-by="appName" value-by="appName" :clearable="true"
                                    searchable :loading="loadingDomainOptions" :disabled="!!queryParamsApp.id"
                                    :rules="[v => !!v || t('channel.domain.domainRequired')]" requiredMark
                                    @update:modelValue="handleDomainChange">
                                    <template #prependInner>
                                        <VaIcon name="web" color="secondary" size="small" />
                                    </template>
                                    <template #no-options>
                                        <div class="text-center p-2 text-gray-500">
                                            {{ loadingDomainOptions ? t('channel.domain.loading') : t('channel.domain.noDomains') }}
                                        </div>
                                    </template>
                                </VaSelect>
                            </div>
                            <p class="text-sm text-red-500" v-if="selectItem && selectItem.appName">{{ t('channel.domain.dataInfo', {
                                startTime: selectItem.startTime,
                                lastTime: selectItem.lastTime,
                                total: selectItem.total
                            }) }}</p>
                            <!-- 时间范围 -->
                            <div class="flex flex-col gap-2">
                                <label class="primary-label">{{ t('channel.domain.startDate') }}</label>
                                <VaDateInput v-model="queryParamsApp.startTime" :placeholder="t('channel.domain.startDatePlaceholder')" class="w-full"
                                    :format="formatFn" />
                            </div>
                            <div class="flex flex-col gap-2">
                                <label class="primary-label">{{ t('channel.domain.endDate') }}</label>
                                <VaDateInput v-model="queryParamsApp.lastTime" clearable :placeholder="t('channel.domain.endDatePlaceholder')"
                                    class="w-full" :format="formatFn" :rules="[
                                        v => !queryParamsApp.startTime || !v || new Date(v) >= new Date(queryParamsApp.startTime) || t('channel.domain.endDateAfterStart')
                                    ]" />
                            </div>

                            <!-- 比例设置 -->
                            <div class="flex gap-2 w-1/2">
                                <VaInput v-model="queryParamsApp.affShare" :placeholder="t('channel.domain.affSharePlaceholder')"
                                    :label="t('channel.domain.affShare')" class="w-full" type="number" :min="0" :max="100" :step="1"
                                    :rules="[v => (v >= 0 && v <= 100) || t('channel.domain.affShareRange')]" />
                                <VaInput v-model="queryParamsApp.deductScale" :placeholder="t('channel.domain.deductScalePlaceholder')"
                                    class="w-full" type="number" :min="0" :max="100" :step="1"
                                    :rules="[v => (v >= 0 && v <= 100) || t('channel.domain.deductScaleRange')]" :label="t('channel.domain.deductScale')" />
                            </div>


                            <!-- 表单按钮 -->
                            <div class="flex justify-end gap-2 mt-4">
                                <VaButton preset="secondary" size="small" @click="hideDomainForm">{{ t('channel.buttons.cancel') }}</VaButton>
                                <VaButton color="primary" size="small" @click="handleAssignDomain"
                                    :loading="assigningDomain" :disabled="!queryParamsApp.appName">
                                    {{ queryParamsApp.id ? t('channel.buttons.save') : t('channel.buttons.add') }}
                                </VaButton>
                            </div>
                        </VaForm>
                    </div>
                </div>
            </div>
        </VaModal>
        <!-- 移除不再需要的 openditApp 弹窗 -->
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useModal, defineVaDataTableColumns, useForm, useToast } from 'vuestic-ui'
import { list as channelList, add, update, remove, updateApp, addApp, removeApp, listApp, advs, listAvailableDomain } from "@/api/ads/channel.js"
import { useTransition } from '@/composables/useTransition.js'
import { useI18n } from 'vue-i18n'

// 使用 i18n
const { t } = useI18n()

// 初始化toast通知
const { init: toast } = useToast()

// 使用过渡动画功能
const {
    isExpanded: isFilterExpanded,
    contentRef: filterContent,
    isMobileView,
    toggle: toggleFilter,
    getContentStyles,
    init: initTransition,
    cleanup: cleanupTransition
} = useTransition({
    defaultExpanded: true,  // PC端默认展开
    breakpoint: 992,        // 小于992px为移动设备
    animationDuration: 300  // 动画持续时间
})

// 表格列定义
const columns = defineVaDataTableColumns([
    { key: 'name', label: t('channel.table.name'), sortable: false },
    { key: 'contact', label: t('channel.table.contact'), sortable: false },
    { key: 'contactMail', label: t('channel.table.contactMail'), sortable: false },
    { key: 'isCreateSysUser', label: t('channel.table.isCreateSysUser'), sortable: false },
    { key: 'company', label: t('channel.table.company'), sortable: false },
    { key: 'type', label: t('channel.table.type'), sortable: false },
    { key: 'affShare', label: t('channel.table.affShare'), sortable: false },
    { key: 'accessToken', label: t('channel.table.accessToken'), sortable: false },
    { key: 'promoteStartTime', label: t('channel.table.promoteStartTime') },
    { key: 'promoteEndTime', label: t('channel.table.promoteEndTime') },
    { key: 'createTime', label: t('channel.table.createTime'), sortable: false },
    { key: 'actions', label: t('channel.table.actions') }
])

// 状态管理
const loading = ref(false)
const open = ref(false)
const title = ref('')
const list = ref([])
const total = ref(0)
const sumPage = ref(0)
const domainList = ref([])
const openApp = ref(false)
const password = ref('')
const assignLoading = ref(false)

// 分配数据加载状态
const loadingDomains = ref(false)
const loadingDomainOptions = ref(false)
const loadingAdvertiserOptions = ref(false)
const assigningDomain = ref(false)
const assigningAdvertiser = ref(false)
const removingDomainId = ref(null)
const removingAdvertiserId = ref(null)
// 移除不再需要的 openditApp 变量
// 新增：控制域名表单显示状态和模式
const showDomainForm = ref(false)
const formMode = ref('add') // 'add' 或 'edit'
const form = ref({
    id: undefined,
    name: undefined,
    type: undefined,
    company: undefined,
    contact: undefined,
    contactMail: undefined,
    affShare: 80,
    promoteStartTime: undefined,
    promoteEndTime: undefined,
    isCreateSysUser: false
})
const sortBy = ref('createTime')
const sortingOrder = ref('desc')
const titleApp = ref('')
const selectedItemIds = ref('')
// 分配数据相关变量
const assignedDomains = ref([]) // 已分配的域名列表
const assignedAdvertisers = ref([]) // 已分配的广告主列表
const currentChannelId = ref(null) // 当前操作的渠道ID
const selectItem = ref(null)
// 已分配域名表格列
const domainColumns = defineVaDataTableColumns([
    { key: 'domain', label: t('channel.domain.domain') },
    { key: 'startTime', label: t('channel.domain.startDate') },
    { key: 'lastTime', label: t('channel.domain.endDate') },
    { key: 'affShare', label: t('channel.domain.affShare') },
    { key: 'deductScale', label: t('channel.domain.deductScale') },
    { key: 'actions', label: t('channel.table.actions'), width: '80px' }
])

const handleDomainChange = (value) => {
    // 在domainList中找到对应的域名
    const domain = domainList.value.find(item => item.appName == value)

    if (domain) {
        selectItem.value = domain
        selectItem.value.startTime = formatFn(new Date(domain.startTime * 1000))
        selectItem.value.lastTime = formatFn(new Date(domain.endTime * 1000))
    }
}

const handleSelectionChange = (items) => {
    selectedItemIds.value = items.currentSelectedItems.map(item => item.id).join(',')
    // 将对象格式化为逗号分隔的字符串
}

// 将日期对象转换为10位时间戳（秒级）
const dateToTimestamp = (date) => {
    if (!date) return undefined
    // 判断是否已经是时间戳
    if (typeof date === 'number') return date
    // 如果是Date对象，转换为10位时间戳
    if (date instanceof Date) return Math.floor(date.getTime() / 1000)
    // 如果是其他格式，尝试转换为Date再计算
    try {
        const dateObj = new Date(date)
        if (!isNaN(dateObj.getTime())) return Math.floor(dateObj.getTime() / 1000)
    } catch (e) {
        console.error('日期转换错误:', e)
    }
    return undefined
}
const advList = ref([])
const getAdvs = async () => {
    loadingAdvertiserOptions.value = true
    try {
        const response = await advs()
        if (response.code === 200) {
            advList.value = response.data.pageList || []
        }
    } catch (error) {
        console.error(t('channel.domain.fetchAdvertisersFailed'), error)
        toast({
            color: 'danger',
            message: t('channel.domain.fetchAdvertisersFailed')
        })
    } finally {
        loadingAdvertiserOptions.value = false
    }
}

const getDomains = async () => {
    loadingDomainOptions.value = true
    try {
        const response = await listAvailableDomain()
        if (response.code === 200) {
            domainList.value = response.data || []

            // 同时进行域名过滤和去重处理
            const domainMap = new Map()

            // 先过滤掉已分配的域名，同时进行去重处理
            domainList.value.forEach(item => {
                // 检查域名是否已分配
                const isAssigned = assignedDomains.value.some(assigned => assigned.appName === item.appName)
                // 如果已分配，则跳过设置为禁用状态
                if (isAssigned) {
                    item.disabled = true
                }
                // 移除www前缀获取基本域名
                const basicDomain = item.appName.replace(/^www\./, '')
                // 如果Map中不存在该基本域名，或者当前域名比已存在的更短（优先保留不带www的）
                if (!domainMap.has(basicDomain) || item.appName.length < domainMap.get(basicDomain).appName.length) {
                    domainMap.set(basicDomain, item)
                }
            })

            // 将Map转换回数组
            domainList.value = Array.from(domainMap.values())


        }
    } catch (error) {
        console.error(t('channel.domain.fetchDomainsFailed'), error)
        toast({
            color: 'danger',
            message: t('channel.domain.fetchDomainsFailed')
        })
    } finally {
        loadingDomainOptions.value = false
    }
}

// 查询应用
const handleQueryApp = async (row) => {
    assignLoading.value = true
    currentChannelId.value = row.userId
    // 将创建时间作为开始时间
    queryParamsApp.value.startTime = row.promoteStartTime ? new Date(row.promoteStartTime * 1000) : undefined
    Promise.all([
        getDomains(),
        getAdvs(),
        getAssignedDomains(row.userId),

    ]).then(() => {
        titleApp.value = t('channel.domain.title', { name: row.name })
        formMode.value = 'add'
        showDomainForm.value = true
        openApp.value = true
    }).finally(() => {
        assignLoading.value = false
    })
}

// 获取已分配的域名列表
const getAssignedDomains = async (channelId) => {
    loadingDomains.value = true
    try {
        const response = await listApp({ userId: channelId })
        if (response.code === 200) {
            assignedDomains.value = response.data.pageList || []
            assignedAdvertisers.value = response.data.pageList || []
        } else {
            assignedDomains.value = []
            toast({
                color: 'danger',
                message: t('channel.domain.fetchAssignedDomainsFailed', { msg: response.msg || '' })
            })
        }
    } catch (error) {
        console.error(t('channel.domain.fetchAssignedDomainsFailed'), error)
        assignedDomains.value = []
    } finally {
        loadingDomains.value = false
    }
}
// 新增：显示添加域名表单
const showAddDomainForm = () => {
    // 重置表单数据
    queryParamsApp.value = {
        ...queryParamsApp.value,
        appName: undefined,
        // startTime: 保留原有开始时间
        lastTime: undefined,
        affShare: undefined,
        deductScale: undefined,
        id: undefined
    }
    formMode.value = 'add'
    showDomainForm.value = true
}

// 新增：隐藏域名表单
const hideDomainForm = () => {
    showDomainForm.value = false
}

// 更新域名分配 - 修改为显示表单
const handleUpdateDomain = async (row) => {
    // 将行数据填充到表单
    queryParamsApp.value = {
        ...queryParamsApp.value,
        id: row.id,
        appName: row.appName,
        affShare: row.affShare !== undefined ? row.affShare * 100 : undefined,
        deductScale: row.deductScale !== undefined ? row.deductScale * 100 : undefined
    }
    handleDomainChange(row.appName);
    // 处理时间戳转换为日期对象
    if (row.startTime) {
        queryParamsApp.value.startTime = new Date(row.startTime * 1000)
    }

    if (row.lastTime) {
        queryParamsApp.value.lastTime = new Date(row.lastTime * 1000)
    }

    formMode.value = 'edit'
    showDomainForm.value = true
}

// 表单校验 - 域名分配表单
const appFormRef = useForm('appForm')

// 分配域名（添加或更新） - 修改成功后隐藏表单
const handleAssignDomain = async () => {
    // 使用表单验证
    if (!appFormRef.validate()) {
        toast({
            color: 'warning',
            message: t('channel.messages.formValidationWarning')
        })
        return
    }

    if (!queryParamsApp.value.appName) {
        toast({
            color: 'warning',
            message: t('channel.domain.domainRequired')
        })
        return
    }

    // 如果是新增，检查是否已经分配了该域名
    if (!queryParamsApp.value.id) {
        const isDomainAssigned = assignedDomains.value.some(
            item => item.appName === queryParamsApp.value.appName
        )

        if (isDomainAssigned) {
            toast({
                color: 'warning',
                message: t('channel.domain.domainAlreadyAssigned')
            })
            return
        }
    }

    assigningDomain.value = true

    try {
        // 构建请求数据
        const payload = {
            userId: currentChannelId.value,
            appName: queryParamsApp.value.appName
        }

        // 如果是编辑，添加ID
        if (queryParamsApp.value.id) {
            payload.id = queryParamsApp.value.id
        }

        // 处理时间字段 - 转换为10位时间戳
        if (queryParamsApp.value.startTime) {
            payload.startTime = dateToTimestamp(queryParamsApp.value.startTime)
        }

        if (queryParamsApp.value.lastTime) {
            payload.lastTime = dateToTimestamp(queryParamsApp.value.lastTime)
        }

        // 添加分成比例和扣量比例（将百分比转换为小数）
        if (queryParamsApp.value.affShare !== undefined) {
            payload.affShare = queryParamsApp.value.affShare / 100
        }

        if (queryParamsApp.value.deductScale !== undefined) {
            payload.deductScale = queryParamsApp.value.deductScale / 100
        }

        // 根据是否有ID决定是新增还是更新
        const response = queryParamsApp.value.id
            ? await updateApp(payload)
            : await addApp(payload)

        if (response.code === 200) {
            toast({
                color: 'success',
                message: queryParamsApp.value.id ? t('channel.domain.updateSuccess') : t('channel.domain.assignSuccess')
            })
            // 重新获取分配列表
            await getAssignedDomains(currentChannelId.value)
            // 关闭编辑表单
            showDomainForm.value = false
            // 清空选择
            queryParamsApp.value.appName = undefined
            queryParamsApp.value.lastTime = undefined
            queryParamsApp.value.affShare = undefined
            queryParamsApp.value.deductScale = undefined
            queryParamsApp.value.id = undefined
        } else {
            toast({
                color: 'danger',
                message: t('channel.messages.fetchFailed', { msg: response.msg || '' })
            })
        }
    } catch (error) {
        console.error(t('channel.messages.apiError'), error)
    } finally {
        assigningDomain.value = false
    }
}

// 移除域名分配
const handleRemoveDomain = async (row) => {
    const agreed = await confirm({
        title: t('channel.domain.deleteDomain'),
        message: t('channel.domain.deleteConfirm', { name: row.appName }),
        okText: t('channel.buttons.confirm'),
        cancelText: t('channel.buttons.cancel'),
        size: 'small'
    })

    if (agreed) {
        removingDomainId.value = row.id
        try {
            const response = await removeApp(row.id)
            if (response.code === 200) {
                toast({
                    color: 'success',
                    message: t('channel.domain.deleteSuccess')
                })
                // 重新获取分配列表
                await getAssignedDomains(currentChannelId.value)
            }
        } catch (error) {
            console.error(t('channel.messages.apiError'), error)

        } finally {
            removingDomainId.value = null
        }
    }
}

// 批量删除域名分配
const handleBatchDelete = async () => {
    const agreed = await confirm({
        title: t('channel.domain.batchDelete'),
        message: t('channel.domain.batchDeleteConfirm'),
        okText: t('channel.buttons.confirm'),
        cancelText: t('channel.buttons.cancel'),
        size: 'small'
    })

    if (agreed) {
        try {
            const response = await removeApp(selectedItemIds.value)
            if (response.code === 200) {
                toast({
                    color: 'success',
                    message: t('channel.domain.deleteSuccess')
                })
                // 重新获取分配列表
                await getAssignedDomains(currentChannelId.value)
            }
        } catch (error) {
            console.error(t('channel.messages.apiError'), error)

        } finally {
            selectedItemIds.value = ''
        }
    }
}
// 关闭域名列表弹窗 - 同时确保关闭表单
const cancelApp = () => {
    openApp.value = false
    showDomainForm.value = false
    resetQueryParamsApp()
}

// 重置查询参数
const resetQueryParamsApp = () => {
    queryParamsApp.value = {
        pageNum: 1,
        pageSize: 20,
        appName: undefined,
        advId: undefined,
        startTime: undefined,
        lastTime: undefined,
        affShare: undefined,
        deductScale: undefined,
        id: undefined
    }
    selectItem.value = null
}

// 获取应用列表
const getListApp = (row) => {
    listApp(row).then(response => {
    })
}
// 表单校验
const formRef = useForm('channelForm')

// 查询参数
const queryParams = ref({
    pageNum: 1,
    pageSize: 20,
    name: undefined,
    type: undefined
})
const queryParamsApp = ref({
    pageNum: 1,
    pageSize: 20,
    appName: undefined, // 分配的域名
    advId: undefined, // 分配的广告主id
    startTime: undefined,
    lastTime: undefined,
    affShare: undefined, // 分成比例
    deductScale: undefined // 扣量比例
})

// 计算总页数
const totalPages = computed(() => {
    // 如果API返回了sumPage并且有效，则使用它
    if (sumPage.value > 0) {
        return sumPage.value
    }
    // 否则使用计算的总页数
    return Math.ceil(total.value / queryParams.value.pageSize)
})

// 渠道类型选项
const typeList = [
    { value: t('channel.channelType.revenueShare') },
    { value: t('channel.channelType.cpa') }
]

const { confirm } = useModal()

const formatFn = (value) => {
    if (!value) return ''
    return `${value.getFullYear()}/${('0' + (value.getMonth() + 1)).slice(-2)}/${('0' + value.getDate()).slice(-2)}`
}

// 生命周期
onMounted(() => {
    getList()
    initTransition() // 初始化过渡动画相关逻辑
})

// 移除事件监听
onUnmounted(() => {
    cleanupTransition() // 清理过渡动画相关事件监听
})

// 获取列表数据
const getList = () => {
    loading.value = true
    channelList(queryParams.value)
        .then(response => {
            // 根据API响应结构更新数据获取逻辑
            if (response.code === 200) {
                list.value = response.data.pageList || response.rows
                total.value = response.data?.total || response.total
                sumPage.value = response.data?.sumPage || 0
            } else {
                // 错误处理
                list.value = []
                total.value = 0
                sumPage.value = 0
                console.error(t('channel.messages.fetchFailed', { msg: response.msg || '' }))
                toast({
                    color: 'danger',
                    message: t('channel.messages.fetchFailed', { msg: response.msg || '' }),
                })
            }
        })
        .catch(error => {
            console.error(t('channel.messages.apiError'), error)
            list.value = []
            total.value = 0
            sumPage.value = 0
        })
        .finally(() => {
            loading.value = false
        })
}

// 格式化时间
const formatTime = (timestamp) => {
    if (!timestamp) return '-'
    return new Date(timestamp * 1000).toLocaleString('zh-CN')
}

// 格式化分成比例
const formatSharePoint = (value) => {
    if (!value && value !== 0) return '-'
    return value * 100 + '%'
}

// 获取渠道类型颜色
const getTypeColor = (type) => {
    switch (type) {
        case '分成推广渠道':
            return 'success'
        case 'CPA推广渠道':
            return 'info'
        default:
            return 'secondary'
    }
}

// 搜索按钮
const handleQuery = () => {
    queryParams.value.pageNum = 1
    getList()
}

// 重置按钮
const resetQuery = () => {
    queryParams.value = {
        pageNum: 1,
        pageSize: 20,
        name: undefined,
        type: undefined
    }
    getList()
}

// 取消按钮
const cancel = () => {
    open.value = false

    resetForm()
}

// 表单重置
const resetForm = () => {
    form.value = {
        id: undefined,
        name: undefined,
        type: undefined,
        company: undefined,
        contact: undefined,
        contactMail: undefined,
        affShare: 80,
        promoteStartTime: undefined,
        promoteEndTime: undefined,
        isCreateSysUser: true
    }
    password.value = ''

}

// 新增按钮
const handleAdd = () => {
    resetForm()
    open.value = true
    title.value = t('channel.modal.addChannel')
}

// 修改按钮
const handleUpdate = (row) => {
    resetForm()
    const rowData = { ...row }

    // 如果时间是时间戳，转换为Date对象以便显示
    if (rowData.promoteStartTime && typeof rowData.promoteStartTime === 'number') {
        rowData.promoteStartTime = new Date(rowData.promoteStartTime * 1000)
    }

    if (rowData.promoteEndTime && typeof rowData.promoteEndTime === 'number') {
        rowData.promoteEndTime = new Date(rowData.promoteEndTime * 1000)
    }

    // 将分成比例从小数转换为百分比显示
    if (rowData.affShare !== undefined) {
        rowData.affShare = rowData.affShare * 100
    }

    form.value = rowData
    open.value = true
    title.value = t('channel.modal.editChannel')
}

// 删除按钮
const handleDelete = async (row) => {
    const agreed = await confirm({
        title: t('channel.modal.deleteChannel'),
        message: t('channel.modal.deleteConfirm', { name: row.name }),
        okText: t('channel.buttons.delete'),
        cancelText: t('channel.buttons.cancel'),
        size: 'small'
    })

    if (agreed) {
        remove(row.id).then(response => {
            if (response.code === 200) {
                getList()
                toast({
                    color: 'success',
                    message: t('channel.messages.deleteSuccess'),
                })
            } else {
                toast({
                    color: 'danger',
                    message: t('channel.messages.fetchFailed', { msg: response.msg || '' }),
                })
            }
        }).catch(error => {
            console.error(t('channel.messages.apiError'), error)

        })
    }
}

// 提交表单
const submitForm = () => {
    // 表单验证通过后提交
    if (formRef.validate()) {
        // 处理表单数据
        const submitData = { ...form.value }

        // 验证日期范围
        if (submitData.promoteStartTime && submitData.promoteEndTime &&
            new Date(submitData.promoteEndTime) < new Date(submitData.promoteStartTime)) {
            toast({
                color: 'warning',
                message: t('channel.messages.endTimeWarning')
            })
            return
        }

        // 将日期转换为10位时间戳
        submitData.promoteStartTime = dateToTimestamp(submitData.promoteStartTime)
        submitData.promoteEndTime = dateToTimestamp(submitData.promoteEndTime)

        // 将分成比例从百分比转换为小数
        if (submitData.affShare !== undefined) {
            submitData.affShare = submitData.affShare / 100
        }

        // 如果创建账户，则添加密码
        if (submitData.isCreateSysUser && !submitData.id) {
            submitData.password = password.value || undefined
        }

        if (submitData.id) {
            update(submitData).then(response => {
                if (response.code === 200) {
                    open.value = false
                    getList()
                    toast({
                        color: 'success',
                        message: t('channel.messages.updateSuccess'),
                    })
                } else {
                    toast({
                        color: 'danger',
                        message: t('channel.messages.fetchFailed', { msg: response.msg || '' }),
                    })
                }
            }).catch(error => {
                console.error(t('channel.messages.apiError'), error)

            })
        } else {
            add(submitData).then(response => {
                if (response.code === 200) {
                    open.value = false
                    getList()
                    toast({
                        color: 'success',
                        message: t('channel.messages.addSuccess'),
                    })
                } else {
                    toast({
                        color: 'danger',
                        message: t('channel.messages.fetchFailed', { msg: response.msg || '' }),
                    })
                }
            }).catch(error => {
                console.error(t('channel.messages.apiError'), error)
            })
        }
    }
}
</script>

<style scoped>
/* 基础样式 */
.primary-label {
    color: var(--va-primary);
    font-size: 12px;
    font-weight: 600;
}

.filter-label {
    color: var(--va-primary);
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
}

.va-data-table__sortable-icon {
    opacity: 0.6;
}

.va-data-table__sortable-icon--active {
    opacity: 1;
}

/* 筛选表单布局 */
.filter-form {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

/* 筛选网格布局 */
.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 12px;
    margin-top: 10px;
}

.filter-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-bottom: 8px;
}

.filter-item-header {
    display: flex;
    align-items: center;
}

.filter-input {
    width: 100%;
}

/* 操作按钮区域 */
.filter-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 10px;
    margin: 8px 0;
}

/* 响应式样式 */
@media (max-width: 991px) {
    .filter-toggle {
        display: flex;
    }

    .filter-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

@media (min-width: 992px) {
    .filter-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }
}

.va-input-label {
    font-size: 0.8rem;
    color: var(--va-text-secondary);
}
</style>
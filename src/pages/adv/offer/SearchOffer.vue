<template>
    <div>


        <!-- 筛选区域卡片 -->
        <VaCard class="filter-card">
            <!-- 筛选区域标题和控制按钮 -->
            <div class="filter-header flex justify-between items-center pb-2">
                <div class="flex items-center gap-2">
                    <VaIcon name="mso-filter_list" color="primary" />
                    <h2 class="text-lg font-medium">{{ t('search.filter.title') }}</h2>
                </div>
                <div class="flex gap-2">
                    <!-- 仅在移动设备和平板上显示展开/折叠按钮 -->
                    <VaButton v-if="isMobileView" preset="secondary" border-color="primary" size="small"
                        class="filter-toggle" @click="toggleFilter"
                        :icon="isFilterExpanded ? 'mso-expand_less' : 'mso-expand_more'"
                        :aria-label="isFilterExpanded ? t('search.filter.collapseFilter') : t('search.filter.expandFilter')">
                        {{ isFilterExpanded ? t('search.filter.collapse') : t('search.filter.expand') }}
                    </VaButton>
                </div>
            </div>

            <!-- 筛选区域内容 - 使用JS动画 -->
            <div ref="filterContent" class="filter-content" :style="getContentStyles()">
                <!-- 筛选表单 -->
                <div class="filter-form" v-show="isFilterExpanded">
                    <!-- 筛选条件网格 -->
                    <div class="filter-grid">
                        <!-- 状态筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">{{ t('search.filter.status') }}</label>
                            </div>
                            <VaSelect v-model="queryParams.status" :placeholder="t('search.filter.statusPlaceholder')" :options="offerStatusOptions"
                                text-by="text" value-by="value" class="filter-input" />
                        </div>

                        <!-- 广告主筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">{{ t('search.filter.advertiser') }}</label>
                            </div>
                            <VaSelect v-model="queryParams.advId" :placeholder="t('search.filter.advertiserPlaceholder')" :options="advOptions"
                                text-by="name" value-by="id" class="filter-input" />
                        </div>

                        <!-- 类型筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">{{ t('search.filter.type') }}</label>
                            </div>
                            <VaSelect v-model="queryParams.type" :placeholder="t('search.filter.typePlaceholder')" :options="searchTypeOptions"
                                text-by="text" value-by="value" class="filter-input" />
                        </div>
                    </div>

                    <!-- 操作按钮区域 -->
                    <div class="filter-actions">
                        <!-- 新增按钮 -->
                        <VaButton preset="secondary" border-color="primary" icon="mso-add" @click="handleAdd">
                            {{ t('search.buttons.add') }}
                        </VaButton>
                        <!-- 重置按钮 -->
                        <VaButton preset="secondary" border-color="primary" icon="mso-refresh" @click="resetQuery">
                            {{ t('search.buttons.reset') }}
                        </VaButton>
                        <!-- 查询按钮 -->
                        <VaButton color="primary" icon="mso-search" @click="handleQuery">
                            {{ t('search.buttons.search') }}
                        </VaButton>
                    </div>
                </div>
            </div>
        </VaCard>

        <VaCard>

            <!-- 表格区域 -->
            <div class="table-container">
                <div class="table-shadow-container">
                    <VaDataTable v-model:sort-by="sortBy" v-model:sorting-order="sortingOrder" :columns="columns"
                        :items="offerList" :loading="loading" striped hoverable>
                        <!-- 名称 -->
                        <template #cell(name)="{ rowData }">
                            <VaPopover v-if="rowData.name" :message="rowData.name" :max-width="300" placement="top"
                                :delay="500">
                                <span class="truncate block max-w-xs ">{{ rowData.name || '-' }}</span>
                            </VaPopover>
                            <span v-else class="truncate block max-w-xs">{{ rowData.name || '-' }}</span>
                        </template>
                        <!-- 自定义列内容 -->
                        <template #cell(status)="{ rowData }">
                            <VaBadge :text="getOfferStatus(rowData.status)" :color="getStatusColor(rowData.status)" />
                        </template>

                        <template #cell(type)="{ rowData }">
                            <VaBadge :text="getOfferType(rowData.type)" color="info" />
                        </template>

                        <template #cell(searchLink)="{ rowData }">
                            <VaPopover v-if="rowData.searchLink" :message="rowData.searchLink" :max-width="300"
                                placement="top" :delay="500">
                                <span class="truncate block max-w-xs url-hover">{{ rowData.searchLink || '-'
                                    }}</span>
                            </VaPopover>
                            <span v-else class="truncate block max-w-xs">{{ rowData.searchLink || '-' }}</span>
                        </template>

                        <template #cell(affUrl)="{ rowData }">
                            <VaPopover v-if="rowData.affUrl" :message="rowData.affUrl" :max-width="300" placement="top"
                                :delay="500">
                                <span class="truncate block max-w-xs url-hover">{{ rowData.affUrl || '-' }}</span>
                            </VaPopover>
                            <span v-else class="truncate block max-w-xs">{{ rowData.affUrl || '-' }}</span>
                        </template>
                        <template #cell(cap)="{ rowData }">
                            <VaPopover v-if="rowData.cap" :message="rowData.cap" :max-width="300" placement="top"
                                :delay="500">
                                <span class="truncate block max-w-xs ">{{ rowData.cap || '-' }}</span>
                            </VaPopover>
                            <span v-else class="truncate block max-w-xs">{{ rowData.cap || '-' }}</span>
                        </template>
                        <template #cell(createTime)="{ rowData }">
                            {{ formatTime(rowData.createTime) }}
                        </template>

                        <template #cell(updateTime)="{ rowData }">
                            {{ rowData.updateTime ? formatTime(rowData.updateTime) : '--' }}
                        </template>

                        <template #cell(actions)="{ rowData }">
                            <div class="flex gap-2 justify-start">
                                <VaButton preset="primary" size="small" icon="mso-edit" :aria-label="t('search.buttons.edit')"
                                    @click="handleUpdate(rowData)" />
                            </div>
                        </template>

                    </VaDataTable>
                </div>
            </div>

            <!-- 分页 -->
            <div class="flex justify-end mt-4 gap-2 filter-actions1 mx-4">
                <div>
                    <b>{{ total }} {{ t('search.table.results') }}</b>
                    {{ t('search.table.perPage') }}
                    <VaSelect v-model="queryParams.pageSize" class="!w-20 inline-block" :options="[10, 20, 50, 100]"
                        @update:model-value="getList" />
                </div>
                <VaPagination v-model="queryParams.pageNum" :pages="totalPages" :visible-pages="5"
                    buttons-preset="secondary" gapped border-color="primary" class="justify-center sm:justify-start"
                    @update:modelValue="getList" />
            </div>
        </VaCard>

        <!-- 编辑/添加弹窗 -->
        <VaModal v-model="open" :title="title" max-width="700px" @cancel="cancel" hide-default-actions close-button
            mobile-fullscreen>
            <VaForm ref="offerForm" v-slot="{ isValid }"
                class="flex-col justify-start items-start gap-4 inline-flex w-full">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
                    <VaInput v-model="form.name" :label="t('search.form.name')" :placeholder="t('search.form.namePlaceholder')" class="w-full"
                        :rules="[v => !!v || t('search.form.nameRequired')]" requiredMark />

                    <VaSelect v-model="form.status" :label="t('search.form.status')" :placeholder="t('search.form.statusPlaceholder')" class="w-full"
                        :options="offerStatusOptions" text-by="text" value-by="value"
                        :rules="[v => !!v || t('search.form.statusRequired')]" requiredMark />

                    <VaInput v-model="form.searchLink" :label="t('search.form.url')" :placeholder="t('search.form.urlPlaceholder')" class="w-full"
                        :rules="[v => !!v || t('search.form.urlRequired')]" requiredMark />

                    <VaInput v-model="form.cap" :label="t('search.form.cap')" :placeholder="t('search.form.capPlaceholder')" class="w-full" />

                    <VaSelect v-model="form.advId" :label="t('search.form.advertiser')" :placeholder="t('search.form.advertiserPlaceholder')"
                        class="w-full" :options="advOptions" text-by="name" value-by="id"
                        :rules="[v => !!v || t('search.form.advertiserRequired')]" requiredMark />

                    <VaSelect v-model="form.type" :label="t('search.form.type')" :placeholder="t('search.form.typePlaceholder')" class="w-full"
                        :options="searchTypeOptions" text-by="text" value-by="value" :rules="[v => !!v || t('search.form.typeRequired')]"
                        requiredMark />

                    <div class="col-span-1 md:col-span-2">
                        <VaTextarea v-model="form.remark" :label="t('search.form.note')" :placeholder="t('search.form.notePlaceholder')" class="w-full" rows="3" />
                    </div>
                </div>

                <div class="flex justify-end gap-2 mt-6 w-full">
                    <VaButton preset="secondary" @click="cancel">{{ t('search.buttons.cancel') }}</VaButton>
                    <VaButton :disabled="!isValid" type="submit" @click="submitForm">{{ t('search.buttons.confirm') }}</VaButton>
                </div>
            </VaForm>
        </VaModal>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useModal, defineVaDataTableColumns, useForm, useToast } from 'vuestic-ui'
import { offerList as list, offerAdd, offerUpdate } from "@/api/ads/offer"
import { listAll } from "@/api/ads/adv"
import { useTransition } from '@/composables/useTransition.js'
import { useI18n } from 'vue-i18n'

// 使用 i18n
const { t } = useI18n()

// 初始化toast通知
const { init: toast } = useToast()

// 使用过渡动画功能
const {
    isExpanded: isFilterExpanded,
    contentRef: filterContent,
    isMobileView,
    toggle: toggleFilter,
    getContentStyles,
    init: initTransition,
    cleanup: cleanupTransition
} = useTransition({
    defaultExpanded: true,  // PC端默认展开
    breakpoint: 992,        // 小于992px为移动设备
    animationDuration: 300  // 动画持续时间
})
// 表格列定义
const columns = defineVaDataTableColumns([
    { key: 'id', label: t('search.table.id')},
    { key: 'name', label: t('search.table.name'), },
    { key: 'hashOfferId', label: t('search.table.hashOfferId'), },
    { key: 'advName', label: t('search.table.advertiser'), },
    { key: 'status', label: t('search.table.status'), },
    { key: 'type', label: t('search.table.type'), },
    { key: 'searchLink', label: t('search.table.advUrl'), },
    { key: 'affUrl', label: t('search.table.affUrl'), },
    { key: 'cap', label: t('search.table.cap'), },
    { key: 'creator', label: t('search.table.creator'), },
    { key: 'createTime', label: t('search.table.createTime'), },
    { key: 'updator', label: t('search.table.updator'), },
    { key: 'updateTime', label: t('search.table.updateTime'), },
    {
        key: 'actions',
        label: t('search.table.actions'),
        width: '100px',

    }
])

// 状态管理
const loading = ref(false)
const open = ref(false)
const title = ref('')
const offerList = ref([])
const total = ref(0)
const domainSuggestions = ref([]) // 自动完成建议列表
const form = ref({
    id: undefined,
    advId: undefined,
    name: undefined,
    searchLink: undefined,
    cap: undefined,
    type: undefined,
    status: undefined,
    remark: undefined,
})
const sortBy = ref('createTime')
const sortingOrder = ref('desc')

// 表单校验
const offerForm = useForm('offerForm')

// 查询参数
const queryParams = ref({
    searchValue: undefined,
    status: undefined,
    advId: undefined,
    type: undefined,
    pageNum: 1,
    pageSize: 20,
    orderByColumn: 'createTime',
    isAsc: 'desc'
})

// Offer状态选项
const offerStatusOptions = [
    { text: t('search.status.connecting'), value: "0" },
    { text: t('search.status.pending'), value: "1" },
    { text: t('search.status.online'), value: "2" },
    { text: t('search.status.promoting'), value: "3" },
    { text: t('search.status.paused'), value: "4" }
]

// 搜索类型选项
const searchTypeOptions = [
    { text: t('search.type.yahoo'), value: "1" },
    { text: t('search.type.bing'), value: "2" },
    { text: t('search.type.google'), value: "3" },
    { text: t('search.type.ghs'), value: "4" },
    { text: t('search.type.bhs'), value: "5" },
    { text: t('search.type.b_n2s'), value: "6" },
    { text: t('search.type.y_n2s'), value: "7" },
    { text: t('search.type.g_afd'), value: "8" },
    { text: t('search.type.g_afs'), value: "9" },
    { text: t('search.type.y_afs'), value: "10" },
    { text: t('search.type.yangdex'), value: "11" }
]

// 广告商选项
const advOptions = ref([])

// 计算总页数
const totalPages = computed(() => {
    return Math.ceil(total.value / queryParams.value.pageSize)
})

const { confirm } = useModal()

// 生命周期
onMounted(() => {
    getDefaultSort()
    getAdvList()
    initTransition() // 初始化过渡动画相关逻辑
})

// 移除事件监听
onUnmounted(() => {
    cleanupTransition() // 清理过渡动画相关事件监听
})

// 获取广告商列表
const getAdvList = () => {
    listAll().then(response => {
        advOptions.value = response.data.pageList
    })
}

// 获取默认排序
const getDefaultSort = () => {
    queryParams.value.orderByColumn = sortBy.value
    queryParams.value.isAsc = sortingOrder.value
    getList()
}

// 获取Offer列表
const getList = () => {
    loading.value = true

    // 处理排序参数
    queryParams.value.orderByColumn = sortBy.value
    queryParams.value.isAsc = sortingOrder.value

    list(queryParams.value).then(response => {

        offerList.value = response.data.pageList
        total.value = response.data.total
        loading.value = false
    }).catch(() => {
        loading.value = false
        offerList.value = []
        total.value = 0
        toast({
            color: 'danger',
            message: t('search.messages.fetchFailed'),
        })
    })
}

// 格式化时间
const formatTime = (timestamp) => {
    if (!timestamp) return '-'
    return new Date(timestamp * 1000).toLocaleString('zh-CN')
}

// 获取Offer状态文本
const getOfferStatus = (index) => {
    if (index === undefined || index >= offerStatusOptions.length) {
        return t('search.status.unknown')
    }
    return offerStatusOptions[index].text
}

// 获取状态颜色
const getStatusColor = (index) => {
    switch (index) {
        case "0":
        case 0:
            return "warning" // 对接中
        case "1":
        case 1:
            return "info" // 待推广
        case "2":
        case 2:
            return "success" // 已上线
        case "3":
        case 3:
            return "primary" // 推广中
        case "4":
        case 4:
            return "danger" // 已暂停
        default:
            return "secondary"
    }
}

// 获取Offer类型文本
const getOfferType = (index) => {
    if (index === undefined || index >= searchTypeOptions.length) {
        return t('search.type.unknown')
    }
    return searchTypeOptions[index - 1]?.text || t('search.type.unknown')
}

// 搜索按钮
const handleQuery = () => {
    queryParams.value.pageNum = 1
    getList()
}

// 重置按钮
const resetQuery = () => {
    queryParams.value = {
        searchValue: undefined,
        status: undefined,
        advId: undefined,
        type: undefined,
        pageNum: 1,
        pageSize: 20,
        orderByColumn: 'createTime',
        isAsc: 'desc'
    }
    getList()
}

// 取消按钮
const cancel = () => {
    open.value = false
    resetForm()
}

// 表单重置
const resetForm = () => {
    form.value = {
        id: undefined,
        advId: undefined,
        name: undefined,
        searchLink: undefined,
        cap: undefined,
        type: undefined,
        status: undefined,
        remark: undefined,
    }
}

// 新增按钮
const handleAdd = () => {
    resetForm()
    open.value = true
    title.value = t('search.form.addTitle')
}

// 修改按钮
const handleUpdate = (row) => {
    resetForm()
    form.value = { ...row }
    open.value = true
    title.value = t('search.form.editTitle')
}

// 域名选择处理
const handleSelectDomain = (value) => {
    if (typeof value === 'object' && value) {
        queryParams.value.searchValue = value.value
    }
}

// 提交表单
const submitForm = () => {
    if (offerForm.validate()) {
        if (form.value.id) {
            offerUpdate(form.value).then(response => {
                if (response.code === 200) {
                    toast({
                        color: 'success',
                        message: t('search.messages.updateSuccess'),
                    })
                    open.value = false
                    getList()
                } else {
                    toast({
                        color: 'danger',
                        message: t('search.messages.updateFailed', { msg: response.msg || '' }),
                    })
                }
            }).catch(error => {
                toast({
                    color: 'danger',
                    message: t('search.messages.operationError', { msg: error.message || '' }),
                })
            })
        } else {
            offerAdd(form.value).then(response => {
                if (response.code === 200) {
                    toast({
                        color: 'success',
                        message: t('search.messages.addSuccess'),
                    })
                    open.value = false
                    getList()
                } else {
                    toast({
                        color: 'danger',
                        message: t('search.messages.addFailed', { msg: response.msg || '' }),
                    })
                }
            }).catch(error => {
                toast({
                    color: 'danger',
                    message: t('search.messages.operationError', { msg: error.message || '' }),
                })
            })
        }
    }
}
</script>

<style scoped>
/* 基础样式 */
.primary-label {
    color: var(--va-primary);
    font-size: 12px;
    font-weight: 600;
}

.filter-label {
    color: var(--va-primary);
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
}

.va-data-table__sortable-icon {
    opacity: 0.6;
}

.va-data-table__sortable-icon--active {
    opacity: 1;
}

/* 筛选表单布局 */
.filter-form {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

/* 筛选网格布局 */
.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 12px;
    margin-top: 10px;
}

.filter-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-bottom: 8px;
}

.filter-item-header {
    display: flex;
    align-items: center;
}

.filter-input {
    width: 100%;
}

/* 操作按钮区域 */
.filter-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 10px;
    margin: 8px 0;
}

.filter-actions1 {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    gap: 10px;
    margin: 8px 0;
}


/* 响应式样式 */
@media (max-width: 991px) {
    .filter-toggle {
        display: flex;
    }

    .filter-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

@media (min-width: 992px) {
    .filter-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }
}

/* 以下是原有的样式，保留并合并 */
.truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* URL悬停样式 */
.url-hover {
    border-bottom: 1px dashed var(--va-primary);
    cursor: help;
}

/* 表格样式优化 */
:deep(.va-data-table__table-td) {
    padding: 0.5rem 0.75rem;
    vertical-align: middle;
}

/* 表单标签样式 */
:deep(.va-input-wrapper__label) {
    font-size: 0.85rem;
}

/* 表格容器样式 */
.table-container {
    overflow-x: auto;
    width: 100%;
    position: relative;
    -webkit-overflow-scrolling: touch;
}

/* 确保表格单元格在条纹状态下有正确背景色 */
:deep(.va-data-table__table-tr--striped) td[style*="position: sticky"] {
    background-color: var(--va-background-element) !important;
}

/* 确保表格单元格在悬停状态下有正确背景色 */
:deep(.va-data-table__table-tr:hover) td[style*="position: sticky"] {
    background-color: var(--va-data-table-hover-color, rgba(var(--va-primary-rgb), 0.05)) !important;
}

/* 表头样式优化 */
:deep(.va-data-table__table-th) {
    position: sticky;
    top: 0;
    z-index: 1;
}

/* 增强表格可见性 */
:deep(.va-data-table__table) {
    border-collapse: separate;
    border-spacing: 0;
}

/* 表格内容溢出时显示滚动条 */
:deep(.va-data-table__table-wrapper) {
    overflow-x: auto;
    scrollbar-width: thin;
}

/* 确保最后一列固定在右侧 */
:deep(.va-data-table__table-td:last-child),
:deep(.va-data-table__table-th:last-child) {
    border-left: 1px solid var(--va-background-border);
}
</style>
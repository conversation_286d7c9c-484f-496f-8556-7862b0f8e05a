<template>
    <div>


        <!-- 筛选区域卡片 -->
        <VaCard class="filter-card">
            <!-- 筛选区域标题和控制按钮 -->
            <div class="filter-header flex justify-between items-center pb-2">
                <div class="flex items-center gap-2">
                    <VaIcon name="mso-filter_list" color="primary" />
                    <h2 class="text-lg font-medium">{{ t('google.filter.title') }}</h2>
                </div>
                <div class="flex gap-2">
                    <!-- 仅在移动设备和平板上显示展开/折叠按钮 -->
                    <VaButton v-if="isMobileView" preset="secondary" border-color="primary" size="small"
                        class="filter-toggle" @click="toggleFilter"
                        :icon="isFilterExpanded ? 'mso-expand_less' : 'mso-expand_more'"
                        :aria-label="isFilterExpanded ? t('google.filter.collapseFilter') : t('google.filter.expandFilter')">
                        {{ isFilterExpanded ? t('google.filter.collapse') : t('google.filter.expand') }}
                    </VaButton>
                </div>
            </div>

            <!-- 筛选区域内容 - 使用JS动画 -->
            <div ref="filterContent" class="filter-content" :style="getContentStyles()">
                <!-- 筛选表单 -->
                <div class="filter-form" v-show="isFilterExpanded">
                    <!-- 筛选条件网格 -->
                    <div class="filter-grid">
                        <!-- 名称筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">{{ t('google.filter.siteName') }}</label>
                            </div>
                            <VaInput v-model="queryParams.propertyName" :placeholder="t('google.filter.siteNamePlaceholder')" class="filter-input" />
                        </div>
                    </div>

                    <!-- 操作按钮区域 -->
                    <div class="filter-actions">
                        <!-- 新增按钮 -->
                        <VaButton preset="secondary" border-color="primary" icon="mso-add" @click="handleAdd">
                            {{ t('google.buttons.add') }}
                        </VaButton>
                        <!-- 重置按钮 -->
                        <VaButton preset="secondary" border-color="primary" icon="mso-refresh" @click="resetData">
                            {{ t('google.buttons.reset') }}
                        </VaButton>
                        <!-- 查询按钮 -->
                        <VaButton color="primary" icon="mso-search" @click="search">
                            {{ t('google.buttons.search') }}
                        </VaButton>
                    </div>
                </div>
            </div>
        </VaCard>

        <VaCard>
            <!-- 表格区域 -->
            <VaDataTable v-model:sort-by="sortBy" v-model:sorting-order="sortingOrder" :columns="columns" :items="list"
                :loading="loading" @update:sorting-order="handleSortChange" bordered hoverable>
                <!-- 自定义列内容 -->
                <template #cell(propertyName)="{ rowData }">
                    <div class="flex items-center gap-2" @click="copyDomain(rowData.defaultUri)">
                        {{ rowData.propertyName }}
                        <VaIcon name="content_copy" size="small" color="#666666" s />
                    </div>
                </template>

                <template #cell(defaultUri)="{ rowData }">
                    <span>{{ rowData.defaultUri }}</span>
                </template>

                <template #cell(siteSeo)="{ rowData }">
                    <VaBadge :text="rowData.siteSeo ? t('google.status.yes') : t('google.status.no')" :color="rowData.siteSeo ? 'success' : 'danger'" />
                </template>

                <template #cell(propertyCreateOn)="{ rowData }">
                    {{ formatTime(rowData.propertyCreateOn) }}
                </template>

                <template #cell(actions)="{ rowData }">
                    <div class="flex gap-2 justify-start">
                        <VaButton preset="primary" size="small" @click="checkGaData(rowData)">{{ t('google.buttons.viewGaData') }}</VaButton>
                    </div>
                </template>

            </VaDataTable>

            <!-- 分页 -->
            <div class="flex justify-end mt-4 gap-2 filter-actions1 w-full">
                <div>
                    <b>{{ total }} {{ t('google.table.results') }}</b>
                    {{ t('google.table.perPage') }}
                    <VaSelect v-model="queryParams.pageSize" class="!w-20 inline-block" :options="[10, 20, 50, 100]"
                        @update:model-value="getList" />
                </div>
                <VaPagination v-model="queryParams.pageNum" :pages="totalPages" :visible-pages="5"
                    buttons-preset="secondary" gapped border-color="primary" class="justify-center sm:justify-start"
                    @update:modelValue="getList" />
            </div>
        </VaCard>

        <!-- GA数据详情弹窗 -->
        <VaModal v-model="analyticOpen" :title="analyticTitle" max-width="85%" @cancel="clearAnalytic" min-height="80vh"
            z-index="1" :cancel-text="t('google.buttons.close')" :ok-text="t('google.buttons.confirm')" cancel-border-color="primary" close-button>
            <div class="flex flex-col gap-4">
                <!-- 筛选区域卡片 -->
                <VaCard class="filter-card">
                    <!-- 筛选区域标题和控制按钮 -->
                    <div class="filter-header flex justify-between items-center pb-2">
                        <div class="flex items-center gap-2">
                            <VaIcon name="mso-filter_list" color="primary" />
                            <h2 class="text-lg font-medium">{{ t('google.filter.title') }}</h2>
                        </div>
                        <div class="flex gap-2">
                            <!-- 仅在移动设备和平板上显示展开/折叠按钮 -->
                            <VaButton v-if="isMobileView" preset="secondary" border-color="primary" size="small"
                                class="filter-toggle" @click="toggleFilter"
                                :icon="isFilterExpanded ? 'mso-expand_less' : 'mso-expand_more'"
                                :aria-label="isFilterExpanded ? t('google.filter.collapseFilter') : t('google.filter.expandFilter')">
                                {{ isFilterExpanded ? t('google.filter.collapse') : t('google.filter.expand') }}
                            </VaButton>
                        </div>
                    </div>

                    <!-- 筛选区域内容 - 使用JS动画 -->
                    <div ref="filterContent" class="filter-content" :style="getContentStyles()">
                        <!-- 筛选表单 -->
                        <div class="filter-form" v-show="isFilterExpanded">
                            <!-- 筛选条件网格 -->
                            <div class="filter-grid">
                                <!-- 日期范围筛选 -->
                                <div class="filter-item flex flex-row gap-2 items-center">
                                    <div class="filter-item-header">
                                        <div class="flex items-center">
                                            <label class="filter-label">{{ t('google.filter.dateRange') }}</label>
                                            <VaButtonDropdown preset="plain" style="z-index: 10000 !important;"
                                                innerAnchorSelector="tesst1">
                                                <div class="flex items-center flex-col gap-2 date-shortcuts">
                                                    <VaButton size="small" preset="secondary" border-color="primary"
                                                        @click="() => { selectLastThreeDays(updateAnalyticDates) }">{{ t('google.buttons.lastThreeDays') }}
                                                    </VaButton>
                                                    <VaButton size="small" preset="secondary" border-color="primary"
                                                        @click="() => { selectLastWeek(updateAnalyticDates) }">{{ t('google.buttons.lastWeek') }}
                                                    </VaButton>
                                                    <VaButton size="small" preset="secondary" border-color="primary"
                                                        @click="() => { selectLastMonth(updateAnalyticDates) }">{{ t('google.buttons.lastMonth') }}
                                                    </VaButton>
                                                </div>
                                            </VaButtonDropdown>
                                        </div>
                                    </div>
                                    <Datepicker v-model="analyticDateRange" range locale="en-US" format="yyyy/MM/dd"
                                        :enable-time-picker="false" auto-apply :placeholder="t('google.filter.dateRangePlaceholder')" week-numbers="iso"
                                        week-num-name="We" class="filter-input" @update:model-value="onDateRangeChange"
                                        now-button-label="Today" :dark="isDark" :clearable="false">
                                    </Datepicker>
                                </div>
                            </div>

                            <!-- 操作按钮区域 -->
                            <div class="filter-actions">
                                <VaButton preset="secondary" border-color="primary" icon="mso-refresh"
                                    @click="resetAnalytic">
                                    {{ t('google.buttons.reset') }}
                                </VaButton>
                                <VaButton color="primary" icon="mso-search" @click="searchAnalytic">
                                    {{ t('google.buttons.search') }}
                                </VaButton>
                            </div>
                        </div>
                    </div>
                </VaCard>

                <!-- GA数据表格 -->
                <VaDataTable :columns="analyticColumns" :items="analytic_data" :loading="loading_ga" bordered hoverable>
                    <template #cell(bounceRate)="{ rowData }">
                        {{ rowData.bounceRate ? rowData.bounceRate + '%' : '-' }}
                    </template>

                    <template #cell(newUser)="{ rowData }">
                        {{ formatNumber(rowData.newUser) }}
                    </template>

                    <template #cell(totalUser)="{ rowData }">
                        {{ formatNumber(rowData.totalUser) }}
                    </template>

                    <template #cell(sessions)="{ rowData }">
                        {{ formatNumber(rowData.sessions) }}
                    </template>

                    <template #cell(sessionsPerUser)="{ rowData }">
                        {{ formatNumber(rowData.sessionsPerUser) }}
                    </template>

                    <template #cell(sessionDurationPerUser)="{ rowData }">
                        {{ rowData.sessionDurationPerUser ? rowData.sessionDurationPerUser + 's' : '-' }}
                    </template>

                    <template #cell(engagedSessions)="{ rowData }">
                        {{ formatNumber(rowData.engagedSessions) }}
                    </template>

                    <template #cell(engagedSessionsPerUser)="{ rowData }">
                        {{ formatNumber(rowData.engagedSessionsPerUser) }}
                    </template>

                    <template #cell(engagedSessionDuration)="{ rowData }">
                        {{ rowData.engagedSessionDuration ? rowData.engagedSessionDuration + 's' : '-' }}
                    </template>
                    <template #cell(screenPageViewsPerUser)="{ rowData }">
                        {{ formatNumber(rowData.screenPageViewsPerUser) }}
                    </template>
                    <template #cell(screenPageViews)="{ rowData }">
                        {{ formatNumber(rowData.screenPageViews) }}
                    </template>


                </VaDataTable>


            </div>
        </VaModal>

        <!-- 添加GA数据弹窗 -->
        <VaModal v-model="open" :title="t('google.modal.addGaData')" max-width="500px" @cancel="cancel" hide-default-actions close-button>
            <div class="flex flex-col gap-4">
                <VaSelect v-model="gaDomain" :label="t('google.modal.domain')" :placeholder="t('google.modal.domainPlaceholder')" :options="differList"
                    class="w-full" />

                <div class="flex justify-end gap-2 mt-4">
                    <VaButton preset="secondary" @click="cancel">{{ t('google.buttons.cancel') }}</VaButton>
                    <VaButton :loading="confrimData" :disabled="confrimData || !gaDomain" @click="submitForm">{{ t('google.buttons.confirm') }}
                    </VaButton>
                </div>
            </div>
        </VaModal>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { defineVaDataTableColumns, useToast } from 'vuestic-ui'
import { analytic_list, addGa, insights, notGaDomainList } from "@/api/ads/ga.js"
import Datepicker from '@vuepic/vue-datepicker'
import { useUserStore } from '@/stores/user-store'
import { formatNumber } from '@/utils'
import { useTransition } from '@/composables/useTransition.js'
import { useData } from '@/composables/useData.js'
import { useI18n } from 'vue-i18n'

// 使用 i18n
const { t } = useI18n()

// 初始化toast通知
const { init: toast } = useToast()
const userStore = useUserStore()
const isDark = ref(userStore.theme === 'dark')

// 使用过渡动画功能
const {
    isExpanded: isFilterExpanded,
    contentRef: filterContent,
    isMobileView,
    toggle: toggleFilter,
    getContentStyles,
    init: initTransition,
    cleanup: cleanupTransition
} = useTransition({
    defaultExpanded: true,  // PC端默认展开
    breakpoint: 992,        // 小于992px为移动设备
    animationDuration: 300  // 动画持续时间
})

// 表格列定义
const columns = defineVaDataTableColumns([
    { key: 'propertyName', label: t('google.table.site'), sortable: true, width: '200px' },
    { key: 'propertyId', label: t('google.table.propertyId'), sortable: false, width: '200px' },
    { key: 'siteSeo', label: t('google.table.seo'), sortable: false, width: '50px' },
    { key: 'propertyTimeZone', label: t('google.table.timeZone'), sortable: false, width: '200px' },
    { key: 'propertyCreateOn', label: t('google.table.createTime'), sortable: true, width: '200px' },
    { key: 'measurementId', label: t('google.table.gaCode'), sortable: false, width: '150px' },
    { key: 'actions', label: t('google.table.actions') }
])

// GA分析数据表格列定义
const analyticColumns = defineVaDataTableColumns([
    { key: 'statTimeStr', label: t('google.table.date'), sortable: true, },
    { key: 'newUser', label: t('google.table.newUsers'), sortable: true },
    { key: 'totalUser', label: t('google.table.totalUsers'), sortable: true },
    { key: 'sessions', label: t('google.table.sessions'), sortable: true },
    { key: 'sessionsPerUser', label: t('google.table.sessionsPerUser'), sortable: true },
    { key: 'sessionDurationPerUser', label: t('google.table.durationPerUser'), sortable: true },
    { key: 'engagedSessionsPerUser', label: t('google.table.engagedSessionsPerUser'), sortable: true },
    { key: 'screenPageViewsPerUser', label: t('google.table.screenPageViewsPerUser'), sortable: true },
    { key: 'engagedSessions', label: t('google.table.engagedSessions'), sortable: true },
    { key: 'screenPageViews', label: t('google.table.screenPageViews'), sortable: true },
    { key: 'bounceRate', label: t('google.table.bounceRate'), sortable: true },
])

// 状态管理
const loading = ref(false)
const loading_ga = ref(false)
const total = ref(0)
const list = ref([])
const analyticOpen = ref(false)
const analyticTitle = ref("")
const analytic_data = ref([])
const analytic_total = ref(0)
const groupBy = ref(true)
const useAnalytic = ref({})
const open = ref(false)
const confrimData = ref(false)
const differList = ref([])
const gaDomain = ref()
const sortBy = ref('defaultUri')
const sortingOrder = ref('asc')

// 查询参数
const queryParams = ref({
    pageNum: 1,
    pageSize: 20,
    propertyName: undefined,
    orderByColumn: 'defaultUri',
    isAsc: 'desc'
})

// GA分析参数
const analyticParams = ref({
    propertyId: undefined,
    endTime: undefined,
    startTime: undefined,
    groupBy: undefined,
    pageNum: 1,
    pageSize: 20
})

// 使用useData处理日期相关逻辑
const {
    dateRange: analyticDateRange,
    selectLastThreeDays,
    selectLastWeek,
    selectLastMonth,
    onDateRangeChange: handleDateRangeChange,
    formatDate
} = useData({
    defaultDateRange: 'week'  // 默认选择最近一周
})

// 处理日期范围变化
const onDateRangeChange = (value) => {
    if (value && value.length === 2) {
        // 转换为10位时间戳
        analyticParams.value.startTime = dateToTimestamp(value[0])
        analyticParams.value.endTime = dateToTimestamp(value[1])
    }
}

// 监听排序变化
const handleSortChange = (value) => {
    if (value === null) {
        return
    }

    // 将Vuestic DataTable的排序值转换为后端API所需的格式
    queryParams.value.orderByColumn = sortBy.value
    queryParams.value.isAsc = value === 'asc' ? 'ascending' : 'descending'
    getList()
}
const sortingOrderOptions = [
    { text: "asc", value: "asc" },
    { text: "desc", value: "desc" },

];

const copyDomain = (domain) => {
    if (!domain) return

    navigator.clipboard.writeText(domain)
    toast({
        color: 'success',
        message: t('google.messages.copySuccess'),
    })
}

// 计算总页数
const totalPages = computed(() => {
    return Math.ceil(total.value / queryParams.value.pageSize)
})

// 生命周期钩子
onMounted(() => {
    getList()
    initTransition() // 初始化过渡动画相关逻辑
})

// 移除事件监听
onUnmounted(() => {
    cleanupTransition() // 清理过渡动画相关事件监听
})

// 将日期对象转换为10位时间戳（秒级）
function dateToTimestamp(date) {
    if (!date) return ''
    if (!(date instanceof Date)) {
        try {
            date = new Date(date)
        } catch (e) {
            console.error(t('google.messages.dateError'), e)
            return ''
        }
    }
    // 将时间设置为当天的0:00
    const normalizedDate = new Date(date)
    normalizedDate.setHours(0, 0, 0, 0)
    return Math.floor(normalizedDate.getTime() / 1000)
}

// 获取列表数据
const getList = () => {
    loading.value = true
    analytic_list(queryParams.value).then(response => {
        if (response.code === 200) {
            list.value = response.rows || response.data?.pageList || []
            total.value = response.total || response.data?.total || 0
        }
    }).catch(error => {
        console.error(t('google.messages.apiError'), error)
        list.value = []
        total.value = 0
    }).finally(() => {
        loading.value = false
    })
}

// 重置查询参数
const resetData = () => {
    queryParams.value = {
        pageNum: 1,
        pageSize: 25,
        propertyName: undefined,
        orderByColumn: 'defaultUri',
        isAsc: 'descending'
    }
    sortBy.value = 'defaultUri'
    sortingOrder.value = 'descending'
    getList()
}

// 搜索按钮
const search = () => {
    queryParams.value.pageNum = 1
    getList()
}

// 更新分析日期范围
const updateAnalyticDates = () => {
    // 检查日期范围是否为数组且包含两个有效日期
    if (analyticDateRange.value && analyticDateRange.value.length === 2) {
        // 转换为10位时间戳
        analyticParams.value.startTime = dateToTimestamp(analyticDateRange.value[0])
        analyticParams.value.endTime = dateToTimestamp(analyticDateRange.value[1])

        // 执行查询
        searchAnalytic()
    }
}

// 清除查看GA数据的查询数据
const clearAnalytic = () => {
    analyticParams.value = {
        propertyId: undefined,
        startTime: undefined,
        endTime: undefined,
        groupBy: undefined,
        pageNum: 1,
        pageSize: 20
    }
    analyticTitle.value = ""
    analyticOpen.value = false
    analytic_data.value = []
    useAnalytic.value = {}
}

// 给需要查询GA数据赋予默认值
const initAnalyticParams = () => {
    // 使用selectLastWeek设置默认日期范围
    selectLastWeek()

    // 更新查询参数
    analyticParams.value = {
        propertyId: useAnalytic.value.propertyId,
        startTime: dateToTimestamp(analyticDateRange.value[0]),
        endTime: dateToTimestamp(analyticDateRange.value[1]),
        groupBy: "date",
        pageNum: 1,
        pageSize: 20
    }
}

function getPreviousDaysDateRange(days) {
    const today = new Date()
    const endDate = new Date(today)
    const startDate = new Date(today)
    startDate.setDate(startDate.getDate() - days)
    // 返回数组格式
    return [startDate, endDate]
}

// 查看GA数据
const checkGaData = (row) => {
    clearAnalytic()
    useAnalytic.value = row
    analyticTitle.value = t('google.modal.analyticTitle', { site: row.propertyName })
    analyticOpen.value = true
    initAnalyticParams()
    getAnalyticList()
}

// 获取GA数据列表
const getAnalyticList = () => {
    loading_ga.value = true
    insights(analyticParams.value).then(response => {
        if (response.code === 200) {
            analytic_data.value = response.data || []
        } else {
            analytic_data.value = []
            analytic_total.value = 0
            toast({
                color: 'danger',
                message: t('google.messages.fetchGaDataFailed', { msg: response.msg || '' }),
            })
        }
    }).catch(error => {
        console.error(t('google.messages.apiError'), error)
        analytic_data.value = []
        analytic_total.value = 0
    }).finally(() => {
        loading_ga.value = false
    })
}

// 重置GA分析参数
const resetAnalytic = () => {
    if (useAnalytic.value && useAnalytic.value.propertyId) {
        initAnalyticParams()
        getAnalyticList()
    } else {
        clearAnalytic()
    }
}

// 搜索GA分析数据
const searchAnalytic = () => {
    analyticParams.value.groupBy = groupBy.value ? "date" : ""
    getAnalyticList()
}

// 处理添加GA数据
const handleAdd = () => {
    confrimData.value = false
    open.value = true
    differList.value = []
    gaDomain.value = undefined
    getNoLoadGaCodeList()
}

// 取消添加
const cancel = () => {
    open.value = false
}

// 提交添加GA数据
const submitForm = () => {
    if (!gaDomain.value) {
        toast({
            color: 'warning',
            message: t('google.messages.domainRequired'),
        })
        return
    }

    confrimData.value = true
    addGa(gaDomain.value).then(response => {
        if (response.code === 200) {
            toast({
                color: 'success',
                message: t('google.messages.addSuccess'),
            })
            getList()
        } else {
            toast({
                color: 'danger',
                message: t('google.messages.addFailed', { msg: response.msg || '' }),
            })
        }
    }).catch(error => {
        console.error(t('google.messages.apiError'), error)
    }).finally(() => {
        confrimData.value = false
        open.value = false
    })
}

// 获取未加载GA代码的域名列表
const getNoLoadGaCodeList = () => {
    notGaDomainList().then(response => {
        if (response.code === 200) {
            differList.value = response.data || []
        } else {
            differList.value = []
            toast({
                color: 'danger',
                message: t('google.messages.fetchDomainsFailed', { msg: response.msg || '' }),
            })
        }
    }).catch(error => {
        console.error(t('google.messages.apiError'), error)
        differList.value = []
    })
}

// 格式化时间
const formatTime = (timestamp) => {
    if (!timestamp) return '-'
    return new Date(timestamp * 1000).toLocaleString('zh-CN')
}
</script>


<style scoped>
/* 基础样式 */
.primary-label {
    color: var(--va-primary);
    font-size: 12px;
    font-weight: 600;
}

.filter-label {
    color: var(--va-primary);
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
}

.va-data-table__sortable-icon {
    opacity: 0.6;
}

.va-data-table__sortable-icon--active {
    opacity: 1;
}

/* 筛选表单布局 */
.filter-form {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

/* 筛选网格布局 */
.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 12px;
    margin-top: 10px;
}

.filter-item {
    display: flex;

    gap: 6px;
    margin-bottom: 8px;
}

.filter-item-header {
    display: flex;
    align-items: center;
}

.filter-input {
    width: 100%;

}

/* 日期选择器相关样式 */
.date-filter {
    grid-column: span 2;
}

.date-shortcuts {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 4px;
}

.date-shortcuts .va-button {
    font-size: 12px;
    padding: 4px 8px;
}

/* 操作按钮区域 */
.filter-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 10px;
    margin: 8px 0;
}

/* 操作按钮区域 */
.filter-actions1 {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    gap: 10px;
    margin: 8px 0;
}

:deep(.va-dropdown__content) {
    z-index: 10000 !important;
}

/* 响应式样式 */
@media (max-width: 991px) {
    .filter-toggle {
        display: flex;
    }

    .filter-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }

    .date-filter {
        grid-column: span 1;
    }
}

@media (min-width: 992px) {
    .filter-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }
}

:deep(.va-modal__inner) {
    min-height: 500px !important;
}
</style>
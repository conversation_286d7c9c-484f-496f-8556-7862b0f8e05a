import { ref, computed, watch } from 'vue'
import { defineVaDataTableColumns, useToast } from 'vuestic-ui'
import { list, advs, domains, affs, affInsights } from '@/api/ads/insights.js'
import { useUserStore } from '@/stores/user-store'
import { useI18n } from 'vue-i18n'

export function useInsights() {
  const userStore = useUserStore()
  const { init: toast } = useToast()
  const roleName = userStore.roleInfo.name
  const isDark = ref(userStore.theme === 'dark')
  const { t } = useI18n()

  // 状态管理
  const loading = ref(false)
  const total = ref(0)
  const list = ref([])
  const dateRange = ref(getPreviousDaysDateRange(7))
  const checkList = ref([t('insight.group.date')])
  const groupBy = ref(['stat_time'])
  const sortBy = ref('statTime')
  const sortingOrder = ref('descending')
  const settingsModalOpen = ref(false) // 设置弹窗是否打开

  const advList = ref([])
  const affList = ref([])
  const domainList = ref([])



  // 查询参数
  const queryParams = ref({
    pageNum: 1,
    pageSize: 20,
    groupBy: 'stat_time',
    countryName: undefined,
    appName: undefined,
    adsName: undefined,
    advIds: [],
    affIds: [],
    orderByColumn: 'stat_time', // 排序列名
    isAsc: 'descending'         // 排序方向
  })

  // 分组选项

  const groupByList = ref([
    { label: t('insight.group.date'), value: 'stat_time' },
    { label: t('insight.group.domain'), value: 'app_name' },
    { label: t('insight.group.channel'), value: 'aff_id' },
    { label: t('insight.group.country'), value: 'country_name' },
    { label: t('insight.group.adUnit'), value: 'ads_name' },
  ])

  // 表格列定义 - 所有可能的列
  let allColumnsConfig = [
    // 分组类别的列
    {
      key: 'statTimeStr',
      label: t('insight.table.date'),
      category: '分组列',
      visible: true,
      sortable: true,
      field: 'stat_time',
      groupName: 'dataName'
    },
    {
      key: 'appName',
      label: t('insight.table.domain'),
      category: '分组列',
      visible: false,
      sortable: true,
      field: 'app_name',
      groupName: 'appName'
    },
    {
      key: 'countryName',
      label: t('insight.table.country'),
      category: '分组列',
      visible: false,
      sortable: false,
      field: 'country_name',
      groupName: 'countryName'
    },
    // {
    //   key: 'advName',
    //   label: 'Advertiser',
    //   category: '分组列',
    //   visible: false,
    //   sortable: false,
    //   field: 'adv_id',
    //   groupName: 'advName'
    // },
    {
      key: 'affName',
      label: t('insight.table.channel'),
      category: '分组列',
      visible: false,
      sortable: true,
      field: 'aff_id',
      groupName: 'affName'
    },
    {
      key: 'adsName',
      label: t('insight.table.adUnit'),
      category: '分组列',
      visible: false,
      sortable: true,
      field: 'ads_name',
      groupName: 'adsName'
    },
    // {
    //   key: 'pubId',
    //   label: 'Publisher ID',
    //   category: '分组列',
    //   visible: false,
    //   sortable: true,
    //   field: 'pub_id',
    //   groupName: 'pubId'
    // },

    // 用户数据类别的列

    // 广告数据类别的列
    {
      key: 'adsRequest',
      label: t('insight.table.adRequest'),
      category: '广告数据',
      visible: true,
      sortable: true
    },
    {
      key: 'adsMatch',
      label: t('insight.table.adMatch'),
      category: '广告数据',
      visible: true,
      sortable: true
    },
    {
      key: 'matchRate',
      label: t('insight.table.matchRate'),
      category: '广告数据',
      visible: true,
      sortable: true
    },
    {
      key: 'adsImpression',
      label: t('insight.table.adImpression'),
      category: '广告数据',
      visible: true,
      sortable: true
    },
    {
      key: 'impressionRate',
      label: t('insight.table.impressionRate'),
      category: '广告数据',
      visible: true,
      sortable: true
    },
    {
      key: 'adsDisplay',
      label: t('insight.table.adDisplay'),
      category: '广告数据',
      visible: true,
      sortable: true
    },
    {
      key: 'activeViewRate',
      label: t('insight.table.activeViewRate'),
      category: '广告数据',
      visible: true,
      sortable: true
    },
    {
      key: 'adsClick',
      label: t('insight.table.adClick'),
      category: '广告数据',
      visible: true,
      sortable: true
    },
    {
      key: 'clickRate',
      label: t('insight.table.clickRate'),
      category: '广告数据',
      visible: true,
      sortable: true
    },

    // 收益数据类别的列
    {
      key: 'estimateRevenue',
      label: t('insight.table.estimatedRevenue'),
      category: '收益数据',
      visible: true,
      sortable: true
    },
    {
      key: 'ecpm',
      label: t('insight.table.ecpm'),
      category: '收益数据',
      visible: true,
      sortable: true
    },
    {
      key: 'ecpc',
      label: t('insight.table.ecpc'),
      category: '收益数据',
      visible: true,
      sortable: true
    }
  ]

  // 存储所有列的可见性设置
  const columnsVisibility = ref(
    allColumnsConfig.reduce((acc, column) => {
      acc[column.key] = column.visible
      return acc
    }, {})
  )

  // 临时存储设置弹窗中的列可见性状态（用于取消操作）
  const tempColumnsVisibility = ref({})

  // 表格列可见性控制
  const groupByMap = ref({
    dataName: true,
    appName: false,
    countryName: false,
    advName: false,
    affName: false,
    adsName: false,
    // pubId: false,

  })

  // 打开设置弹窗
  function openSettings() {
    // 保存当前可见性设置到临时变量，以便取消时恢复
    tempColumnsVisibility.value = { ...columnsVisibility.value }
    settingsModalOpen.value = true
  }

  // 关闭设置弹窗（取消更改）
  function closeSettings() {
    // 恢复为打开弹窗前的设置
    columnsVisibility.value = { ...tempColumnsVisibility.value }
    settingsModalOpen.value = false
  }

  // 应用设置变更
  function applySettings() {
    // 更新 groupByMap
    updateGroupByMapFromVisibility()

    // 更新 groupBy 和 checkList，使其与可见的分组列保持一致
    updateGroupByFromVisibility()

    // 检查排序字段是否在分组中，如果是则重置排序
    // checkSortFieldAndReset() 

    // 保存设置到本地存储
    saveColumnsSettingsToLocalStorage()

    // 关闭设置弹窗
    settingsModalOpen.value = false

    // 刷新数据
    getList()
  }

  // 检查排序字段是否需要重置
  function checkSortFieldAndReset() {
    // 获取当前前端排序字段对应的后端字段
    let currentBackendSortField = null;

    // 反向查找 - 通过前端排序字段找到对应的后端字段
    for (const [backendField, frontendField] of Object.entries(groupSortMap)) {
      if (frontendField === sortBy.value) {
        currentBackendSortField = backendField;
        break;
      }
    }

    // 如果当前排序字段在分组中，则重置排序
    if (currentBackendSortField && groupBy.value.includes(currentBackendSortField)) {
      queryParams.value.orderByColumn = 'stat_time'
      queryParams.value.isAsc = 'descending'

      // 同步更新前端排序状态
      sortBy.value = 'statTime'
      sortingOrder.value = 'descending'
    }
  }

  // 根据可见性设置更新 groupBy 数组和 checkList
  function updateGroupByFromVisibility() {
    // 找出所有可见的分组列
    const visibleGroupColumns = allColumnsConfig
      .filter(col => col.category === '分组列' && columnsVisibility.value[col.key] && col.field)

    // 更新 groupBy 值
    groupBy.value = visibleGroupColumns.map(col => col.field)

    // 更新 checkList 值
    checkList.value = visibleGroupColumns.map(col => {
      // 查找 groupByList 中与当前字段匹配的项
      const matchedItem = groupByList.value.find(item => item.value === col.field)
      return matchedItem ? matchedItem.label : col.label
    })

    // 如果没有选中任何分组列，至少默认选中日期
    if (groupBy.value.length === 0) {
      groupBy.value = ['stat_time']
      checkList.value = [t('insight.group.date')]

      // 也要更新对应列的可见性
      const dateColumn = allColumnsConfig.find(col => col.field === 'stat_time')
      if (dateColumn) {
        columnsVisibility.value[dateColumn.key] = true
      }
    }
  }

  // 切换列可见性
  function toggleColumnVisibility(columnKey, visible) {
    columnsVisibility.value[columnKey] = visible
  }

  // 从列可见性设置更新 groupByMap
  function updateGroupByMapFromVisibility() {
    // 找出所有分组列配置
    const groupColumns = allColumnsConfig.filter(col => col.category === '分组列' && col.groupName)

    // 更新 groupByMap
    groupColumns.forEach(col => {
      if (col.groupName) {
        groupByMap.value[col.groupName] = columnsVisibility.value[col.key]
      }
    })

    // 更新非分组列的可见性
    allColumnsConfig.filter(col => col.category !== '分组列' && col.groupName).forEach(col => {
      if (col.groupName) {
        groupByMap.value[col.groupName] = columnsVisibility.value[col.key]
      }
    })
  }

  // 动态生成表格列
  const columns = computed(() => {
    // 过滤出可见的列
    const visibleColumns = allColumnsConfig.filter(column => columnsVisibility.value[column.key])

    // 转换为 VaDataTable 所需的列格式
    return defineVaDataTableColumns(visibleColumns.map(column => ({
      key: column.key,
      label: column.label,
      sortable: column.sortable
    })))
  })

  // 获取按类别分组的所有列
  const columnsByCategory = computed(() => {
    // 创建一个对象，键为类别，值为该类别下的列数组
    const result = {}

    allColumnsConfig.forEach(column => {
      if (!result[column.category]) {
        result[column.category] = []
      }
      result[column.category].push(column)
    })

    return result
  })

  // 计算总页数
  const totalPages = computed(() => {
    return Math.ceil(total.value / queryParams.value.pageSize)
  })

  // 默认显示days天的数据
  function getPreviousDaysDateRange(days) {
    const today = new Date()
    const endDate = new Date(today)
    const startDate = new Date(today)
    startDate.setDate(startDate.getDate() - days)
    // @vuepic/vue-datepicker 的 range 模式需要数组格式 [startDate, endDate]
    return [startDate, endDate]
  }

  // 将日期对象转换为10位时间戳（秒级），并设置为当天的0:00
  function dateToTimestamp(date, isEndDate = false) {
    if (!date) return ''
    if (!(date instanceof Date)) {
      try {
        date = new Date(date)
      } catch (e) {
        console.error('日期转换错误:', e)
        return ''
      }
    }
    // 将时间设置为当天的0:00，如果是结束日期则设置为23:59:59
    const normalizedDate = new Date(date)
    if (isEndDate) {
      normalizedDate.setHours(23, 59, 59, 999)
    } else {
      normalizedDate.setHours(0, 0, 0, 0)
    }
    return Math.floor(normalizedDate.getTime() / 1000)
  }

  const sortByMap = {
    '日期': 'statTime',
    '国家': 'country_name',
    '广告位': 'adsName',
    '广告主': 'adv_id',
    '渠道': 'affName',
    '广告请求': 'ads_request',
    '广告匹配': 'ads_match',
    '广告展示': 'ads_display',
    '广告点击': 'ads_click',
    '广告点击率': 'click_rate',
    '域名': 'appName',
  }

  // 将日期范围添加到查询参数
  function addDateRange(params) {
    if (!dateRange.value || !Array.isArray(dateRange.value) || dateRange.value.length !== 2) {
      console.warn('日期范围不完整:', dateRange.value);
      return params
    }

    const startDate = dateToTimestamp(dateRange.value[0])
    const endDate = dateToTimestamp(dateRange.value[1], true) // 结束日期设置为当天的23:59:59
    return {
      ...params,
      startTime: startDate,
      endTime: endDate,
    }
  }

  // 处理复选框变化
  function handleCheckboxChange(label, checked) {
    if (checked) {
      if (!checkList.value.includes(label)) {
        checkList.value.push(label)
      }
    } else {
      // 如果取消选中，从 checkList 中移除
      const index = checkList.value.indexOf(label)
      if (sortByMap[label] === queryParams.value.orderByColumn) {
        queryParams.value.orderByColumn = 'stat_time'
        queryParams.value.isAsc = 'descending'
      }

      if (index > -1) {
        checkList.value.splice(index, 1)
      }
    }

    // 更新 groupBy 数组
    const resultList = checkList.value.map((item) => {
      // 查找 groupByList 中与当前 item 相匹配的 label
      const matchedItem = groupByList.value.find((obj) => obj.label === item)
      if (matchedItem) {
        // 如果找到匹配项，则返回对应的 value
        return matchedItem.value
      }
      return item
    })

    groupBy.value = resultList

    // 更新分组列的可见性
    updateGroupByColumnsVisibility()

    // 检查排序字段是否需要重置
    // checkSortFieldAndReset()

    // 更新 groupByMap 并重新获取数据
    updateGroupByMapAndFetchData()
  }

  // 更新分组列的可见性
  function updateGroupByColumnsVisibility() {
    // 先重置所有分组列可见性为 false
    allColumnsConfig
      .filter(col => col.category === '分组列')
      .forEach(col => {
        columnsVisibility.value[col.key] = false
      })
    // 根据当前选中的分组项，设置对应列的可见性为 true
    groupBy.value.forEach(groupByValue => {
      const column = allColumnsConfig.find(col => col.field === groupByValue)
      if (column && column.groupName) {
        columnsVisibility.value[column.key] = true
      }
    })
  }

  // 更新 groupByMap 并获取数据
  function updateGroupByMapAndFetchData() {
    const initialGroupByMap = {
      dataName: false,
      appName: false,
      countryName: false,
      advName: false,
      affName: false,
      adsName: false,
      // pubId: false,
      totalUser: false,
      pageViews: false,
      bounceRate: false,
    }

    const fieldNameMap = {
      dataName: 'stat_time',
      // appName: 'app_name',
      countryName: 'country_name',
      // advName: 'adv_id',
      affName: 'aff_id',
      adsName: 'ads_name',
      // pubId: 'pub_id',
    }

    const newGroupByMap = { ...initialGroupByMap }

    if (!groupBy.value || groupBy.value.length <= 0) {
      Object.keys(fieldNameMap).forEach((groupName) => {
        newGroupByMap[groupName] = false
      })
      groupByMap.value = { ...newGroupByMap }
      queryParams.value.pageNum = 1
      getList()
      return
    }

    if (groupBy.value.length > 0) {
      groupBy.value.forEach((groupByKey) => {
        Object.keys(fieldNameMap).forEach((groupName) => {
          if (fieldNameMap[groupName] === groupByKey) {
            newGroupByMap[groupName] = true
          }
        })
      })
    } else {
      Object.keys(fieldNameMap).forEach((groupName) => {
        newGroupByMap[groupName] = false
      })
    }

    groupByMap.value = { ...newGroupByMap }
    queryParams.value.pageNum = 1
    getList()
  }

  // 处理排序变化
  function handleSortChange(value) {
    if (sortingOrder.value === null) {
      return
    }
    // 获取当前前端排序字段对应的后端字段
    let backendSortField = sortBy.value;
    // 反向查找映射 - 通过前端字段找到对应的后端字段
    for (const [backend, frontend] of Object.entries(groupSortMap)) {
      if (frontend === sortBy.value) {
        backendSortField = backend;
        break;
      }
    }
    // 将 VaDataTable 的排序值转换为后端 API 所需的格式
    queryParams.value.orderByColumn = backendSortField == 'statTimeStr' ? 'stat_time' : backendSortField;
    queryParams.value.isAsc = value === 'asc' ? 'ascending' : 'descending'

    // 检查排序字段是否在分组中，如果是则重置排序
    // checkSortFieldAndReset();
    // 如果没有被重置，则获取数据
    getList()
  }


  // 计算数据
  function calculateEightyFivePercent(value, dateStr, digits, isDiver) {
    const userName = localStorage.getItem('userName')
    if (!value) return '0'

    const comparisonDate = new Date(`2025-05-18`)
    const inputDate = new Date(`2025-${dateStr}`)

    let factor
    if (inputDate < comparisonDate) {
      factor = 1
    } else {
      factor = 1
    }
    return (value * factor).toFixed(digits)
  }
  //  group 与 排序字段的映射
  const groupSortMap = {
    stat_time: 'statTime',
    // app_name: 'appName',
    country_name: 'countryName',
    // adv_id: 'advName',
    ads_name: 'adsName',
  }


  // 获取列表数据
  function getList() {
    loading.value = true

    // 剔除groupByList中不存在的value
    queryParams.value.groupBy = groupBy.value.filter((value) => groupByList.value.some((item) => item.value === value)).join(',')

    // 检查排序字段是否在分组中，如果是则重置排序
    // checkSortFieldAndReset()

    const advIds = queryParams.value.advIds
      ? queryParams.value.advIds.filter((id) => id !== null && id !== undefined && id !== '').join(',')
      : ''
    const affIds = queryParams.value.affIds
      ? queryParams.value.affIds.filter((id) => id !== null && id !== undefined && id !== '').join(',')
      : ''

    // 确保排序参数传递给 API
    const params = {
      ...queryParams.value,
      advIds,
      affIds,
      orderByColumn: queryParams.value.orderByColumn,
      isAsc: queryParams.value.isAsc
    }

    affInsights(addDateRange(params))
      .then((response) => {
        if (response.code === 200) {
          list.value = response.data.pageList[0] == null ? [] : response.data.pageList
          total.value = response.data.total || 0
        } else {
          list.value = []
          total.value = 0
          toast({
            color: 'danger',
            message: `获取数据失败: ${response.msg || '未知错误'}`,
          })
        }
      })
      .catch((error) => {
        console.error('API调用异常:', error)
        list.value = []
        total.value = 0
      })
      .finally(() => {
        loading.value = false
      })
  }

  // 获取广告主列表
  function getAdvs() {
    if (roleName != '广告渠道') {
      advs()
        .then((response) => {
          if (response.code === 200) {
            advList.value = response.data.pageList || []
          }
        })
        .catch((error) => {
          console.error('获取广告主列表失败:', error)
        })
    }
  }

  // 获取渠道列表
  function getAffs() {


    if (roleName != '广告渠道') {
      affs()
        .then((response) => {
          if (response.code === 200) {
            affList.value = response.data.pageList || []
          }
        })
        .catch((error) => {
          console.error('获取渠道列表失败:', error)
        })
    }

  }

  // 获取域名列表
  function getDomains() {
    domains().then((response) => {
      if (response.code === 200) {
        domainList.value = response.data.domainList || []
      }
    })
  }

  // 搜索按钮
  function handleQuery() {
    console.log('执行查询，当前日期范围:', dateRange.value);
    updateGroupByMapAndFetchData()
  }

  // 重置按钮
  function resetQuery() {
    queryParams.value = {
      pageNum: 1,
      pageSize: 0,
      countryName: undefined,
      appName: undefined,
      adsName: undefined,
      advIds: [],
      affIds: [],
      orderByColumn: 'stat_time',
      isAsc: 'descending'
    }

    dateRange.value = getPreviousDaysDateRange(7)
    // 重置排序状态
    sortBy.value = 'statTime'
    sortingOrder.value = 'descending'

    // 重置列可见性为默认值
    // allColumnsConfig.forEach(column => {
    //   columnsVisibility.value[column.key] = column.visible
    // })

    // 确保 groupByMap 与列可见性一致
    updateGroupByMapFromVisibility()
    // 确保分组列可见性与 groupBy 一致
    updateGroupByColumnsVisibility()

    handleQuery()
  }

  // 初始化
  function init() {
    getAdvs()
    getDomains()
    getAffs()
    if (roleName == '广告渠道') {
      groupByList.value = [
        { label: t('insight.group.date'), value: 'stat_time' },
        { label: t('insight.group.domain'), value: 'app_name' },
        { label: t('insight.group.adUnit'), value: 'ads_name' },
        { label: t('insight.group.country'), value: 'country_name' },
      ]


      allColumnsConfig = allColumnsConfig.filter((item) => item.field !== 'aff_id')
    }
    // 尝试从本地存储加载设置
    const hasLoadedSettings = loadColumnsSettingsFromLocalStorage()

    // 如果成功加载了设置，则更新 groupByMap
    if (hasLoadedSettings) {
      // 更新 groupByMap 以保持同步
      updateGroupByMapFromVisibility()
    } else {
      // 如果没有保存的设置，则确保默认的列可见性与 groupBy 一致
      updateGroupByColumnsVisibility()
    }
    // 获取数据
    getList()
  }

  // 保存列设置到本地存储
  function saveColumnsSettingsToLocalStorage() {
    try {
      // 构建要保存的数据对象
      const settingsToSave = {
        columnsVisibility: columnsVisibility.value,
        groupBy: groupBy.value,
        checkList: checkList.value,
        timestamp: new Date().getTime()
      }
      // 保存到 localStorage
      localStorage.setItem('InsightsColumnsSettings', JSON.stringify(settingsToSave))
    } catch (error) {
      console.error('保存列设置失败:', error)
    }
  }

  // 从本地存储加载列设置
  function loadColumnsSettingsFromLocalStorage() {
    try {
      // 从 localStorage 获取设置
      const savedSettings = localStorage.getItem('InsightsColumnsSettings')

      if (savedSettings) {
        const settings = JSON.parse(savedSettings)

        // 恢复列可见性设置
        if (settings.columnsVisibility) {
          // 合并保存的设置和当前设置（兼容可能新增的列）
          columnsVisibility.value = {
            ...columnsVisibility.value,
            ...settings.columnsVisibility
          }
        }

        // 恢复分组设置
        if (settings.groupBy && Array.isArray(settings.groupBy)) {
          groupBy.value = settings.groupBy
        }

        // 恢复复选框列表
        if (settings.checkList && Array.isArray(settings.checkList)) {
          checkList.value = settings.checkList
        }

        return true
      }

      return false
    } catch (error) {
      console.error('加载列设置失败:', error)
      return false
    }
  }

  return {
    // 状态
    loading,
    total,
    list,
    dateRange,
    checkList,
    groupBy,
    sortBy,
    sortingOrder,
    advList,
    affList,
    domainList,
    queryParams,
    isDark,
    groupByList,
    columns,
    totalPages,
    settingsModalOpen,
    columnsVisibility,
    columnsByCategory,
    allColumnsConfig,
    roleName,
    // 方法
    handleCheckboxChange,
    calculateEightyFivePercent,
    getList,
    handleQuery,
    resetQuery,
    init,
    openSettings,
    closeSettings,
    applySettings,
    toggleColumnVisibility,
    updateGroupByColumnsVisibility,
    updateGroupByFromVisibility,
    updateGroupByMapFromVisibility,
    saveColumnsSettingsToLocalStorage,
    loadColumnsSettingsFromLocalStorage,
    handleSortChange,
    checkSortFieldAndReset
  }
}

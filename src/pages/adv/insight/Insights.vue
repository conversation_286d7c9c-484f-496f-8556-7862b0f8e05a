<template>
    <div>


        <VaCard class="filter-card">
            <!-- 筛选区域标题和控制按钮 -->
            <div class="filter-header flex justify-between items-center  pb-2">
                <div class="flex items-center gap-2">
                    <VaIcon name="mso-filter_list" color="primary" />
                    <h2 class="text-lg font-medium">{{ t('insight.filter.title') }}</h2>
                </div>
                <div class="flex gap-2">
                    <!-- 仅在移动设备和平板上显示展开/折叠按钮 -->
                    <VaButton v-if="isMobileView" preset="secondary" border-color="primary" size="small"
                        class="filter-toggle" @click="toggleFilter"
                        :icon="isFilterExpanded ? 'mso-expand_less' : 'mso-expand_more'"
                        :aria-label="isFilterExpanded ? t('insight.filter.collapseFilter') : t('insight.filter.expandFilter')">
                        {{ isFilterExpanded ? t('insight.filter.collapse') : t('insight.filter.expand') }}
                    </VaButton>
                </div>
            </div>
            <!-- 筛选区域内容 - 使用JS动画 -->
            <div ref="filterContent" class="filter-content" :style="{
                ...getContentStyles(),
            }">
                <!-- 筛选表单 -->
                <div class="filter-form" v-show="isFilterExpanded">
                    <!-- 筛选条件网格 -->
                    <div class="filter-grid">
                        <!-- 日期范围筛选 -->
                        <div class="filter-item ">
                            <div class="filter-item-header">
                                <div class="flex items-center">
                                    <label class="filter-label">{{ t('insight.filter.dateRange') }}</label>
                                    <VaButtonDropdown preset="plain">
                                        <div class="flex items-center flex-col gap-2 date-shortcuts">
                                            <VaButton size="small" preset="secondary" border-color="primary"
                                                @click="handleLastThreeDays">{{ t('insight.buttons.lastThreeDays') }}</VaButton>
                                            <VaButton size="small" preset="secondary" border-color="primary"
                                                @click="handleLastWeek">{{ t('insight.buttons.lastWeek') }}</VaButton>
                                            <VaButton size="small" preset="secondary" border-color="primary"
                                                @click="handleLastMonth">{{ t('insight.buttons.lastMonth') }}</VaButton>
                                        </div>
                                    </VaButtonDropdown>
                                </div>
                            </div>
                            <Datepicker v-model="dateRange" range locale="en-US" format="yyyy/MM/dd"
                                :enable-time-picker="false" auto-apply :placeholder="t('insight.filter.dateRangePlaceholder')" week-numbers="iso"
                                week-num-name="We" @update:model-value="onDateRangeChange" now-button-label="Today"
                                :dark="isDark" :clearable="false" />
                        </div>
                        <!-- 域名筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">{{ t('insight.filter.domain') }}</label>
                            </div>
                            <VaSelect v-model="queryParams.appName" :placeholder="t('insight.filter.domainPlaceholder')" :options="domainList"
                                text-by="domain" value-by="domain" :clearable="true" searchable class="filter-input" />
                        </div>
                        <!-- 渠道筛选 -->
                        <div class="filter-item" v-if="roleName != '广告渠道'">
                            <div class="filter-item-header">
                                <label class="filter-label">{{ t('insight.filter.channel') }}</label>
                            </div>
                            <VaSelect v-model="queryParams.affIds" :placeholder="t('insight.filter.channelPlaceholder')" :options="affList" text-by="name"
                                value-by="id" :clearable="true" searchable multiple class="filter-input" />
                        </div>
                        <!-- 国家筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">{{ t('insight.filter.country') }}</label>
                            </div>
                            <VaInput v-model="queryParams.countryName" :placeholder="t('insight.filter.countryPlaceholder')" class="filter-input" />
                        </div>


                        <!-- 广告位筛选 -->
                        <div class="filter-item">
                            <div class="filter-item-header">
                                <label class="filter-label">{{ t('insight.filter.adUnit') }}</label>
                            </div>
                            <VaInput v-model="queryParams.adsName" :placeholder="t('insight.filter.adUnitPlaceholder')" class="filter-input" />
                        </div>


                    </div>

                    <!-- Group By 选择 -->
                    <div class="group-by-section mt-2 mb-2 flex justify-between items-center flex-wrap">
                        <div class="flex gap-2 items-center">
                            <label class="filter-label">{{ t('insight.filter.groupBy') }}</label>
                            <div class="group-by-container">
                                <VaCheckbox v-for="item in groupByList" :key="item.value"
                                    :model-value="checkList.includes(item.label)" :label="item.label"
                                    @update:model-value="(checked) => handleCheckboxChange(item.label, checked)"
                                    :disabled="item.label === t('insight.group.date')" class="group-by-checkbox" />
                            </div>
                        </div>
                        <div class="filter-actions">
                            <VaButton preset="secondary" border-color="primary" icon="mso-settings"
                                @click="openSettings">
                                {{ t('insight.buttons.settings') }}
                            </VaButton>
                            <VaButton preset="secondary" border-color="primary" icon="mso-refresh" @click="resetQuery">
                                {{ t('insight.buttons.reset') }}
                            </VaButton>
                            <VaButton color="primary" icon="mso-search" @click="handleQuery">
                                {{ t('insight.buttons.search') }}
                            </VaButton>
                        </div>
                    </div>

                    <!-- 操作按钮区域 -->

                </div>
            </div>
        </VaCard>

        <VaCard>
            <!-- 表格区域 -->
            <VaDataTable v-model:sort-by="sortBy" v-model:sorting-order="sortingOrder" :columns="columns" :items="list"
                :loading="loading" @update:sorting-order="handleSortChange" bordered hoverable>
                <!-- 数据列自定义渲染 -->

                <template #cell(appName)="{ rowData }">
                    <template v-if="rowData.appName">
                        {{ rowData.appName }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(countryName)="{ rowData }">
                    <template v-if="rowData.countryName">
                        {{ rowData.countryName }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(affName)="{ rowData }">
                    <template v-if="rowData.affName">
                        {{ rowData.affName }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(adsName)="{ rowData }">
                    <template v-if="rowData.adsName">
                        {{ rowData.adsName }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(adsRequest)="{ rowData }">
                    <template v-if="rowData.adsRequest !== undefined && rowData.adsRequest !== null">
                        {{ formatNumber(rowData.adsRequest) }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(adsMatch)="{ rowData }">
                    <template v-if="rowData.adsMatch !== undefined && rowData.adsMatch !== null">
                        {{ formatNumber(rowData.adsMatch) }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(matchRate)="{ rowData }">
                    <template v-if="rowData.matchRate !== undefined && rowData.matchRate !== null">
                        <span :style="shouldHighlight('matchRate', rowData.matchRate) ? getHighlightStyle('matchRate') : {}">
                            {{ rowData.matchRate + '%' }}
                        </span>
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <!-- 数据列自定义渲染 -->
                <template #cell(adsImpression)="{ rowData }">
                    <template v-if="rowData.adsImpression !== undefined && rowData.adsImpression !== null">
                        {{ formatNumber(rowData.adsImpression) }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(impressionRate)="{ rowData }">
                    <template v-if="rowData.impressionRate !== undefined && rowData.impressionRate !== null">
                        <span :style="shouldHighlight('impressionRate', rowData.impressionRate) ? getHighlightStyle('impressionRate') : {}">
                            {{ rowData.impressionRate + '%' }}
                        </span>
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(adsDisplay)="{ rowData }">
                    <template v-if="rowData.adsDisplay !== undefined && rowData.adsDisplay !== null">
                        {{ formatNumber(rowData.adsDisplay) }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(activeViewRate)="{ rowData }">
                    <template v-if="rowData.activeViewRate !== undefined && rowData.activeViewRate !== null">
                        <span :style="shouldHighlight('activeViewRate', rowData.activeViewRate) ? getHighlightStyle('activeViewRate') : {}">
                            {{ rowData.activeViewRate + '%' }}
                        </span>
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(adsClick)="{ rowData }">
                    <template v-if="rowData.adsClick !== undefined && rowData.adsClick !== null">
                        {{ formatNumber(rowData.adsClick) }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(clickRate)="{ rowData }">
                    <template v-if="rowData.clickRate !== undefined && rowData.clickRate !== null">
                        <span :style="shouldHighlight('clickRate', rowData.clickRate) ? getHighlightStyle('clickRate') : {}">
                            {{ rowData.clickRate + '%' }}
                        </span>
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(estimateRevenue)="{ rowData }">
                    <template v-if="rowData.estimateRevenue !== undefined && rowData.estimateRevenue !== null">
                        {{ '$' + formatNumber(rowData.estimateRevenue) }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(ecpm)="{ rowData }">
                    <template v-if="rowData.ecpm !== undefined && rowData.ecpm !== null">
                        {{ '$' + rowData.ecpm }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
                <template #cell(ecpc)="{ rowData }">
                    <template v-if="rowData.ecpc !== undefined && rowData.ecpc !== null">
                        {{ '$' + rowData.ecpc }}
                    </template>
                    <template v-else>
                        --
                    </template>
                </template>
            </VaDataTable>
            <!-- 分页 -->
            <div class="flex justify-end mt-4 gap-2 filter-actions1">
                <div>
                    <b>{{ total }} {{ t('insight.table.results') }}</b>
                    {{ t('insight.table.perPage') }}
                    <VaSelect v-model="queryParams.pageSize" class="!w-20 inline-block" :options="[10, 20, 50, 100]"
                        @update:model-value="getList" />
                </div>
                <VaPagination v-model="queryParams.pageNum" :pages="totalPages" :visible-pages="5"
                    buttons-preset="secondary" gapped border-color="primary" class="justify-center sm:justify-start"
                    @update:modelValue="getList" />
            </div>
        </VaCard>
        <!-- 列设置弹窗 -->
        <VaModal v-model="settingsModalOpen" :title="t('insight.modal.tableColumnSettings')" size="medium" hide-default-actions close-button
            mobile-fullscreen>
            <h3 class="text-lg font-semibold mb-4">{{ t('insight.modal.selectColumns') }}</h3>
            <!-- 按类别分组展示所有列 -->
            <div v-for="(columns, category) in columnsByCategory" :key="category" class="mb-6">
                <h4 class="font-semibold mb-2 text-primary">{{ getCategoryTranslation(category) }}</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    <VaCheckbox v-for="column in columns" :key="column.key" :label="column.label"
                        :model-value="columnsVisibility[column.key]"
                        @update:model-value="(checked) => toggleColumnVisibility(column.key, checked)"
                        :disabled="column.groupName === 'dataName'" />
                </div>
            </div>
            <!-- 操作按钮 -->
            <div class="flex justify-end mt-6 gap-4">
                <VaButton preset="secondary" @click="closeSettings" border-color="primary">{{ t('insight.buttons.cancel') }}</VaButton>
                <VaButton preset="secondary" border-color="primary" @click="resetToDefault">{{ t('insight.buttons.default') }}</VaButton>
                <VaButton color="primary" @click="applySettings">{{ t('insight.buttons.apply') }}</VaButton>
            </div>
        </VaModal>
    </div>
</template>

<script setup>
import { onMounted, onUnmounted, watch } from 'vue'
import { useInsights } from './useInsights.js'
import { useData } from '@/composables/useData.js'
import { useTransition } from '@/composables/useTransition.js'
import { useAdHighlight } from '@/composables/useAdHighlight.js'
import Datepicker from '@vuepic/vue-datepicker'
import { useI18n } from 'vue-i18n'

// 使用 i18n
const { t } = useI18n()

// 使用广告高亮Hook
const { shouldHighlight, getHighlightStyle } = useAdHighlight()

// 使用过渡动画功能
const {
    isExpanded: isFilterExpanded,
    contentRef: filterContent,
    isMobileView,
    toggle: toggleFilter,
    getContentStyles,
    init: initTransition,
    cleanup: cleanupTransition
} = useTransition({
    defaultExpanded: true,  // PC端默认展开
    breakpoint: 992,        // 小于992px为移动设备
    animationDuration: 300  // 动画持续时间
})

// 从useInsights获取所有状态和方法
const {
    // 状态
    loading,
    list,
    dateRange,  // 使用useInsights中的dateRange
    checkList,
    sortBy,
    sortingOrder,
    advList,
    affList,
    domainList,
    isDark,
    queryParams,
    groupByList,
    columns,
    total,
    totalPages,
    settingsModalOpen,
    columnsVisibility,
    columnsByCategory,
    allColumnsConfig,
    roleName,
    // 方法
    handleCheckboxChange,
    getList,
    handleQuery,
    resetQuery,
    init,
    openSettings,
    closeSettings,
    applySettings,
    toggleColumnVisibility,
    handleSortChange
} = useInsights()

// 使用useData获取日期相关的工具函数，但不使用其dateRange状态
const {
    selectLastThreeDays,
    selectLastWeek,
    selectLastMonth,
    onDateRangeChange: handleDateRangeChange,
    formatDate
} = useData({
    defaultDateRange: 'none'  // 不自动设置默认日期范围，使用useInsights中的
})

// 将数字格式化为千分位展示
const formatNumber = (value) => {
    if (value === undefined || value === null) {
        return '--'
    }
    return value.toLocaleString()
}

// 处理日期范围变化
const onDateRangeChange = (value) => {
    if (value && value.length === 2) {
        dateRange.value = value  // 更新useInsights中的dateRange
        handleQuery()  // 执行查询
    }
}

// 日期快捷选择处理函数
const handleLastThreeDays = () => {
    const today = new Date()
    const pastDate = new Date()
    pastDate.setDate(today.getDate() - 3)
    dateRange.value = [pastDate, today]
    handleQuery()
}

const handleLastWeek = () => {
    const today = new Date()
    const pastDate = new Date()
    pastDate.setDate(today.getDate() - 6)
    dateRange.value = [pastDate, today]
    handleQuery()
}

const handleLastMonth = () => {
    const today = new Date()
    const pastDate = new Date()
    pastDate.setDate(today.getDate() - 29)
    dateRange.value = [pastDate, today]
    handleQuery()
}

// 重置为默认设置
const resetToDefault = () => {
    allColumnsConfig.forEach(column => {
        toggleColumnVisibility(column.key, column.visible)
    })
}

// 获取分类的翻译
const getCategoryTranslation = (category) => {
    const categoryMap = {
        '分组列': 'insight.modal.categoryBasic',
        '用户数据': 'insight.modal.categoryUser',
        '广告数据': 'insight.modal.categoryAd',
        '收益数据': 'insight.modal.categoryRevenue'
    }
    return t(categoryMap[category] || category)
}

// 监听dateRange变化
watch(dateRange, (newValue) => {
    console.log('日期范围变化:', newValue)
}, { deep: true })

onMounted(() => {
    init()
    initTransition() // 初始化过渡动画相关逻辑
})

// 移除事件监听
onUnmounted(() => {
    cleanupTransition() // 清理过渡动画相关事件监听
})
</script>

<style scoped>
/* 基础样式 */
.primary-label {
    color: var(--va-primary);
    font-size: 12px;
    font-weight: 600;
}

.filter-label {
    color: var(--va-primary);
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
}

.va-data-table__sortable-icon {
    opacity: 0.6;
}

.va-data-table__sortable-icon--active {
    opacity: 1;
}

/* 筛选表单布局 */
.filter-form {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

/* 筛选网格布局 */
.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 12px;
    margin-top: 10px;
}

.filter-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-bottom: 8px;
}

.filter-item-header {
    display: flex;
    align-items: center;
}

.filter-input {
    width: 100%;
}

/* 日期选择器相关样式 */
.date-filter {
    grid-column: span 2;
}

.date-shortcuts {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 4px;
}

.date-shortcuts .va-button {
    font-size: 12px;
    padding: 4px 8px;
}

.group-by-container {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;
}

.group-by-checkbox {
    margin: 0 !important;
}

/* 操作按钮区域 */
.filter-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 10px;
    margin: 8px 0;
}

.filter-actions1 {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    gap: 10px;
    margin: 8px 0;
}

/* 响应式样式 */
@media (max-width: 991px) {
    .filter-toggle {
        display: flex;
    }

    .filter-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 2fr));
    }

    .date-filter {
        grid-column: span 1;
    }

    .group-by-container {
        justify-content: flex-start;
    }
}

@media (min-width: 992px) {
    .filter-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }
}
</style>
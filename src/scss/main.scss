@import 'tailwind';
@import 'vuestic';
@import 'icon-fonts/index';

/* fallback */
@font-face {
  font-family: 'Material Symbols Outlined';
  font-style: normal;
  font-weight: 100 700;
  src: url(/fonts/material-symbols.woff2) format('woff2');
  font-display: block;
}

@font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 400;
  src: url(/fonts/material-icons.woff2) format('woff2');
  font-display: block;
}

/* 导入PingFang SC字体 */
// @font-face {
//   font-family: 'PingFang SC';
//   src: url('/fonts/PingFangSC-Regular.ttf') format('truetype');
//   font-weight: 400;
//   font-style: normal;
//   font-display: swap;
// }

@font-face {
  font-family: 'PingFang SC';
  src: url('/fonts/PingFangSC-Medium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'PingFang SC';
  src: url('/fonts/PingFangSC-Semibold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'PingFang SC';
  src: url('/fonts/PingFangSC-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}


.material-symbols-outlined {
  font-family: 'Material Symbols Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
}

body {
  @apply text-regularMedium;

  // TODO Move to some other place so that it's more elegant.
  --va-checkbox-font-size: 0.875rem;
  --va-card-box-shadow: none; // TODO Remove after https://github.com/epicmaxco/vuestic-ui/issues/3964
  --va-card-padding: 1rem;
  --va-font-family: 'PingFang SC', sans-serif;
}

code,
kbd,
samp,
pre {
  font-family: monospace;
  font-size: 1em;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.page-title {
  @apply text-[32px] md:text-5xl font-bold leading-9 md:leading-[56px] max-sm:mt-6 mb-6 md:mb-4;
}

.h1 {
  @apply text-[32px] md:text-5xl font-bold leading-9 md:leading-[56px] max-sm:mt-6 mb-6 md:mb-4;
}

.h3,
.page-sub-title {
  @apply text-2xl font-bold leading-[30px];
}

.h5 {
  @apply font-bold leading-tight;
}

.block-title {
  @apply text-2xl font-bold mb-2;
}

.pricing-plan-card-title {
  @apply text-[28px] md:text-[32px] leading-10 font-bold;
}

.text-regular-small {
  font-size: 0.8125rem;
  line-height: 1rem;

}

/* 全局样式，应用到所有VaDataTable */
.va-data-table {
  border: 1px solid var(--va-background-border) !important;
}

.va-data-table th,
.va-data-table td {
  border: 1px solid var(--va-background-border) !important;
}

.primary-label {
  color: var(--va-primary);
  font-size: 12px;
  font-weight: 600;
}

.va-input-label {
  text-transform: capitalize;
}

:root {
  --primary-text: var(--va-primary-text);
  --dp-font-family: 'PingFang SC', sans-serif;
  --secondary-text: var(--va-secondary-text);
  --accent-color: var(--va-primary);
  --border-color: rgba(0, 0, 0, 0.05);
  --card-bg: var(--va-background-secondary);
  --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  --hover-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.va-file-upload {
  margin: 0px !important;
}

.va-data-table .va-data-table__table .va-data-table__table-th {
  font-size: 14px;
  font-weight: bold;
  color: var(--va-primary);
  text-transform: none;
}

/* 深色主题样式 */
.dp__theme_dark {
  /* 背景和文本 */
  --dp-background-color: #111827 !important;
  /* vuestic 深色主题背景色 */
  --dp-text-color: #ffffff !important;
  --dp-hover-color: #1e293b !important;
  --dp-hover-text-color: #ffffff !important;
  --dp-hover-icon-color: #93a3c0 !important;
  --dp-primary-color: var(--va-primary) !important;
  --dp-primary-disabled-color: rgba(var(--va-primary-rgb), 0.5) !important;
  --dp-primary-text-color: #ffffff !important;
  --dp-secondary-color: #a9a9a9 !important;

  /* 边框 */
  --dp-border-color: #1e293b !important;
  --dp-menu-border-color: #1e293b !important;
  --dp-border-color-hover: #2d3748 !important;
  --dp-border-color-focus: var(--va-primary) !important;

  /* 禁用状态 */
  --dp-disabled-color: #374151 !important;
  --dp-disabled-color-text: #6b7280 !important;

  /* 滚动条 */
  --dp-scroll-bar-background: #111827 !important;
  --dp-scroll-bar-color: #2d3748 !important;

  /* 状态颜色 */
  --dp-success-color: var(--va-success) !important;
  --dp-success-color-disabled: rgba(var(--va-success-rgb), 0.5) !important;
  --dp-icon-color: #93a3c0 !important;
  --dp-danger-color: var(--va-danger) !important;
  --dp-marker-color: var(--va-danger) !important;

  /* 其他 */
  --dp-tooltip-color: #111827 !important;
  --dp-highlight-color: rgba(var(--va-primary-rgb), 0.2) !important;
  --dp-range-between-dates-background-color: rgba(var(--va-primary-rgb), 0.2) !important;
  --dp-range-between-dates-text-color: #ffffff !important;
  --dp-range-between-border-color: rgba(var(--va-primary-rgb), 0.2) !important;
}


/* 浅色主题样式 */
.dp__theme_light {
  /* 背景和文本 */
  --dp-background-color: #ffffff !important;
  /* vuestic 浅色主题背景色 */
  --dp-text-color: #212121 !important;
  --dp-hover-color: #F7F9F9 !important;
  /* vuestic 浅色主题卡片背景色 */
  --dp-hover-text-color: #212121 !important;
  --dp-hover-icon-color: #6b7280;
  --dp-primary-color: var(--va-primary) !important;
  --dp-primary-disabled-color: rgba(var(--va-primary-rgb), 0.5);
  --dp-primary-text-color: #ffffff !important;
  --dp-secondary-color: #c0c4cc !important;

  /* 边框 */
  --dp-border-color: #e5e7eb !important;
  --dp-menu-border-color: #e5e7eb !important;
  --dp-border-color-hover: #d1d5db !important;
  --dp-border-color-focus: var(--va-primary) !important;

  /* 禁用状态 */
  --dp-disabled-color: #f3f4f6 !important;
  --dp-disabled-color-text: #9ca3af !important;

  /* 滚动条 */
  --dp-scroll-bar-background: #f3f4f6 !important;
  --dp-scroll-bar-color: #d1d5db !important;

  /* 状态颜色 */
  --dp-success-color: var(--va-success) !important;
  --dp-success-color-disabled: rgba(var(--va-success-rgb), 0.5) !important;
  --dp-icon-color: #6b7280 !important;
  --dp-danger-color: var(--va-danger) !important;
  --dp-marker-color: var(--va-danger) !important;

  /* 其他 */
  --dp-tooltip-color: #ffffff !important;
  --dp-highlight-color: rgba(var(--va-primary-rgb), 0.1) !important;
  --dp-range-between-dates-background-color: rgba(var(--va-primary-rgb), 0.1) !important;
  --dp-range-between-dates-text-color: #212121 !important;
  --dp-range-between-border-color: rgba(var(--va-primary-rgb), 0.1) !important;
}

/* 日期选择器全局样式 */
.dp__main {
  font-family: var(--dp-font-family, var(--va-font-family));
}

.dp__input {
  font-family: var(--dp-font-family, var(--va-font-family));
  border-radius: 4px;
  transition: all 0.2s ease;
  color: inherit;
}

.dp__input:hover {
  border-color: var(--va-primary) !important;
}

.dp__input:focus {
  border-color: var(--va-primary) !important;
  box-shadow: 0 0 0 2px rgba(var(--va-primary-rgb), 0.2);
}

.dp__action_button {
  color: var(--va-primary);
}

.dp__action_select {
  color: var(--va-primary);
}

.dp__selection_preview {
  font-family: var(--dp-font-family, var(--va-font-family));
}

/* 日期选择器动画 */
.dp__overlay_container {
  animation: dp-fade-in 0.2s ease-out;
}

@keyframes dp-fade-in {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 日期选择器根变量 */
:root {
  --dp-font-family: 'PingFang SC', sans-serif !important;
  --dp-border-radius: 4px !important;
  --dp-cell-border-radius: 8px !important;
  --dp-common-transition: all 0.2s ease !important;
  --dp-button-height: 35px !important;
  --dp-month-year-row-height: 35px !important;
  --dp-month-year-row-button-size: 35px !important;
  --dp-cell-size: 35px !important;
  --dp-cell-padding: 5px !important;
  --dp-common-padding: 10px !important;
  --dp-menu-min-width: 260px !important;
  --dp-animation-duration: 0.2s;
  --dp-font-size: 14px !important;
  --dp-transition-timing: ease-out;
}

.dp__range_end,
.dp__range_start,
.dp__active_date {
  border-radius: var(--dp-border-radius) !important;
}

.va-pagination {
  .va-pagination__item {
    border-radius: 4px;
  }


}
<template>
  <VaNavbar class="app-layout-navbar py-2 px-0 container" :class="{ 'with-shadow': isScrolled }">
    <template #left>
      <div class="left">
        <Transition v-if="isMobile" name="icon-fade" mode="out-in">
          <VaIcon color="primary" :name="isSidebarMinimized ? 'menu' : 'close'" size="24px" style="margin-top: 3px"
            @click="isSidebarMinimized = !isSidebarMinimized" />
        </Transition>
        <RouterLink to="/dashboard" aria-label="Visit home page">
          <div class="logo-container max-w-[154px]">
            <VuesticLogo />
          </div>
        </RouterLink>
      </div>
    </template>
    <template #right>
      <AppNavbarActions class="app-navbar__actions" :is-mobile="isMobile" />
    </template>
  </VaNavbar>
</template>

<script setup>
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '../../stores/global-store.js'
import AppNavbarActions from './components/AppNavbarActions.vue'
import VuesticLogo from '../VuesticLogo.vue'
import { ref, onMounted, onUnmounted } from 'vue'

defineProps({
  isMobile: { type: Boolean, default: false },
})

const GlobalStore = useGlobalStore()

const { isSidebarMinimized } = storeToRefs(GlobalStore)

// 添加滚动状态变量
const isScrolled = ref(false)

// 处理滚动事件
const handleScroll = () => {
  isScrolled.value = window.scrollY > 10
}

// 组件挂载时添加滚动监听
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  // 初始检查
  handleScroll()
})

// 组件卸载时移除滚动监听
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style lang="scss" scoped>
.va-navbar {
  z-index: 2;
  transition: box-shadow 0.3s ease;

  &.with-shadow {
    box-shadow: 0 2px 1px rgba(0, 0, 0, 0.1);
  }

  @media screen and (max-width: 950px) {
    .left {
      width: 100%;
    }

    .app-navbar__actions {
      display: flex;
      justify-content: space-between;
    }
  }
}

.left {
  display: flex;
  align-items: center;
  margin-left: 1rem;

  &>* {
    margin-right: 1rem;
  }

  &>*:last-child {
    margin-right: 0;
  }
}

.icon-fade-enter-active,
.icon-fade-leave-active {
  transition: transform 0.5s ease;
}

.icon-fade-enter,
.icon-fade-leave-to {
  transform: scale(0.5);
}
</style>

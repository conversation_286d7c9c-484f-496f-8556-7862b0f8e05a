<template>
  <VaButton preset="secondary" color="textPrimary" @click="gotoMessage">
    <VaBadge overlap v-if="unreadCount > 0">
      <template #text>{{ unreadCount ? unreadCount : 0 }}</template>
      <VaIconNotification class="notification-dropdown__icon" />
    </VaBadge>
    <VaIconNotification class="notification-dropdown__icon" v-else/>
  </VaButton>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import VaIconNotification from '../../../icons/VaIconNotification.vue'
import { getMessageData, updateMessageStatus } from '@/api/message/message'
import { useRouter } from 'vue-router'
import notification from '@/utils/notification'
import { useI18n } from 'vue-i18n' // 导入 i18n

// 使用 i18n
const { t } = useI18n()

const router = useRouter()
const gotoMessage = () => {
  router.push('/message')
}

// 存储消息数据
const messageData = ref({
  total: 0,
  unread: 0,
  read: 0,
  undelivered: 0
})


// 轮询间隔时间（毫秒）
const POLLING_INTERVAL = 10000 // 10秒  
let pollingInterval = null

// 获取消息数据
const fetchMessageData = async () => {
  try {
    const response = await getMessageData()
    if (response && response.code === 200 && response.data) {
      messageData.value = response.data
      // 检查是否有新消息
      if ( response.data.undelivered > 0) {
        // 有新消息，获取最新的未读消息
        fetchLatestUnreadMessage()
      }
    }
  } catch (error) {
    console.error(t('errors.fetch_message_failed'), error)
  }
}

// 获取最新的未读消息并显示通知
const fetchLatestUnreadMessage = async () => {
  try {
    // const notificationInstance = notification.info({
    //   title: t('notifications.title'),
    //   message: t('notifications.unread_messages', { count: messageData.value.undelivered ? messageData.value.undelivered : 0 }),
    //   autoClose: false,
    //   onClick: () => {
    //     // 点击通知时跳转到消息详情并关闭通知
    //     notificationInstance.close()
    //     router.push({
    //       path: '/message',
    //     })
    //   }
    // })
    // 修改所有消息状态未已读
    await updateMessageStatusFn();
  } catch (error) {
    console.error(t('errors.fetch_unread_failed'), error)
  }
}

const updateMessageStatusFn = async (id) => {
  try {
    await updateMessageStatus({
      isAll: true,
      status: 0,
    })
  } catch (error) {
    console.error(t('errors.update_status_failed'), error)
  }
}


// 格式化显示的未送达消息数量
const unreadCount = computed(() => {
  const count = messageData.value.unread || 0
  return count > 99 ? '99+' : count > 0 ? count.toString() : ''
})

// 开始轮询
const startPolling = () => {
  // 立即获取一次数据
  fetchMessageData()
  // 设置定时获取
  pollingInterval = setInterval(fetchMessageData, POLLING_INTERVAL)
}

// 停止轮询
const stopPolling = () => {
  if (pollingInterval) {
    clearInterval(pollingInterval)
    pollingInterval = null
  }
}

// 组件挂载时开始轮询
onMounted(() => {
  startPolling()
})

// 组件卸载时停止轮询
onUnmounted(() => {
  stopPolling()
})
</script>

<style lang="scss" scoped>
.notification-dropdown {
  cursor: pointer;

  .notification-dropdown__icon {
    position: relative;
    display: flex;
    align-items: center;
  }

  .va-dropdown__anchor {
    display: inline-block;
  }
}
</style>

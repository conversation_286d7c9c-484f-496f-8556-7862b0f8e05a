<template>
  <div class="profile-dropdown-wrapper">
    <VaDropdown v-model="isShown" :offset="[9, 0]" class="profile-dropdown" stick-to-edges>
      <template #anchor>
        <VaButton preset="secondary" color="textPrimary">
          <span class="profile-dropdown__anchor min-w-max">
            <slot />
            <div class="user-info">
              <div class="user-avatar-wrapper">
                <div class="user-avatar">{{ userStore.userName.charAt(0) }}</div>
                <VaBadge overlap class="notification-badge" v-if="unreadCount && isPermission">
                  <template #text>{{ unreadCount }}</template>
                </VaBadge>
              </div>
              <!-- <div>
                <div style="font-weight: 600; font-size: 17px;text-align: left;">{{ userStore.userName }}</div>
                <div style="font-size: 14px; color: var(--secondary-text);">{{ userStore.roleInfo.name }}</div>
              </div> -->
            </div>
          </span>
        </VaButton>
      </template>
      <VaDropdownContent class="profile-dropdown__content md:w-60 px-0 py-4 w-full"
        :style="{ '--hover-color': hoverColor }">
        <VaList v-for="group in profileOptions" :key="group.name">
          <header v-if="group.name" class="uppercase text-[var(--va-secondary)] opacity-80 font-bold text-xs px-4">
            {{ t(`user.${group.name}`) }}
          </header>
          <VaListItem v-for="item in group.list" :key="item.name" class="menu-item px-4 text-base cursor-pointer"
            :class="{ 'h-8': !item.customContent || (item.customContent && item.contentType !== 'theme' && item.contentType !== 'language') }"
            v-bind="resolveLinkAttribute(item)"
            @click="item.customContent && (item.contentType === 'theme' || item.contentType === 'language') ? null : (item.click ? item.click() : null)">
            <template v-if="item.customContent && item.name === 'notifications'">
              <div class="notification-item">
                <VaIcon :name="item.icon" class="pr-1" color="secondary" />
                <span>{{ t(`user.${item.name}`) }}</span>
                <VaBadge class="ml-2" v-if="unreadCount">
                  <template #text>{{ unreadCount }}</template>
                </VaBadge>
              </div>
            </template>
            <template v-else-if="item.customContent && item.contentType === 'theme'">
              <div class="setting-item flex justify-between">
                <div class="setting-item-header">
                  <VaIcon :name="item.icon" class="pr-1" color="secondary" />
                  <div class="setting-item-content">
                    <div class="switch-wrapper">
                      <VaSwitch v-model="isDarkTheme" color="primary" size="small" @change="toggleTheme" @click.stop>
                        <template #innerLabel>
                          <div class="va-text-center">
                            <VaIcon :name="isDarkTheme ? 'dark_mode' : 'light_mode'" />
                          </div>
                        </template>
                      </VaSwitch>
               
                    </div>
                  </div>
                </div>
             
              </div>
            </template>
            <template v-else-if="item.customContent && item.contentType === 'language'">
              <div class="setting-item flex justify-between">
                <div class="setting-item-header">
                  <VaIcon :name="item.icon" class="pr-1" color="secondary" />
                  <div class="setting-item-content">
                  <div class="switch-wrapper">

                    <VaSwitch v-model="isEnglishLanguage" color="primary" size="small"  @change="toggleLanguage"
                      @click.stop >
                      <template #innerLabel>
                        <div class="va-text-center">
                          <span>{{ isEnglishLanguage ? 'EN' : 'CN' }}</span>
                        </div>
                      </template>
                    </VaSwitch>

                  </div>
                </div>
                </div>
            
              </div>
            </template>
            <template v-else>
              <VaIcon :name="item.icon" class="pr-1" color="secondary" />
              {{ t(`user.${item.name}`) }}
            </template>
          </VaListItem>
          <VaListSeparator v-if="group.separator" class="mx-3 my-2" />
        </VaList>
      </VaDropdownContent>
    </VaDropdown>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useColors } from 'vuestic-ui'
import { useModal } from 'vuestic-ui'
import { useRouter } from 'vue-router'
import { Loading } from '../../../../utils'
import { useUserStore } from '../../../../stores/user-store'
import VaIconNotification from '../../../icons/VaIconNotification.vue'
import { getMessageData, updateMessageStatus } from '@/api/message/message'
import notification from '@/utils/notification'

const router = useRouter()
const { colors, setHSLAColor } = useColors()
const hoverColor = computed(() => setHSLAColor(colors.focus, { a: 0.1 }))
const { t } = useI18n()
const { confirm } = useModal()
const userStore = useUserStore()
const isPermission = ref(false)

// 主题相关
const { applyPreset, currentPresetName } = useColors()
const isDarkTheme = computed(() => currentPresetName.value === 'dark')

// 切换主题
const toggleTheme = () => {
  const newTheme = isDarkTheme.value ? 'light' : 'dark'
  applyPreset(newTheme)
  userStore.setTheme(newTheme)
}

// 语言相关
const { locale } = useI18n()
const isEnglishLanguage = computed(() => locale.value === 'gb')

// 切换语言
const toggleLanguage = () => {
  const newLang = isEnglishLanguage.value ? 'cn' : 'gb'
  userStore.setLanguage(newLang)
  locale.value = newLang
}

// 存储消息数据
const messageData = ref({
  total: 0,
  unread: 0,
  read: 0,
  undelivered: 0
})

// 轮询间隔时间（毫秒）
const POLLING_INTERVAL = 10000 // 10秒  
let pollingInterval = null

// 获取消息数据
const fetchMessageData = async () => {
  try {
    const response = await getMessageData()
    if (response && response.code === 200 && response.data) {
      messageData.value = response.data
      // 检查是否有新消息
      if (response.data.undelivered > 0) {
        // 有新消息，获取最新的未读消息
        fetchLatestUnreadMessage()
      }
    }
  } catch (error) {
    console.error(t('errors.fetch_message_failed'), error)
  }
}

// 获取最新的未读消息并显示通知
const fetchLatestUnreadMessage = async () => {
  try {
    await updateMessageStatusFn();
  } catch (error) {
    console.error(t('errors.fetch_unread_failed'), error)
  }
}

const updateMessageStatusFn = async () => {
  try {
    await updateMessageStatus({
      isAll: true,
      status: 0,
    })
  } catch (error) {
    console.error(t('errors.update_status_failed'), error)
  }
}

// 格式化显示的未读消息数量
const unreadCount = computed(() => {
  const count = messageData.value.unread || 0
  return count > 99 ? '99+' : count > 0 ? count.toString() : ''
})

// 开始轮询
const startPolling = () => {
  // 立即获取一次数据
  fetchMessageData()
  // 设置定时获取
  pollingInterval = setInterval(fetchMessageData, POLLING_INTERVAL)
}

// 停止轮询
const stopPolling = () => {
  if (pollingInterval) {
    clearInterval(pollingInterval)
    pollingInterval = null
  }
}

// 跳转到消息页面
const gotoMessage = () => {
  router.push('/message')
  isShown.value = false
}

// 退出登录
const logoutFn = async () => {
  confirm({ size: 'small', title: t('user.common.title'), message: t('user.common.message'), maxWidth: '400px', okText: t('user.common.okText'), cancelText: t('user.common.cancelText') }).then(async (ok) => {
    if (ok) {
      try {
        Loading.service()
        await userStore.logout()
        router.push('/auth/login')
      } catch (error) {

        Loading.close()
      } finally {
        Loading.close()
      }
    }
  })
}


const props = defineProps({
  options: Object,
})

const profileOptions = computed(() => {
  if (isPermission.value) {
    return (

      props.options || [
        {
          name: '',
          separator: true,
          list: [
            {
              name: 'notifications',
              icon: 'mso-notifications',
              click: gotoMessage,
              customContent: true,
            },
            {
              name: 'profile',
              to: 'preferences',
              icon: 'mso-account_circle',
            },
            {
              name: 'theme',
              icon: 'mso-brightness_medium',
              customContent: true,
              contentType: 'theme',
            },
            {
              name: 'language',
              icon: 'mso-language',
              customContent: true,
              contentType: 'language',
            },
          ],
        },
  
        {
          name: '',
          separator: false,
          list: [
            {
              name: 'logout',
              to: 'login',
              icon: 'mso-logout',
              click: logoutFn,
            },
          ],
        },
      ]
    )
  } else {
    return (

      props.options || [
        {
          name: '',
          separator: true,
          list: [
            {
              name: 'profile',
              to: 'preferences',
              icon: 'mso-account_circle',
            },
            {
              name: 'theme',
              icon: 'mso-brightness_medium',
              customContent: true,
              contentType: 'theme',
            },
            {
              name: 'language',
              icon: 'mso-language',
              customContent: true,
              contentType: 'language',
            },
          ],
        },
        {
          name: '',
          separator: false,
          list: [
            {
              name: 'logout',
              to: 'login',
              icon: 'mso-logout',
              click: logoutFn,
            },
          ],
        },
      ]
    )
  }
})
const isShown = ref(false)
const resolveLinkAttribute = (item) => {
  return item.to ? { to: { name: item.to } } : item.href ? { href: item.href, target: '_blank' } : {}
}

// 组件挂载时开始轮询
onMounted(() => {
  isPermission.value = userStore.roleInfo.name != '广告渠道'
  if (isPermission.value) {
    startPolling()
  }
})

// 组件卸载时停止轮询
onUnmounted(() => {
  stopPolling()
})
</script>

<style lang="scss">
.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-avatar-wrapper {
  position: relative;
}

.user-avatar {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--va-primary) 0%, #FAD0C4 100%);
  display: flex;
  text-transform: uppercase;
  align-items: center;
  text-transform: uppercase;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 18px;
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
}

.notification-item {
  display: flex;
  align-items: center;
  width: 100%;
}
.menu-item{
  margin-bottom: 5px;
}
.notification-icon {
  margin-right: 8px;
}

.setting-item {
  width: 90%;
}

.setting-item-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}


.profile-dropdown-wrapper{
  overflow-x: hidden;
}


.switch-label {
  font-size: 0.85rem;
  color: var(--va-text-secondary);
}

.profile-dropdown {
  cursor: pointer;

  &__content {
    .menu-item:hover {
      background: var(--hover-color);
    }
  }

  &__anchor {
    display: inline-block;
  }
}

// 覆盖语言选择器样式
:deep(.language-switcher) {
  .va-select {
    width: 100%;
    min-width: 0;
  }
}
</style>

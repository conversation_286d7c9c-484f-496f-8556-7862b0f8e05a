<template>
  <Bar :data="props.data" :options="{ ...options, ...horizontalBarOptions }" />
</template>

<script setup>
import { Bar } from 'vue-chartjs'

import { Chart as ChartJS, Title, Tooltip, Legend, BarElement, LinearScale, CategoryScale } from 'chart.js'


ChartJS.register(Title, Tooltip, Legend, BarElement, LinearScale, CategoryScale)

const horizontalBarOptions = {
  indexAxis: 'y',
  elements: {
    bar: {
      borderWidth: 1,
    },
  },
}

const props = defineProps({
  data: Object,
  options: Object,
})
</script>

<template>
  <component :is="chartComponent" :chart-data="data" :data="data" :options="chartOptions" class="va-chart" />
</template>

<script setup>
import { computed } from 'vue'
import { defaultConfig, chartTypesMap } from './vaChartConfigs'

defineOptions({
  name: 'V<PERSON><PERSON><PERSON>',
})

const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  options: {
    type: Object,
    default: () => ({})
  },
  type: {
    type: String,
    required: true,
    validator: (value) => ['line', 'bar', 'bubble', 'doughnut', 'pie'].includes(value)
  }
})

const chartComponent = chartTypesMap[props.type]

const chartOptions = computed(() => ({
  ...defaultConfig,
  ...props.options,
}))
</script>

<style lang="scss">
.va-chart {
  min-width: 100%;
  min-height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  >* {
    height: 100%;
    width: 100%;
  }

  canvas {
    width: 100%;
    height: auto;
    min-height: 20px;
  }
}
</style>

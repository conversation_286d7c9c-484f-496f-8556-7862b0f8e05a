<template>
  <div class="notification-container" v-if="visible">
    <div class="notification" :class="[`notification-${type}`, { 'notification-with-close': closable }]" @click="handleClick">
      <div class="notification-content">
        <div class="notification-title">{{ title }}</div>
        <div class="notification-message" v-if="message">{{ message }}</div>
      </div>
      <div class="notification-close" v-if="closable" @click.stop="close">
        <i class="material-icons">close</i>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  message: {
    type: String,
    default: ''
  },
  duration: {
    type: Number,
    default: 4500
  },
  autoClose: {
    type: Boolean,
    default: true
  },
  type: {
    type: String,
    default: 'info',
    validator: (value) => ['info', 'success', 'warning', 'error'].includes(value)
  },
  closable: {
    type: Boolean,
    default: true
  },
  onClose: {
    type: Function,
    default: () => {}
  },
  onClick: {
    type: Function,
    default: () => {}
  }
});

const visible = ref(true);
let timer = null;

const close = () => {
  visible.value = false;
  props.onClose();
};

const handleClick = (event) => {
  props.onClick(event);
};

onMounted(() => {
  if (props.duration > 0 && props.autoClose) {
    timer = setTimeout(() => {
      close();
    }, props.duration);
  }
});
</script>

<style lang="scss" scoped>
.notification-container {
  position: fixed;
  top: 16px;
  right: 16px;
  z-index: 1000;
  max-width: 350px;
  width: 100%;
}

.notification {
  padding: 14px 16px;
  border-radius: 4px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 9px 28px 8px rgba(0, 0, 0, 0.05);
  background: #fff;
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 16px;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &-content {
    flex: 1;
    margin-right: 8px;
  }

  &-title {
    font-weight: 500;
    font-size: 16px;
    margin-bottom: 4px;
    line-height: 24px;
  }

  &-message {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
  }

  &-close {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.45);
    cursor: pointer;
    padding: 0 4px;

    &:hover {
      color: rgba(0, 0, 0, 0.75);
    }
  }

  &-info {
    border-left: 4px solid var(--va-primary);
  }

  &-success {
    border-left: 4px solid var(--va-success);
  }

  &-warning {
    border-left: 4px solid var(--va-warning);
  }

  &-error {
    border-left: 4px solid var(--va-danger);
  }
}
</style>
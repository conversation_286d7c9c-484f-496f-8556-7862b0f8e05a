<template>
  <div class="sub-loading-container" :class="{ active: loading }">
    <div class="sub-loading-content">
      <slot></slot>
    </div>
    <Transition name="fade">
      <div v-if="loading" class="sub-loading-overlay" :style="overlayStyle">
        <div class="sub-loading-spinner">
          <div v-for="n in 3" :key="n" class="pulse-ball" :style="{
            backgroundColor: color,
            animationDelay: `${(n - 1) * 0.15}s`,
          }"></div>
        </div>
        <div v-if="text" class="sub-loading-text">{{ text }}</div>
      </div>
    </Transition>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  /**
   * 控制loading状态的显示
   */
  loading: {
    type: Boolean,
    default: false
  },
  /**
   * 自定义loading球的颜色，默认使用--va-primary
   */
  color: {
    type: String,
    default: 'var(--va-primary)'
  },
  /**
   * 自定义遮罩层的颜色，默认使用--va-background-primary
   */
  overlayColor: {
    type: String,
    default: 'var(--va-background-primary)'
  },
  /**
   * 遮罩层透明度
   */
  overlayOpacity: {
    type: Number,
    default: 0.8
  },
  /**
   * 显示在loading下方的文字
   */
  text: {
    type: String,
    default: ''
  }
})

const overlayStyle = computed(() => ({
  backgroundColor: props.overlayColor,
  opacity: props.overlayOpacity,
}))

// 导出组件属性，使其可以在JS/TS中使用
defineExpose({
  loading: props.loading,
})
</script>

<style lang="scss" scoped>
.sub-loading {
  &-container {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 100px;
  }

  &-content {
    width: 100%;
    height: 100%;
  }

  &-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    border-radius: inherit;
  }

  &-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  &-text {
    margin-top: 16px;
    color: var(--va-on-background-primary);
    font-size: 14px;
  }
}

.pulse-ball {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  animation: pulse 1s infinite ease-in-out;
}

@keyframes pulse {

  0%,
  80%,
  100% {
    transform: scale(0.6);
    opacity: 0.6;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}

// 淡入淡出动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>

<template>
  <VaSidebar v-model="writableVisible" :width="sidebarWidth" :color="color" :minimized-width="!mobile ? '45px' : '0'"
    :minimized="isSidebarMinimized" style="border-right: 1px solid #f9f9f9;">
    <VaAccordion v-model="value" multiple>
      <template v-for="(route, index) in filteredRoutes" :key="index">
        <!-- 顶级路由项没有子路由，直接显示 -->
        <VaSidebarItem v-if="route.hidden !== true && (!route.children || !route.children.length)"
          :to="{ name: route.name }" :active="routeHasActiveChild(route)" :active-color="activeColor"
          :text-color="textColor(route)" :aria-label="`Visit ${route.meta.title}`" role="button" hover-opacity="0.10"
          @click="handleMenuItemClick(true)" :class="{ 'menu-item-minimized': isSidebarMinimized }">
          <VaSidebarItemContent class="py-3 pr-2"
            :class="{ 'pl-4': !isSidebarMinimized, 'justify-start': isSidebarMinimized }">
            <VaIcon v-if="route.meta && route.meta.icon" aria-hidden="true" :name="route.meta.icon" size="20px"
              :color="iconColor(route)" @click.stop="handleIconClick(route)" />
            <VaSidebarItemTitle class="leading-5 font-semibold" :class="{ 'menu-title-hidden': isSidebarMinimized }">
              {{ $t(`${route.meta.title}`) }}
            </VaSidebarItemTitle>
          </VaSidebarItemContent>
          <div v-if="isSidebarMinimized" class="menu-tooltip">
            {{ route.meta.title }}
          </div>
        </VaSidebarItem>
        <!-- 有子路由的项继续使用折叠面板 -->
        <div v-else-if="route.hidden !== true"
          :class="{ 'menu-parent': true, 'menu-parent-minimized': isSidebarMinimized }">
          <VaCollapse>
            <template #header="{ value: isCollapsed }">
              <VaSidebarItem :to="route.children ? undefined : { name: route.name }"
                :active="routeHasActiveChild(route)" :active-color="activeColor" :text-color="textColor(route)"
                :aria-label="`${route.children && route.children.length > 0 ? 'Open category ' : 'Visit'} ${route.meta.title}`"
                role="button" hover-opacity="0.10" @click="handleMenuItemClick(route.children ? false : true)"
                :class="{ 'menu-item-minimized': isSidebarMinimized }">
                <VaSidebarItemContent class="py-3 pr-2"
                  :class="{ 'pl-4': !isSidebarMinimized, ' justify-start': isSidebarMinimized }">
                  <VaIcon v-if="route.meta && route.meta.icon" aria-hidden="true" :name="route.meta.icon" size="20px"
                    :color="iconColor(route)" @click.stop="handleIconClick(route)" />
                  <VaSidebarItemTitle class="flex justify-between items-center leading-5 font-semibold"
                    :class="{ 'menu-title-hidden': isSidebarMinimized }">
                    {{ $t(`${route.meta.title}`) }}
                    <VaIcon v-if="route.children && route.children.length && !isSidebarMinimized"
                      :name="arrowDirection(isCollapsed)" size="20px" />
                  </VaSidebarItemTitle>
                </VaSidebarItemContent>
              </VaSidebarItem>
              <!-- 添加悬浮子菜单 -->
              <div v-if="isSidebarMinimized" class="hover-submenu">
                <div class="hover-submenu-header">
                  {{ $t(`${route.meta.title}`) }}
                </div>
                <div class="hover-submenu-body">
                  <template v-for="(childRoute, childIndex) in route.children" :key="childIndex">
                    <div v-if="childRoute.hidden !== true" class="hover-submenu-item"
                      :class="{ 'active': isActiveChildRoute(childRoute) }" @click="navigateToRoute(childRoute)">
                      {{ $t(`${childRoute.meta.title}`) }}
                    </div>
                  </template>
                </div>
              </div>
            </template>
            <template #body>
              <div :class="{ 'submenu-container': true, 'submenu-container-minimized': isSidebarMinimized }">
                <template v-for="(childRoute, index2) in route.children" :key="index2">
                  <div v-if="childRoute.hidden !== true">
                    <VaSidebarItem :to="{ name: childRoute.name }" :active="isActiveChildRoute(childRoute)"
                      :active-color="activeColor" :text-color="textColor(childRoute)"
                      :aria-label="`Visit ${childRoute.meta.title}`" hover-opacity="0.10"
                      @click="handleMenuItemClick(true)">
                      <VaSidebarItemContent class="py-3 pr-2"
                        :class="{ 'pl-11': !isSidebarMinimized, 'pl-4': isSidebarMinimized }">
                        <VaSidebarItemTitle class="leading-5 font-semibold">
                          {{ $t(`${childRoute.meta.title}`) }}
                        </VaSidebarItemTitle>
                      </VaSidebarItemContent>
                    </VaSidebarItem>
                  </div>
                </template>
              </div>
            </template>
          </VaCollapse>
        </div>
      </template>
    </VaAccordion>
    <div class="menu-toggle-button" :class="{ 'sidebar-minimized': isSidebarMinimized }"
      @click="isSidebarMinimized = !isSidebarMinimized" v-if="!mobile">
      <VaIcon name="chevron_left" size="20px" />
    </div>
  </VaSidebar>
</template>
<script>
import { defineComponent, watch, ref, computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { usePermissionStore } from '@/stores/permission.js'
import { useGlobalStore } from '@/stores/global-store.js'
import { useI18n } from 'vue-i18n'
import { useColors } from 'vuestic-ui'
import VaIconMenuCollapsed from '../icons/VaIconMenuCollapsed.vue'

export default defineComponent({
  name: 'Sidebar',
  components: {
    VaIconMenuCollapsed
  },
  props: {
    visible: { type: Boolean, default: true },
    mobile: { type: Boolean, default: false },

  },
  emits: ['update:visible'],

  setup: (props, { emit }) => {
    const { getColor, colorToRgba } = useColors()
    const route = useRoute()
    const router = useRouter()
    const { t } = useI18n()
    const permissionStore = usePermissionStore()
    const globalStore = useGlobalStore()

    const value = ref([])

    const writableVisible = computed({
      get: () => props.visible,
      set: (v) => emit('update:visible', v),
    })

    // 侧边栏折叠状态
    const isSidebarMinimized = computed({
      get: () => globalStore.isSidebarMinimized,
      set: (value) => {
        globalStore.isSidebarMinimized = value
      }
    })

    // 处理菜单项点击事件
    const handleMenuItemClick = (shouldMinimize = false) => {

      // 如果不是需要收起侧边栏的点击，则直接返回
      if (!shouldMinimize) return;
      // 延迟执行，确保路由跳转完成后再收起侧边栏
      nextTick(() => {
        // 根据设备类型决定是否收起侧边栏
        globalStore.minimizeSidebarIfMobile()
      })
    }

    // 处理图标点击事件
    const handleIconClick = (route) => {
      // 如果侧边栏已经折叠，则展开它 




      if (isSidebarMinimized.value) {
        let routers = filteredRoutes.value.filter(item => {
          return item.children && item.children.length > 0 && item.hidden === false
        })
        let index = routers.findIndex(item => item.name === route.name)
        isSidebarMinimized.value = false
        if (index !== -1) {
          value.value[index] = true
        }  // 如果有子路由，则展开对应的父菜单



      }
    }

    // 导航到指定路由并处理侧边栏
    const navigateToRoute = (childRoute) => {
      router.push({ name: childRoute.name })
      // 延迟执行，确保路由跳转完成后再收起侧边栏
      nextTick(() => {
        // 根据设备类型决定是否收起侧边栏
        globalStore.minimizeSidebarIfMobile()
      })
    }

    const filteredRoutes = computed(() => {
      // 获取路由配置
      const routes = permissionStore.sidebarRouters || [];
      // 创建一个新的路由数组，将首页单独提取出来作为顶级菜单项
      const result = [];
      // 添加首页作为单独的顶级菜单项
      result.push({
        hidden: false,
        meta: {
          title: '首页',
          icon: 'vuestic-iconset-dashboard',
        },
        name: 'dashboard',
        path: 'dashboard',
      });

      // 处理其他路由，移除首页子路由
      routes.forEach(route => {
        // 深拷贝路由对象，避免修改原始数据
        const routeCopy = JSON.parse(JSON.stringify(route));
        // 如果这个路由本身就是首页，则跳过
        if (routeCopy.name === 'dashboard' || routeCopy.path === 'dashboard') {
          return;
        }
        // 如果有子路由，处理子路由
        if (routeCopy.children && routeCopy.children.length) {
          // 过滤掉子路由中的首页
          routeCopy.children = routeCopy.children.filter(child =>
            child.name !== 'dashboard' && child.path !== 'dashboard'
          );

          // 只有当过滤后还有子路由时，才添加这个路由
          if (routeCopy.children.length > 0) {
            result.push(routeCopy);
          } else if (routeCopy.meta && routeCopy.meta.title !== '首页') {
            // 如果过滤后没有子路由，但路由本身不是首页，则作为单独项添加
            delete routeCopy.children;
            result.push(routeCopy);
          }
        } else {
          // 没有子路由的直接添加
          result.push(routeCopy);
        }
      });
      return result;
    })

    const isActiveChildRoute = (child) => {
      // 特殊处理首页路由
      if (child.name === 'dashboard') {
        return route.name === 'dashboard' || route.path === '/dashboard';
      }
      return route.name === child.name || route.path.startsWith(`${child.path}/`)
    }
    // 
    const routeHasActiveChild = (section) => {
      // 特殊处理首页路由
      if (section.name === 'dashboard') {
        return route.name === 'dashboard' || route.path === '/dashboard';
      }

      if (!section.children || !section.children.length) {
        return route.path === section.path || route.path.startsWith(`${section.path}/`)
      }

      return section.children.some((child) =>
        route.path === child.path || route.path.startsWith(`${child.path}/`)
      )
    }

    // const setActiveExpand = () => {
    //   // 获取当前已展开的菜单项索引
    //   const currentExpandedIndices = value.value.map((isExpanded, index) => isExpanded ? index : -1).filter(index => index !== -1)

    //   // 计算应该展开的菜单项
    //   const newExpandState = filteredRoutes.value.map((route) => routeHasActiveChild(route))


    // }

    const sidebarWidth = computed(() => (props.mobile ? '240px' : '240px'))
    const color = computed(() => getColor('background-secondary'))
    const activeColor = computed(() => colorToRgba(getColor('focus'), 0.1))
    const collapseIconColor = computed(() => getColor('secondary'))

    const iconColor = (route) => (routeHasActiveChild(route) ? 'primary' : 'secondary')
    const textColor = (route) => (routeHasActiveChild(route) ? 'primary' : 'textPrimary')
    const arrowDirection = (state) => (state ? 'mso-expand_less' : 'mso-expand_more')

    // watch(() => route.fullPath, { immediate: true })

    return {
      writableVisible,
      sidebarWidth,
      value,
      color,
      activeColor,
      filteredRoutes,
      routeHasActiveChild,
      isActiveChildRoute,
      handleMenuItemClick,
      handleIconClick,
      navigateToRoute,
      t,
      iconColor,
      textColor,
      arrowDirection,
      isSidebarMinimized,
      collapseIconColor,
    }
  },
})
</script>

<style>
/* 为被激活的路由添加active-color */
.va-sidebar .va-sidebar__item.va-sidebar__item--active .va-sidebar__item__content,
.va-sidebar .va-sidebar__item[aria-selected="true"] .va-sidebar__item__content {
  background-color: var(--va-color-primary-100) !important;
}

/* 确保激活的子菜单项也有相应的样式 */
.va-sidebar .va-collapse__body .va-sidebar__item.va-sidebar__item--active .va-sidebar__item__content,
.va-sidebar .va-collapse__body .va-sidebar__item[aria-selected="true"] .va-sidebar__item__content {
  background-color: var(--va-color-primary-100) !important;
}

/* 折叠图标翻转效果 */
.x-flip {
  transform: scaleX(-100%);
}

/* 侧边栏头部样式 */
.sidebar-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* 折叠菜单样式 */
.menu-title-hidden {
  display: none;
}

.menu-item-minimized {
  position: relative;
}

/* 悬浮提示样式 */
.menu-tooltip {
  position: absolute;
  left: 100%;
  top: 0;
  background-color: var(--va-background-primary);
  padding: 10px 15px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 10;
  white-space: nowrap;
  display: none;
  transition: all 0.3s;
}

.menu-item-minimized:hover .menu-tooltip {
  display: block;
}

/* 父级菜单项样式 */
.menu-parent {
  position: relative;
}

.menu-parent-minimized:hover .hover-submenu {
  display: block;
}

.va-sidebar__menu {
  justify-content: space-between;
  overflow-x: hidden;
}


/* 折叠状态下的子菜单容器 */
.submenu-container-minimized {
  display: none;
}

/* 悬浮子菜单样式 */
.hover-submenu {
  position: absolute;
  left: 100%;
  top: 0;
  min-width: 180px;
  background-color: var(--va-background-primary);
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 100;
  display: none;
  overflow: hidden;
  transition: all 0.3s ease;
}

.hover-submenu-header {
  padding: 12px 15px;
  font-weight: 600;
  color: var(--va-text-primary);
  background-color: var(--va-background-element);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.hover-submenu-body {
  max-height: 300px;
  overflow-y: auto;
}

.hover-submenu-item {
  padding: 10px 15px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--va-text-primary);
  white-space: nowrap;
}

.hover-submenu-item:hover {
  background-color: var(--va-color-primary-50);
}

.hover-submenu-item.active {
  background-color: var(--va-color-primary-100);
  color: var(--va-primary);
  font-weight: 500;
}

/* 菜单图标在折叠状态下的样式 */
.menu-item-minimized .va-icon {
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.menu-item-minimized .va-icon:hover {
  background-color: rgba(73, 168, 255, 0.1);
}

/* 过渡动画 */
.va-sidebar {
  transition: all 0.1s ease;
  overflow-x: hidden;
}

.menu-toggle-button {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 5px;
  width: 35px;
  height: 35px;
  background-color: rgba(73, 168, 255, 0.1);
  padding: 8px;
  /* Adjust padding as needed */
  cursor: pointer;
  transition: transform 0.1s ease, background-color 0.1s ease;
  border-radius: 50%;
  margin-bottom: 1.5rem;
  /* Makes the button round */
}

.menu-toggle-button:hover {
  background-color: rgba(73, 168, 255, 0.1);
  /* Subtle hover effect */
}

.menu-toggle-button:active {
  transform: scale(0.9);
  /* Click (active) effect */
  background-color: rgba(73, 168, 255, 0.1);
  /* Darker background on click */
  transition: all 0.1s ease;
  /* Faster transition for click */
}

/* If you want the icon to rotate, you can add this */
.menu-toggle-button .va-icon {
  transition: all 0.1s ease;
}

.menu-toggle-button.sidebar-minimized .va-icon {
  transform: rotate(180deg);
  /* Rotate the icon when sidebar is minimized */
}
</style>
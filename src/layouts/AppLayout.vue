<template>
  <VaLayout :top="{ fixed: true, order: 2 }"
    :left="{ fixed: true, absolute: breakpoints.smDown, order: 1, overlay: breakpoints.mdDown && !isSidebarMinimized }"
    @leftOverlayClick="isSidebarMinimized = true">
    <template #top>
      <AppNavbar :is-mobile="isMobile" />
    </template>

    <template #left>
      <AppSidebar :minimized="isSidebarMinimized" :animated="!isMobile" :mobile="isMobile" />
    </template>

    <template #content>
      <div :class="{ minimized: isSidebarMinimized }" class="app-layout__sidebar-wrapper">
        <div v-if="isFullScreenSidebar" class="flex justify-end">
          <VaButton class="px-4 py-4" icon="md_close" preset="plain" @click="onCloseSidebarButtonClick" />
        </div>
      </div>
      <main class="p-4 mx-auto">
        <article>
          <RouterView v-slot="{ Component }">
            <keep-alive :include="cachedViews">
              <component :is="Component" />
            </keep-alive>
          </RouterView>
        </article>
   
      </main>

    </template>
  </VaLayout>
</template>

<script setup>
import { onBeforeUnmount, onMounted, ref, computed, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { onBeforeRouteUpdate, useRoute } from 'vue-router'
import { useBreakpoint } from 'vuestic-ui'
import { useUserStore } from '../stores/user-store'
import { useGlobalStore } from '../stores/global-store'
import { useTagsViewStore } from '../stores/tagsView'
import AppNavbar from '../components/navbar/AppNavbar.vue'
import AppSidebar from '../components/sidebar/AppSidebar.vue'

const GlobalStore = useGlobalStore()
const userStore = useUserStore()
const tagsViewStore = useTagsViewStore()
const route = useRoute()
const breakpoints = useBreakpoint()
const sidebarWidth = ref('16rem')
const sidebarMinimizedWidth = ref(undefined)
const isMobile = ref(false)
const isTablet = ref(false)
const { isSidebarMinimized } = storeToRefs(GlobalStore)
const { enableNavigation } = storeToRefs(userStore)
const { cachedViews } = storeToRefs(tagsViewStore)

const onResize = () => {
  isSidebarMinimized.value = breakpoints.mdDown
  isMobile.value = breakpoints.smDown
  isTablet.value = breakpoints.mdDown
  sidebarMinimizedWidth.value = isMobile.value ? '0' : '4.5rem'
  sidebarWidth.value = isTablet.value ? '100%' : '16rem'

  // 更新全局状态中的设备类型
  if (isMobile.value) {
    GlobalStore.setDeviceType('mobile') // 移动端
  } else if (isTablet.value) {
    GlobalStore.setDeviceType('tablet') // 平板端
  } else {
    GlobalStore.setDeviceType('desktop') // 桌面端
  }
}

onMounted(() => {
  window.addEventListener('resize', onResize)
  onResize()
  // 初始路由添加到缓存
  tagsViewStore.addCachedView(route)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', onResize)
})

onBeforeRouteUpdate((to) => {
  if (breakpoints.mdDown) {
    // Collapse sidebar after route change for Mobile
    isSidebarMinimized.value = true
  }
})

// 监听路由变化，处理缓存
watch(() => route.name, () => {
  if (route.name) {
    tagsViewStore.addCachedView(route)
  }
})

const isFullScreenSidebar = computed(() => isTablet.value && !isSidebarMinimized.value)

const onCloseSidebarButtonClick = () => {
  isSidebarMinimized.value = true
}
</script>

<style lang="scss" scoped>
// Prevent icon jump on animation
.va-sidebar {
  width: unset !important;
  min-width: unset !important;
}

/* 面包屑动画效果 */
.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition:
    opacity 0.3s ease,
    max-height 0.3s ease,
    transform 0.3s ease,
    margin 0.3s ease,
    padding 0.3s ease;
  max-height: 50px;
  opacity: 1;
  transform: translateY(0);
  overflow: hidden;
}

.breadcrumb-enter-from,
.breadcrumb-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 0;
  padding-bottom: 0;
  overflow: hidden;
}
</style>
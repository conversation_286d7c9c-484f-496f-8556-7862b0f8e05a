<template>
  <VaLayout class="h-screen bg-[var(--va-background-secondary)]"
    :class="{ 'layout-large': breakpoint.lgUp, 'layout-small': !breakpoint.lgUp }">
    <template v-if="breakpoint.lgUp" #left>
      <RouterLink class="bg-primary h-full flex items-center justify-center" style="width: 35vw" to="/"
        aria-label="Visit homepage">
        <div class="max-w-[350px]">
          <VuesticLogo start="#0E41C9" />
        </div>
      </RouterLink>
    </template>
    <template #content>
      <div class="relative flex h-full items-center justify-center" :class="{ 'p-4': !breakpoint.lgUp }">
        <main :class="['mx-auto max-w-[420px]', 'flex flex-col items-center justify-center']">
          <div class="w-full flex flex-col items-center">
            <RouterLink v-if="!breakpoint.lgUp" class="py-4 self-center mb-4" to="/dashboard" aria-label="Visit homepage">
              <div class="max-w-[250px]">
                <VuesticLogo start="#0E41C9" />
              </div>
            </RouterLink>
            <RouterView />
          </div>
        </main>
        <div class="copyright text-center text-sm text-gray-500 absolute bottom-[20px] w-full">
          <p>Copyright © 2025 SubDigi Network. All Rights Reserved.</p>
        </div>
      </div>
    </template>
  </VaLayout>
</template>

<script setup>
import { useBreakpoint } from 'vuestic-ui'
import VuesticLogo from '../components/VuesticLogo.vue'
import { useI18n } from 'vue-i18n'
const breakpoint = useBreakpoint()
const { t } = useI18n()
</script>

<style lang="scss" scoped>
.layout-large {
  /* 大屏幕布局样式 */
}

.layout-small {
  /* 小屏幕布局样式 */
}
</style>

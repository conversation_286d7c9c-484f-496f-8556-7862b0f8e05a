import request from '../../utils/request'

/**
 * 获取游戏列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 每页大小
 * @param {string} params.gameName - 游戏名称
 * @param {string} params.gameType - 游戏类型
 * @param {string} params.gameCategory - 游戏类别
 * @param {number} params.enable - 是否启用
 * @param {number} params.featured - 是否精选
 * @returns {Promise} API响应
 */
export function getGameList(params) {
  return request({
    url: '/games',
    method: 'get',
    params: params,
  })
}

/**
 * 获取游戏种类列表
 * @returns {Promise} API响应
 */
export function getGameCategoryList() {
  return request({
    url: '/games/category',
    method: 'get',
  })
}

/**
 * 更新游戏信息
 * @param {Object} gameData - 游戏数据
 * @param {string} gameData.gameName - 游戏名称
 * @param {string} gameData.gameUri - 游戏URI
 * @param {string} gameData.gameIcon - 游戏图标
 * @param {string} gameData.gamePreview - 游戏预览图
 * @param {string} gameData.gameType - 游戏类型
 * @param {number} gameData.gameHot - 热度
 * @param {number} gameData.featured - 是否精选
 * @param {string} gameData.gameCategory - 游戏类别
 * @param {string} gameData.gameDesc - 游戏描述
 * @param {number} gameData.enable - 是否启用
 * @returns {Promise} API响应
 */
export function updateGame(gameData) {
  return request({
    url: '/games',
    method: 'put',
    data: gameData,
  })
}

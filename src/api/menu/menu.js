import request from '../../utils/request'

// 查询菜单列表
export function listMenu() {
    return request({
        url: '/sys/menu/list',
        method: 'get',

    })
}

// 查询菜单详细
export function getMenu(id) {
    return request({
        url: '/sys/menu/' + id,
        method: 'get'
    })
}

// 查询菜单下拉树结构
export function treeselect(id) {
    return request({
        url: '/sys/menu/roleMenuTreeselect/' + id,
        method: 'get'
    })
}

// 根据角色ID查询菜单下拉树结构
export function roleMenuTreeselect(roleId) {
    return request({
        url: '/sys/menu/roleMenuTreeselect/' + roleId,
        method: 'get'
    })
}

// 新增菜单
export function addMenu(data) {
    return request({
        url: '/sys/menu/addMenu',
        method: 'post',
        data: data
    })
}

// 修改菜单
export function updateMenu(data) {
    return request({
        url: '/sys/menu/updateMenu',
        method: 'post',
        data: data
    })
}

// 删除菜单
export function delMenu(menuId) {
    return request({
        url: '/sys/menu/' + menuId,
        method: 'delete'
    })
}
export function getRouters() {
    return request({
        url: '/getRouters',
        method: 'get'
    })
}
import request from '../../utils/request'

// 查询角色列表
export function listRole(query) {
  return request({
    url: '/sys/role/list',
    method: 'get',
    params: query,
  })
}

// 查询角色详细
export function getRole(roleId) {
  return request({
    url: '/sys/role/' + roleId,
    method: 'get',
  })
}

// 新增角色
export function addRole(data) {
  return request({
    url: '/sys/role/addRole',
    method: 'post',
    data: data,
  })
}

// 修改角色
export function updateRole(data) {
  return request({
    url: '/sys/role/updateRole',
    method: 'post',
    data: data,
  })
}

// 删除角色
export function delRole(roleId) {
  return request({
    url: '/sys/role?arrayIds=' + roleId,
    method: 'delete',
  })
}

// 批量授权用户
export function batchAuthUserRole(roleId, userIds) {
  // 创建FormData来正确传递数组参数
  const formData = new FormData()
  formData.append('roleId', roleId)

  // 将userIds数组添加到FormData中
  userIds.forEach((userId) => {
    formData.append('userIds', userId)
  })

  return request({
    url: '/sys/role/batch/authUserRole',
    method: 'put',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

// 批量取消用户授权，将用户降为普通用户
export function batchCancelAuthUser(userIds) {
  // 创建FormData来正确传递数组参数
  const formData = new FormData()

  // 将userIds数组添加到FormData中
  userIds.forEach((userId) => {
    formData.append('userIds', userId)
  })

  return request({
    url: '/sys/role/batch/cancelAuthUser',
    method: 'put',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

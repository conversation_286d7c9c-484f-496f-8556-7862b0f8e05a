import request from '../../utils/request'

/**
 * @description 查询用户列表
 * @param query
 * @returns {Promise<UserListResponse>}
 */
export function listUser(query) {
  return request({
    url: '/sys/user/getUserList',
    method: 'get',
    params: query,
  })
}
/**
 * @description 获取角色列表
 * @param query
 * @returns {Promise<BaseResponse<RoleListItem>>}
 */
export function getUser() {
  return request({
    url: '/sys/user',
    method: 'get',
  })
}

/**
 * @description 新增用户
 * @param userId 用户id
 * @returns {Promise<BaseResponse<any>>}
 */
export function addUser(data) {
  return request({
    url: '/sys/user/insertUser',
    method: 'post',
    data: data,
  })
}

/**
 * @description 删除用户
 * @param userId 用户id
 * @returns {Promise<BaseResponse<any>>}
 */
export function delUser(userId) {
  return request({
    url: '/sys/user/deleteUser?arrayIds=' + userId,
    method: 'delete',
  })
}

/**
 * @description 修改用户状态
 * @param id 用户id
 * @param enable 是否启用
 * @returns {Promise<BaseResponse<any>>}
 */
export function changeUserStatus(id, enable) {
  const data = {
    id,
    enable,
  }
  return request({
    url: '/sys/user/updateUser',
    method: 'post',
    data: data,
  })
}

/**
 * @description 获取当前登录的用户信息
 * @returns {Promise<UserInfoResponse>}
 */
export function getUserInfo() {
  return request({
    url: '/getInfo',
    method: 'get',
  })
}

/**
 * @description 登录时验证
 * @returns {Promise<UserInfoResponse>}
 */
export function isTwofaVerify(data) {
  return request({
    url: '/2faVerify',
    method: 'post',
    data: data,
  })
}

/**
 * @description 获取二维码
 * @param data 请求参数
 * @returns {Promise<TwoFaAuthResponse>}
 */
export function getTwoFaAuth() {
  return request({
    url: '/getTwoFaAuth',
    method: 'post',
  })
}

/**
 * @description 绑定2fa
 * @param data 请求参数
 * @returns {Promise<TwoFaAuthResponse>}
 */
export function setTwoFaAuth(data) {
  return request({
    url: '/setTwoFaAuth',
    method: 'post',
    data: data,
  })
}

/**
 * @description 绑定2fa
 * @param data 请求参数
 * @returns {Promise<TwoFaAuthResponse>}
 */
export function closeTwoFa(data) {
  return request({
    url: '/closeTwoFaAuth',
    method: 'post',
    data: data,
  })
}

// 登录方法
export function login(username, password, code, uuid) {
  const data = {
    userName: username,
    password,
    code,
    uuid,
  }
  return request({
    url: '/login',
    headers: {
      isToken: false,
      repeatSubmit: false,
    },
    method: 'post',
    data: data,
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/logout',
    method: 'post',
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/getVerifyCodeImg',
    headers: {
      isToken: false,
    },
    method: 'get',
    timeout: 20000,
  })
}


/**
 * @description 修改用户密码
 * @param data 请求参数
 * @returns {Promise<BaseResponse<any>>}
 */
export function updateUserPwd(data) {
  return request({
    url: '/sys/user/updateUserPwd',
    method: 'post',
    data: data,
  })
}

/**
 * @description 更新用户设置
 * @param data 请求参数
 * @returns {Promise<BaseResponse<any>>}
 */
export function updateUserSetting(data) {
  return request({
    url: '/sys/user/updateUserSetting',
    method: 'post',
    data: data,
  })
}
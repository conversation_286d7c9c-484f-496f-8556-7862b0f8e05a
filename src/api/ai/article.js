import request from '@/utils/request'

// 原始axios请求函数，已不使用，保留做参考
export function articleRequest(query) {
    return request({
        url: '/v1/article/outline',
        method: 'get',
        params: query,
    })
}

// 获取API基础URL
const getApiBaseUrl = () => {
    if (import.meta.env.MODE === 'development') {
        return 'http://192.168.31.189:9200';
    } else {
        return 'https://dash.subdigi.com/prod-api';
    }
};

// 构建带查询参数的URL
export function buildArticleUrl(query, isOutline = true) {
    const baseUrl = getApiBaseUrl();

    const endpoint = isOutline ? '/v1/article/outline' : '/v1/article/context';
    const url = new URL(`${baseUrl}${endpoint}`);

    // 添加查询参数
    if (query) {
        Object.keys(query).forEach(key => {
            url.searchParams.append(key, query[key]);
        });
    }

    return url.toString();
}

// 构建大纲生成URL
export function buildOutlineUrl(query) {
    return buildArticleUrl(query, true);
}

// 构建内容生成URL
export function buildContentUrl(query) {
    return buildArticleUrl(query, false);
}

// article函数现在只返回请求URL，实际的EventSource连接在组件内创建
export function article(query, isOutline = true) {
    return buildArticleUrl(query, isOutline);
}
import request from '../../utils/request'

// 筛选域名
export function list(query) {
  return request({
    url: "/adv/cf/list",
    method: 'get',
    params: query
  })
}
// 筛选出 没有添加到CF的域名
export function diffDomain() {
  return request({
    url: "/adv/cf/diffList",
    method: 'get'
  })
}
// 手动更新 系统CF 域名信息
export function updateDomainRecord() {
  return request({
    url: "/adv/cf/updateCF",
    method: 'put'
  })
}
// 清理CF 的CDN 缓存信息
export function clearCache() {
  return request({
    url: "/adv/cf/clear/all",
    method: "get"
  })
}
// 更新CF 配置信息
export function updateCF(zoneId) {
  return request({
    url: "/adv/cf/updateCF/" + zoneId,
    method: "put"
  })
}
// 获取Dns 记录
export function getDns(domain) {
  return request({
    url: '/adv/cf/getDns/'+domain,
    method: 'get'
  })
}

// 新增域名托管
export function add(data) {
  return request({
    url: '/adv/cf/add',
    method: 'post',
    data: data
  })
}

// 修改 域名托管
export function update(data) {
  return request({
    url: '/adv/cf/update',
    method: 'post',
    data: data
  })
}
export function updateTrusteeship(data) {
  return request({
    url: '/adv/cf/update/dns',
    method: 'post',
    data: data
  })
}

// 删除 dns 记录
export function deleteDns(data) {
  return request({
    url: '/adv/cf/deleteDns',
    method: 'post',
    data: data
  })
}

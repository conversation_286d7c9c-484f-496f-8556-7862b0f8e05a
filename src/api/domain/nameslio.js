import request from '../../utils/request'

// 筛选域名
export function list(query) {
  return request({
    url: "/adv/domain/register/list",
    method: 'get',
    params: query
  })
}

// 检查域名是否可用
export function checkDomain(domain) {
  return request({
    url: "/adv/domain/register/check/" + domain,
    method: 'get'
  })
}

// 域名注册
export function registerDomain(data) {
  return request({
    url: "/adv/domain/register/buy",
    method: "put",
    data: data
  })
}

// 获取新的域名
export function getNewsDomainList() {
  return request({
    url: '/adv/domain/register/getNewsDomainList',
    method: 'get'
  })
}

// 将域名配置信息入库处理
export  function importDomain(domainList) {
  return request({
    url: '/adv/domain/register/importDomain',
    method: 'post',
    data: domainList
  })
}

// 移除域名
export function deleteDomain(idList) {
  return request({
    url: '/adv/domain/register/delete',
    method: 'post',
    data: idList
  })
}
// 修改ns 服务配置
export function updateNs(data) {
  return request({
    url: '/adv/domain/register/updateNs',
    method: 'post',
    data: data
  })
}

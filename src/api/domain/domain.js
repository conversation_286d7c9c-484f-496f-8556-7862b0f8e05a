import request from '../../utils/request'
// // 清除CDN 上缓存信息
export function clearCdnCache(zone_id) {
  return request({
    url: "/adv/cf/clear/" + zone_id,
    method: "get"
  })
}

// 查询域名
export function listDomain(query) {
  return request({
    url: "/adv/domain/list",
    method: 'get',
    params: query
  })
}
// 新增域名
export function addDomain(data) {
  return request({
    url: "/adv/domain",
    method: 'post',
    data: data
  })
}
// 新增站点数据
export function addSite(data) {
  return request({
    url: "/adv/domain/insert",
    method: 'post',
    data: data
  })
}
// 删除站点数据
export function deleteSite(data) {
  return request({
    url: "/adv/domain/" + data.id,
    method: 'delete',
    data: data
  })
}
// // 修改域名配置信息
export function updateDomain(data) {
  return request({
    url: "/adv/domain",
    method: "put",
    data: data
  })
}
// 拉去cf 上没有被设置过的域名
export function diffDomain() {
  return request({
    url: "/adv/domain/diffList",
    methods: "get"
  })
}
// 获取对应渠道
export function advchannel() {
  return request({
    url: '/adv/domain/advChannel',
    method: 'get'
  })
}

// 设置nginx 配置
export function settingNginx(domain) {
  return request({
    url: '/adv/domain/nginx/' + domain,
    method: 'post'
  })
}


export  function execPullCode() {
  return request({
    url: '/adv/domain/pullCode',
    method: 'get'
  })

}

export  function getWebPrompts(param) {
  return request({
    url: '/adv/prompts/getAdvertPrompts',
    method: 'get',
    params: param
  })
}

export function saveWebPrompts(data) {
  return request({
    url: '/adv/prompts/saveAdvertPrompts',
    method: 'post',
    data: data
  })
}
export function getPromptsAll1(data) {
  return request({
    url: '/adv/prompts/listAll',
    method: 'get',
    params: data
  })
}

export function addPrompts(data) {
  return request({
    url: '/adv/prompts/add',
    method: 'post',
    data: data
  })
}

export function updatePrompts(data) {
  return request({
    url: '/adv/prompts/update',
    method: 'post',
    data: data
  })
}
// 修改（域名广告）
export function changeDomain(data) {
  return request({
      url: '/adv/domain/update',
      method: 'post',
      data: data
  })
}





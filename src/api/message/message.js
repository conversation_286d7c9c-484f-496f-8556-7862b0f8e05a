import request from '../../utils/request'

/**
 * @description 获取是否有消息,数据等通知数据
 */
export function getMessageData() {
    return request({
        url: '/user/message/getMessageData',
        method: 'get',
    })
}

/**
 * @description 获取用户消息列表
 * @param {Object} params
 * @param {number} params.pageNum
 * @param {number} params.pageSize
 */
export function getMessageList(params) {
    return request({
        url: '/user/message/list',
        method: 'get',
        params: params,
    })
}

/**
 * @description 更新用户消息状态
 * @param {Object} params
 * @param {number} params.ids // 更新的消息id列表
 * @param {number} params.isAll // 是否全部已读
 */
export function updateMessageStatus(params) {
    return request({
        url: '/user/message/updateMessageStatus',
        method: 'post',
        data: params,
    })
}
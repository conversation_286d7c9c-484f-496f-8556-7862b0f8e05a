import request from '../../utils/request'

/**
 * 获取Redis缓存名称列表
 * @returns {Promise} API响应
 */
export function getCacheNames() {
  return request({
    url: '/sys/monitor/cache/getCacheNames',
    method: 'get',
  })
}

/**
 * 获取Redis缓存键名列表
 * @param {string} cacheName - 缓存名称
 * @returns {Promise} API响应
 */
export function getCacheKeys(cacheName) {
  return request({
    url: `/sys/monitor/cache/getCacheKeys/${cacheName}`,
    method: 'get',
  })
}

/**
 * 获取Redis缓存内容
 * @param {string} cacheName - 缓存名称
 * @param {string} cacheKey - 缓存键名
 * @returns {Promise} API响应
 */
export function getCacheValue(cacheName, cacheKey) {
  return request({
    url: `/sys/monitor/cache/getCacheValue/${cacheName}/${cacheKey}`,
    method: 'get',
  })
}

/**
 * 获取缓存基本信息
 * @returns {Promise} API响应
 */
export function getCacheInfo() {
  return request({
    url: '/sys/monitor/cache/getCacheInfo',
    method: 'get',
  })
}

/**
 * 删除缓存（模糊删除）
 */
export function delCacheName(cacheName) {
  return request({
    url: `/sys/monitor/cache/deleteCacheName/${cacheName}`,
    method: 'delete',
  })
}

/**
 * 删除缓存（精确删除）
 */
export function delCacheKey(cacheKey) {
  return request({
    url: `/sys/monitor/cache/deleteCacheKey/${cacheKey}`,
    method: 'delete',
  })
}

/**
 * 删除所有缓存
 */
export function delAll() {
  return request({
    url: '/sys/monitor/cache/clearCacheAll',
    method: 'delete',
  })
}

import request from '../../utils/request'

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/sys/user/getUserList',
    method: 'get',
    params: query,
  })
}

// 查询用户详细
export function getUser() {
  return request({
    url: '/sys/user',
    method: 'get',
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: '/sys/user/insertUser',
    method: 'post',
    data: data,
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: '/sys/user/updateUser',
    method: 'post',
    data: data,
  })
}

// 删除用户
export function delUser(arrayIds) {
  return request({
    url: `/sys/user/deleteUser?arrayIds=${arrayIds.join(',')}`,
    method: 'delete',
  })
}



/**
* @description 重置用户密码
* @param data 请求参数
* @returns {Promise<BaseResponse<any>>}
*/
export function resetUserPwd() {
  return request({
    url: '/sys/user/resetUserPwd ',
    method: 'post',
  })
}
import request from "../../utils/request";


// 筛选
export function list(query) {
    return request({
        url: '/ads/adv/list',
        method: 'get',
        params: query
    })
}

export function listAll(query) {
    return request({
        url: '/ads/adv/list',
        method: 'get',
        params: {
            pageNum: 1,
            pageSize: 100
        }
    })
}

// 新增
export function add(data) {
    return request({
        url: '/ads/adv/insert',
        method: 'post',
        data: data
    })
}

// 修改
export function update(data) {
    return request({
        url: 'ads/adv/update',
        method: 'post',
        data: data
    })
}

// 移除
export function remove(id) {
    return request({
        url: '/ads/adv/remove/' + id,
        method: 'delete'
    })
}

export function getPageInit() {
    return request({
        url: '/adv/domain/pageInit',
        method: 'get'
    })
}

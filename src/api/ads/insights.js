import request from '@/utils/request'

// 筛选
export function list(query) {
  return request({
    url: '/ads/insights/list',
    method: 'get',
    params: query,
  })
}

export function advs(query) {
  return request({
    url: '/ads/adv/list',
    method: 'get',
    params: {
      pageNum: 1,
      pageSize: 100,
    },
  })
}
// 导入
export function uploadFile(data) {
  return request({
    url: '/ads/insights/importData',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data: data,
  })
}

// 域名
export function domains() {
  return request({
    url: '/ads/aff/insights/pageInit',
    method: 'get',
    params: {
      pageNum: 1,
      pageSize: 1000,
    },
  })
}
// 域名
export function affDomains() {
  return request({
    url: '/ads/aff/app/listAvailableDomain',
    method: 'get',
    params: {
      pageNum: 1,
      pageSize: 1000,
    },
  })
}
// 渠道
export function affs() {
  return request({
    url: '/ads/aff/list',
    method: 'get',
  })
}

export function affInsights(data) {
  return request({
    url: '/ads/aff/insights/list',
    method: 'get',
    params: data,
  })
}



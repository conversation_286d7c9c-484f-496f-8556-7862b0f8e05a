import request from "@/utils/request";

export function offerList(query) {
  return request({
    url: '/ads/search/off/list',
    method: 'get',
    params: query
  })
}

export function offerAdd(data) {
  return request({
    url: '/ads/search/off/add',
    method: 'post',
    data: data
  })
}

export function offerUpdate(data) {
  return request({
    url: '/ads/search/off/update',
    method: 'post',
    data: data
  })
}


export function remove(id) {
  return request({
    url: '/ads/search/off/delete/' + id,
    method: 'post'
  })
}



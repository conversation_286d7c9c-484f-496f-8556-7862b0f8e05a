import request from "@/utils/request";


export function analytic_list(data) {
  return request({
    url: "/google/ads_analytic/list",
    method: 'get',
    params: data
  })
}


export function addGa(domain) {
  return request({
    url: '/google/ads_analytic/add/' + domain,
    method: 'post'
  })
}

export function insights(data) {
  return request({
    url: '/google/ads_analytic/insights',
    method: 'get',
    params: data
  })
}


export function notGaDomainList() {
  return request({
    url: '/google/ads_analytic/notGaCodeList',
    method: 'get'
  })
}

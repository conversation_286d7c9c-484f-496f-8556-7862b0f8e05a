import request from '../../utils/request'

// 获取模板列表
export function list(query) {
  return request({
    url: '/adv/template/list',
    method: 'get',
    params: query
  })
}

// 新增模板
export function add(data) {
  return request({
    url: '/adv/template/add',
    method: 'post',
    data: data
  })
}

// 修改模板
export function update(data) {
  return request({
    url: '/adv/template/update',
    method: 'post',
    data: data
  })
}

// 删除模板
export function remove(id) {
  return request({
    url: '/adv/template/remove/' + id,
    method: 'delete'
  })
}

import request from "@/utils/request";


// 筛选
export function list(query) {
    return request({
        url: '/ads/aff/list',
        method: 'get',
        params: query
    })
}

// 新增
export function add(data) {
    return request({
        url: '/ads/aff/insert',
        method: 'post',
        data: data
    })
}

// 修改
export function update(data) {
    return request({
        url: '/ads/aff/update',
        method: 'post',
        data: data
    })
}

// 移除
export function remove(id) {
    return request({
        url: '/ads/aff/remove/' + id,
        method: 'delete'
    })
}

// 修改给渠道分配对应数据
export function updateApp(data) {
    return request({
        url: '/ads/aff/app/update',
        method: 'post',
        data: data
    })
}

// 新增给渠道分配对应数据
export function addApp(data) {
    return request({
        url: '/ads/aff/app/insert',
        method: 'post',
        data: data
    })
}


// 删除给渠道分配对应数据
export function removeApp(ids) {
    return request({
        url: '/ads/aff/app/' + ids,
        method: 'delete'
    })
}

// 获取给渠道分配对应数据
export function listApp(query) {
    return request({
        url: '/ads/aff/app/list',
        method: 'get',
        params: query
    })
}


// 获取广告主列表
export function advs() {
    return request({
        url: '/ads/adv/list',
        method: 'get',
        params: {
            pageNum: 1,
            pageSize: 1000,
        }
    })
}

export function listAvailableDomain() {
    return request({
        url: '/ads/aff/app/listAvailableDomain',
        method: 'get'
    })
}

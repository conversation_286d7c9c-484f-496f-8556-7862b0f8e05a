import { createRouter, createWebHistory } from 'vue-router'
import AuthLayout from '../layouts/AuthLayout.vue'
import AppLayout from '../layouts/AppLayout.vue'
import { getToken } from '../utils'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { usePermissionStore } from '../stores/permission'
const whiteList = ['/auth/login', '/article']
NProgress.configure({ showSpinner: false })
// 公共路由
export const constantRoutes = [
  {
    path: '/auth',
    component: AuthLayout,
    children: [
      {
        name: 'login',
        path: 'login',
        component: () => import('../pages/auth/Login.vue'),
      },

      {
        path: '',
        redirect: { name: 'login' },
      },
    ],
  },

  {
    name: '404',
    path: '/404',
    component: () => import('../pages/404.vue'),
  },
  {
    name: 'article',
    path: '/article',
    title: '文章生成',
    hidden: true,
    meta: {
      title: '文章生成',
      icon: 'vuestic-iconset-dashboard',
    },
    component: () => import('../pages/auth/article/Article.vue'),
  },
]

// 动态路由，需要根据用户权限动态加载
export const dynamicRoutes = [
  {
    name: 'admin',
    path: '/',
    title: '首页',
    hidden: true,
    meta: {
      title: '首页',
      icon: 'vuestic-iconset-dashboard',
    },
    component: AppLayout,
    redirect: { name: 'dashboard' },
    children: [
      {
        title: '首页',
        hidden: true,
        meta: {
          title: '首页',
          icon: 'vuestic-iconset-dashboard',
        },
        name: 'dashboard',
        path: 'dashboard',
        component: () => import('../pages/admin/dashboard/Dashboard.vue'),
      },

      {
        title: '个人中心',
        name: 'preferences',
        path: '/preferences',
        hidden: true,
        meta: {
          title: '首页',
          icon: 'vuestic-iconset-dashboard',
        },
        component: () => import('../pages/preferences/Preferences.vue'),
      },
    ],
  },
]

const routes = [...constantRoutes, ...dynamicRoutes]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    if (to.hash) {
      return { el: to.hash, behavior: 'smooth' }
    } else {
      window.scrollTo(0, 0)
    }
  },
  routes,
})

// 全局变量，用于标记是否正在加载路由
let isLoadingRoutes = false

// 全局路由守卫 - beforeEach
router.beforeEach((to, from, next) => {
  NProgress.start()
  const token = getToken()

  // 登录页面逻辑
  if (to.path === '/auth/login') {
    if (token) {
      // 已登录用户访问登录页，重定向到首页或来源页
      NProgress.done()
      next(from.path !== '/auth/login' ? { path: from.path } : { name: 'dashboard' })
    } else {
      // 未登录用户访问登录页，允许访问
      next()
    }
    return
  }

  // 白名单路径检查
  if (whiteList.includes(to.path)) {
    // 在白名单中的路径直接通过，无论是否有token
    next()
    return
  }

  // 未登录用户访问非白名单页面，重定向到登录页
  if (!token) {
    NProgress.done()
    next({ path: '/auth/login' })
    return
  }

  // 已登录用户，检查权限路由是否已加载
  const permissionStore = usePermissionStore()
  if (!permissionStore.hasRoutes && !isLoadingRoutes) {
    // 标记正在加载路由，防止重复加载
    isLoadingRoutes = true

    // 加载动态路由，但不在这里处理重定向
    permissionStore.GenerateRoutes().then(() => {
      isLoadingRoutes = false
      // 路由加载完成后，让 beforeResolve 处理重定向
      next({ ...to, replace: true })
    }).catch(() => {
      isLoadingRoutes = false
      NProgress.done()
      next() // 出错时也继续导航
    })
    return
  }

  // 其他情况，正常通过
  next()
})

// 添加 beforeResolve 守卫，在所有组件内守卫和异步路由组件被解析后调用
router.beforeResolve((to, from, next) => {
  // 如果路由不存在且不是404页面，重定向到404
  if (to.matched.length === 0 && to.path !== '/404') {
    next({ path: '/404' })
  } else {
    next()
  }
})

router.afterEach(() => {
  NProgress.done()
})

export default router

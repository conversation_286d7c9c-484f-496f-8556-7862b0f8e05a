import axios from 'axios'
import { getToken, errorCode, Loading, removeToken } from './index'
import { tansParams, blobValidate } from './subdigi'
import router from '../router'
import { saveAs } from 'file-saver'
import { useToast } from 'vuestic-ui'
import { useUserStore } from '../stores/user-store.js'
const whiteList = ['/auth/login', '/article']
const toast = useToast()
let downloadLoadingInstance
// 是否显示重新登录
export const isRelogin = { show: false }
axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'

// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: import.meta.env.VITE_API_BASE_URL,
  // 超时
  timeout: 600000,
})

// request拦截器
service.interceptors.request.use(
  (config) => {
    // 是否需要设置 token
    const isToken = (config.headers).isToken === false
    // 是否需要防止数据重复提交
    const isRepeatSubmit = (config.headers).repeatSubmit === false
    if (getToken() && !isToken) {
      config.headers['Authorization'] = 'Bearer ' + getToken() // 让每个请求携带自定义token
    }
    // get请求映射params参数
    if (config.method === 'get' && config.params) {
      let url = config.url + '?' + tansParams(config.params)
      url = url.slice(0, -1)
      config.params = {}
      config.url = url
    }
    if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {
      const requestObj = {
        url: config.url,
        data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,
        time: new Date().getTime(),
      }
      const requestSize = Object.keys(JSON.stringify(requestObj)).length // 请求数据大小
      const limitSize = 5 * 1024 * 1024 // 限制存放数据5M
      if (requestSize >= limitSize) {
        console.warn(`[${config.url}]: ` + '请求数据大小超出允许的5M限制，无法进行防重复提交验证。')
        return config
      }

      // 简单的会话存储实现
      const getSessionObj = () => {
        const sessionObj = sessionStorage.getItem('sessionObj')
        return sessionObj ? JSON.parse(sessionObj) : null
      }

      const setSessionObj = (obj) => {
        sessionStorage.setItem('sessionObj', JSON.stringify(obj))
      }

      const sessionObj = getSessionObj()
      if (sessionObj === null) {
        setSessionObj(requestObj)
      } else {
        const s_url = sessionObj.url // 请求地址
        const s_data = sessionObj.data // 请求数据
        const s_time = sessionObj.time // 请求时间
        const interval = 100 // 间隔时间(ms)，小于此时间视为重复提交
        if (s_data === requestObj.data && requestObj.time - s_time < interval && s_url === requestObj.url) {
          const message = '数据正在处理，请勿重复提交'
          console.warn(`[${s_url}]: ` + message)
          return Promise.reject()
        } else {
          setSessionObj(requestObj)
        }
      }
    }
    return config
  },
  (error) => {
    console.log(error)
    Promise.reject(error)
  },
)

// 响应拦截器
service.interceptors.response.use(
  (res) => {
    // 未设置状态码则默认成功状态
    const code = res.data.code || 200
    // 请求路径为logout并且code为100直接返回
    if (res.config.url === '/logout' && code === 100) {
      return res.data
    }
    // 请求路径为login并且code为102 代表需要2fa验证
    if (res.config.url === '/login' && code === 102) {
      return res.data
    }
    // 获取错误信息
    const msg = errorCode[code] || res.data.msg || errorCode['default']
    // 二进制数据则直接返回
    if (res.request.responseType === 'blob' || res.request.responseType === 'arraybuffer') {
      return res.data
    }

    if (code === -401 || code === 401) {
      // 获取当前路径
      const currentPath = window.location.pathname
      console.log(currentPath);

      // 对白名单路径特殊处理
      if (whiteList.includes(currentPath)) {
        // 白名单内的路径，401错误不提示也不跳转
        return Promise.reject(new Error(msg))
      }

      if (!isRelogin.show) {
        isRelogin.show = true
        // toast.init({
        //   message: '登录状态已过期，您可以继续留在该页面，或者重新登录',
        //   color: 'warning',
        //   duration: 5000,
        // })
        // 移除token
        removeToken()
        // 清除用户状态
        const userStore = useUserStore()
        userStore.logout()
        // 延迟跳转，让用户有时间看到提示
        setTimeout(() => {
          router.push('/auth/login')
          isRelogin.show = false
        }, 1000)
        return Promise.reject(new Error(msg))
      }
      return Promise.reject('无效的会话，或者会话已过期，请重新登录。')
    } else if (code === 500) {
      // 使用 toast.init 创建错误通知
      toast.init({
        message: msg,
        color: 'danger',
        duration: 3000,
      })
      return Promise.reject(new Error(msg))
    } else if (code === 601) {
      // 使用 toast.init 创建警告通知
      toast.init({
        message: msg,
        color: 'warning',
        duration: 3000,
      })
      return Promise.reject('error')
    } else if (code !== 200) {
      // 使用 toast.init 创建错误通知
      toast.init({
        message: msg,
        color: 'danger',
        duration: 3000,
      })
      return Promise.reject('error')
    } else {
      return res.data
    }
  },
  (error) => {
    console.log('err', error)
    let { message } = error

    // 获取当前路径
    const currentPath = window.location.pathname

    // 如果是白名单页面，不显示错误提示
    if (whiteList.includes(currentPath)) {
      return Promise.reject(error)
    }

    if (message == 'Network Error') {
      message = '连接超时，检查网络连接'
    } else if (message.includes('timeout')) {
      message = '系统接口请求超时'
    } else if (message.includes('Request failed with status code')) {
      message = '系统接口' + message.substr(message.length - 3) + '异常'
    }
    // 使用 toast.init 创建错误通知
    toast.init({
      message: message,
      color: 'danger',
      duration: 3000,
    })
    return Promise.reject(error)
  },
)

// 通用下载方法
export function download(url, params, filename, config = {}) {
  downloadLoadingInstance = Loading.service({
    text: '正在下载数据，请稍候',
    overlayOpacity: 0.7,
  })

  return service
    .post < Blob > (url, params, {
      transformRequest: [
        (params) => {
          return tansParams(params)
        },
      ],
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      responseType: 'blob',
      ...config,
    })
      .then(async (response) => {
        const data = response.data
        const isBlob = blobValidate(data)
        if (isBlob) {
          const blob = new Blob([data])
          saveAs(blob, filename)
        } else {
          const resText = await data.text()
          const rspObj = JSON.parse(resText)
          const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
          // 使用 toast.init 创建错误通知
          toast.init({
            message: errMsg,
            color: 'danger',
            duration: 5000,
          })
        }
        downloadLoadingInstance.close()
      })
      .catch((r) => {
        console.error(r)
        // 使用 toast.init 创建错误通知
        toast.init({
          message: '下载文件出现错误，请联系管理员！',
          color: 'danger',
          duration: 5000,
        })
        downloadLoadingInstance.close()
      })
}

export default service

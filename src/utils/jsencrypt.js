import CryptoJS from 'crypto-js'


// 密钥，基于设备信息和存储的盐值生成一个相对稳定的密钥
const SECRET_KEY = getSecureKey()

/**
 * 加密数据
 * @param data 需要加密的数据
 * @returns 加密后的字符串
 */
export function encrypt(data) {
  if (!data) return ''

  // 使用AES加密
  const encrypted = CryptoJS.AES.encrypt(data, SECRET_KEY)
  return encrypted.toString()
}

/**
 * 解密数据
 * @param encryptedData 加密后的字符串
 * @returns 解密后的原始数据
 */
export function decrypt(encryptedData) {
  if (!encryptedData) return ''

  try {
    // 使用AES解密
    const decrypted = CryptoJS.AES.decrypt(encryptedData, SECRET_KEY)
    return decrypted.toString(CryptoJS.enc.Utf8)
  } catch (error) {
    console.error('解密失败:', error)
    return ''
  }
}

/**
 * 生成基于设备信息的安全密钥
 * 注意：这个密钥会在同一设备上保持相对稳定，但在不同设备上会不同
 * 这样可以确保即使应用重新加载，之前加密的数据仍然可以被解密
 */
function getSecureKey() {
  // 应用特定的盐值
  const APP_SALT = 'Subdigi-App-2024'

  // 获取或创建设备特定的盐值
  let deviceSalt = localStorage.getItem('device_encryption_salt')
  if (!deviceSalt) {
    // 如果没有存储的盐值，创建一个新的随机盐值
    deviceSalt = generateRandomString(32)
    localStorage.setItem('device_encryption_salt', deviceSalt)
  }

  // 获取设备信息
  const deviceInfo = navigator.userAgent + navigator.language + navigator.platform

  // 结合应用盐值、设备盐值和设备信息生成密钥
  const combinedKey = APP_SALT + deviceSalt + deviceInfo

  // 使用SHA-256生成一个固定长度的密钥
  return CryptoJS.SHA256(combinedKey).toString()
}

/**
 * 生成指定长度的随机字符串
 * @param length 字符串长度
 * @returns 随机字符串
 */
function generateRandomString(length) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'
  let result = ''

  // 使用加密安全的随机数生成
  const randomValues = new Uint32Array(length)
  window.crypto.getRandomValues(randomValues)

  for (let i = 0; i < length; i++) {
    result += chars.charAt(randomValues[i] % chars.length)
  }

  return result
}


/**
 * @description 执行任务，返回promise
 * @param {Function} func 任务函数
 * @param {Array} taskList 任务列表
 * @param {Function} dataFormatter 数据格式化函数，用于为每个任务准备参数
 * @param {Object} options 额外选项
 * @param {Function} options.onProgress 进度回调函数，参数为 (current, total, currentTask)
 * @returns {Promise} 返回所有任务执行的结果数组
 */
export const execTask = async (func, taskList, dataFormatter, options = {}) => {
    const { onProgress } = options;
    const results = [];
    const total = taskList.length;

    for (let i = 0; i < total; i++) {
        const currentTask = taskList[i];
        const current = i + 1;

        // 调用进度回调函数
        if (onProgress && typeof onProgress === 'function') {
            onProgress(current, total, currentTask);
        }

        try {
            // 准备任务参数
            const taskData = dataFormatter ? dataFormatter(currentTask) : currentTask;

            // 执行任务
            const response = await func(taskData);

            // 保存结果
            results.push({
                task: currentTask,
                success: response && response.code === 200,
                response
            });
        } catch (error) {
            // 保存错误信息
            results.push({
                task: currentTask,
                success: false,
                error
            });
        }
    }

    return results;
}
import { createApp, h, ref } from 'vue'
import SubLoading from '../components/SubLoading.vue'


class LoadingService {
  instance = null
  container = null

  /**
   * 显示全局加载状态
   * @param options 加载选项
   * @returns 加载实例，可以调用close方法关闭
   */
  service(options = {}) {
    // 如果已经存在实例，先关闭
    if (this.instance) {
      this.close()
    }

    // 创建容器
    this.container = document.createElement('div')
    this.container.style.position = 'fixed'
    this.container.style.top = '0'
    this.container.style.left = '0'
    this.container.style.width = '100vw'
    this.container.style.height = '100vh'
    this.container.style.zIndex = '9999'
    document.body.appendChild(this.container)

    // 创建Vue应用
    const loading = ref(true)
    const app = createApp({
      setup() {
        return () =>
          h(SubLoading, {
            loading: loading.value,
            text: options.text || '',
            color: options.color,
            overlayColor: options.overlayColor,
            overlayOpacity: options.overlayOpacity,
          })
      },
    })

    // 挂载应用
    this.instance = app.mount(this.container)

    // 返回控制对象
    return {
      // 关闭加载状态
      close: () => {
        this.close()
      },
    }
  }

  /**
   * 关闭加载状态
   */
  close() {
    if (this.instance && this.container) {
      // 卸载应用
      setTimeout(() => {
        if (this.container) {
          document.body.removeChild(this.container)
          this.container = null
          this.instance = null
        }
      }, 300) // 等待动画完成
    }
  }
}

// 创建单例
const Loading = new LoadingService()

export default Loading

/**
 * 将数据转换为CSV格式
 * @param data 数据
 * @returns CSV格式数据
 */
export const toCSV = (data) => {
  const headers = Object.keys(data[0])
  const csv = [
    headers.join(','),
    ...data.map((row) => headers.map((fieldName) => JSON.stringify(row[fieldName])).join(',')),
  ].join('\r\n')
  return csv
}
/**
 * 下载CSV文件
 * @param data 数据
 * @param filename 文件名
 */
export const downloadAsCSV = (data, filename) => {
  const csv = toCSV(data)
  const blob = new Blob([csv], { type: 'text/csv' })
  const link = document.createElement('a')
  link.href = window.URL.createObjectURL(blob)
  link.download = filename
  link.click()
}

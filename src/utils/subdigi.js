

/**
 * 将数字格式化为千分位展示
 * @description 将数值转换为带千分位分隔符的字符串格式，对于undefined或null值返回'--'
 * @param {number|undefined|null} value - 需要格式化的数值
 * @returns {string} 格式化后的字符串，如果输入为undefined或null则返回'--'
 * @example
 * formatNumber(1234567) // 返回 "1,234,567"
 * formatNumber(null) // 返回 "--"
 */
export function formatNumber(value) {
  if (value === undefined || value === null) {
    return '--'
  }
  return value.toLocaleString()
}

/**
 * 日期格式化
 * @description 将各种格式的日期转换为指定格式的字符串
 * @param {Date|string|number} time - 日期对象、日期字符串或时间戳
 * @param {string} [pattern] - 输出格式模板，默认为'{y}-{m}-{d} {h}:{i}:{s}'
 * @returns {string|null} 格式化后的日期字符串，如果输入无效则返回null
 * @example
 * parseTime(new Date(), '{y}-{m}-{d}') // 返回 "2023-05-15"
 * parseTime(1684123456789, '{y}年{m}月{d}日') // 返回 "2023年05月15日"
 * parseTime('2023-05-15', '{y}/{m}/{d}') // 返回 "2023/05/15"
 */
export function parseTime(time, pattern) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time)
    } else if (typeof time === 'string') {
      time = time
        .replace(new RegExp(/-/gm), '/')
        .replace('T', ' ')
        .replace(new RegExp(/\.[\d]{3}/gm), '')
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (match, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    if (match.length > 0 && value < 10) {
      const paddedValue = '0' + value
      return paddedValue
    }
    return String(value || 0)
  })
  return time_str
}


/**
 * 添加日期范围到查询参数
 * @description 将日期范围数组添加到查询参数对象中
 * @param {Object} params - 查询参数对象
 * @param {Array} dateRange - 日期范围数组，包含开始和结束日期
 * @param {string} [propName] - 属性名称，用于自定义参数名，如不提供则使用默认名称
 * @returns {Object} 添加了日期范围的查询参数对象
 * @example
 * addDateRange({page: 1}, ['2023-01-01', '2023-01-31']) 
 * // 返回 {page: 1, params: {beginTime: '2023-01-01', endTime: '2023-01-31'}}
 * 
 * addDateRange({page: 1}, ['2023-01-01', '2023-01-31'], 'Date') 
 * // 返回 {page: 1, params: {beginDate: '2023-01-01', endDate: '2023-01-31'}}
 */
export function addDateRange(params, dateRange, propName) {
  const search = params
  search.params =
    typeof search.params === 'object' && search.params !== null && !Array.isArray(search.params) ? search.params : {}
  dateRange = Array.isArray(dateRange) ? dateRange : []
  if (typeof propName === 'undefined') {
    search.params['beginTime'] = dateRange[0]
    search.params['endTime'] = dateRange[1]
  } else {
    search.params['begin' + propName] = dateRange[0]
    search.params['end' + propName] = dateRange[1]
  }
  return search
}


/**
 * 回显数据字典（字符串、数组）
 * @description 根据值从数据字典中获取对应的标签
 * @param {Array} datas - 数据字典数组，每个元素应包含value和label属性
 * @param {string|Array} value - 需要查找的值，可以是字符串或数组
 * @param {string} [separator] - 分隔符，默认为逗号','
 * @returns {string} 匹配到的标签字符串，多个标签用分隔符连接
 * @example
 * const dict = [{value: '1', label: '男'}, {value: '2', label: '女'}]
 * selectDictLabels(dict, '1') // 返回 "男"
 * selectDictLabels(dict, '1,2') // 返回 "男,女"
 * selectDictLabels(dict, ['1', '2'], '-') // 返回 "男-女"
 */
export function selectDictLabels(
  datas,
  value,
  separator
) {
  if (
    value === undefined ||
    (typeof value === 'string' && value.length === 0) ||
    (Array.isArray(value) && value.length === 0)
  ) {
    return ''
  }
  let valueStr
  if (Array.isArray(value)) {
    valueStr = value.join(',')
  } else {
    valueStr = value
  }
  const actions = []
  const currentSeparator = separator === undefined ? ',' : separator
  const temp = valueStr.split(currentSeparator)
  // 修复索引表达式的类型问题
  Object.keys(temp).forEach((val) => {
    let match = false
    const index = Number(val)
    Object.keys(datas).some((key) => {
      if (datas[key].value == '' + temp[index]) {
        actions.push(datas[key].label + currentSeparator)
        match = true
        return true
      }
      return false
    })
    if (!match) {
      actions.push(temp[index] + currentSeparator)
    }
  })
  return actions.join('').substring(0, actions.join('').length - 1)
}

/**
 * 字符串格式化
 * @description 使用%s作为占位符，将参数依次替换到字符串中
 * @param {string} str - 包含占位符的字符串模板
 * @param {...any} args - 要替换到占位符位置的参数列表
 * @returns {string} 格式化后的字符串
 * @example
 * sprintf('你好，%s！今天是%s。', '张三', '星期一') // 返回 "你好，张三！今天是星期一。"
 * sprintf('当前进度：%s/%s', 1, 10) // 返回 "当前进度：1/10"
 */
export function sprintf(str, ...args) {
  let i = 0
  let flag = true
  const result = str.replace(/%s/g, function () {
    const arg = args[i++]
    if (typeof arg === 'undefined') {
      flag = false
      return ''
    }
    return String(arg)
  })
  return flag ? result : ''
}

/**
 * 转换字符串，undefined,null等转化为空字符串
 * @description 将undefined、null或字符串"undefined"、"null"转换为空字符串
 * @param {string|undefined|null} str - 需要处理的字符串
 * @returns {string} 处理后的字符串，无效值返回空字符串
 * @example
 * parseStrEmpty(undefined) // 返回 ""
 * parseStrEmpty(null) // 返回 ""
 * parseStrEmpty("null") // 返回 ""
 * parseStrEmpty("hello") // 返回 "hello"
 */
export function parseStrEmpty(str) {
  if (!str || str == 'undefined' || str == 'null') {
    return ''
  }
  return str
}

/**
 * 递归合并对象
 * @description 将目标对象的属性递归合并到源对象中
 * @param {Object} source - 源对象，将被修改
 * @param {Object} target - 目标对象，其属性将被合并到源对象
 * @returns {Object} 合并后的对象（即修改后的源对象）
 * @example
 * const source = {a: 1, b: {c: 2}}
 * const target = {b: {d: 3}, e: 4}
 * mergeRecursive(source, target) // 返回 {a: 1, b: {c: 2, d: 3}, e: 4}
 */
export function mergeRecursive(source, target) {
  for (const p in target) {
    try {
      if (target[p].constructor == Object) {
        ; (source)[p] = mergeRecursive((source)[p] || ({}), target[p])
      } else {
        ; (source)[p] = target[p]
      }
    } catch (e) {
      ; (source)[p] = target[p]
    }
  }
  return source
}

/**
 * 构造树型结构数据
 * @description 将平铺的数据结构转换为树形结构
 * @param {Array} data - 数据源数组
 * @param {string} [id='id'] - ID字段名
 * @param {string} [parentId='parentId'] - 父节点ID字段名
 * @param {string} [children='children'] - 子节点列表字段名
 * @returns {Array} 构造好的树形结构数组
 * @example
 * const data = [
 *   {id: 1, name: '父节点', parentId: 0},
 *   {id: 2, name: '子节点1', parentId: 1},
 *   {id: 3, name: '子节点2', parentId: 1}
 * ]
 * handleTree(data) 
 * // 返回 [{id: 1, name: '父节点', parentId: 0, children: [
 * //   {id: 2, name: '子节点1', parentId: 1},
 * //   {id: 3, name: '子节点2', parentId: 1}
 * // ]}]
 */
export function handleTree(data, id, parentId, children) {
  let config = {
    id: id || 'id',
    parentId: parentId || 'parentId',
    childrenList: children || 'children'
  };

  var childrenListMap = {};
  var nodeIds = {};
  var tree = [];

  for (let d of data) {
    let parentId = d[config.parentId];
    if (childrenListMap[parentId] == null) {
      childrenListMap[parentId] = [];
    }
    nodeIds[d[config.id]] = d;
    childrenListMap[parentId].push(d);
  }

  for (let d of data) {
    let parentId = d[config.parentId];
    if (nodeIds[parentId] == null) {
      tree.push(d);
    }
  }

  for (let t of tree) {
    adaptToChildrenList(t);
  }

  function adaptToChildrenList(o) {
    if (childrenListMap[o[config.id]] !== null) {
      o[config.childrenList] = childrenListMap[o[config.id]];
    }
    if (o[config.childrenList]) {
      for (let c of o[config.childrenList]) {
        adaptToChildrenList(c);
      }
    }
  }
  return tree;
}


/**
 * 参数处理
 * @description 将对象参数序列化为URL查询字符串
 * @param {Object} params - 需要序列化的参数对象
 * @returns {string} 序列化后的URL查询字符串（不包含前导?）
 * @example
 * tansParams({name: '张三', age: 18}) // 返回 "name=%E5%BC%A0%E4%B8%89&age=18&"
 * tansParams({user: {name: '张三', age: 18}}) // 返回 "user%5Bname%5D=%E5%BC%A0%E4%B8%89&user%5Bage%5D=18&"
 */
export function tansParams(params) {
  let result = ''
  for (const propName of Object.keys(params)) {
    const value = params[propName]
    const part = encodeURIComponent(propName) + '='
    if (value !== null && value !== '' && typeof value !== 'undefined') {
      if (typeof value === 'object') {
        for (const key of Object.keys(value)) {
          if (value[key] !== null && value[key] !== '' && typeof value[key] !== 'undefined') {
            const paramKey = propName + '[' + key + ']'
            const subPart = encodeURIComponent(paramKey) + '='
            result += subPart + encodeURIComponent(value[key]) + '&'
          }
        }
      } else {
        result += part + encodeURIComponent(value) + '&'
      }
    }
  }
  return result
}

/**
 * 验证是否为blob格式
 * @description 检查数据是否为Blob格式而非JSON
 * @param {Object} data - 需要验证的数据对象
 * @returns {boolean} 如果不是application/json类型则返回true
 * @example
 * blobValidate({type: 'application/octet-stream'}) // 返回 true
 * blobValidate({type: 'application/json'}) // 返回 false
 */
export function blobValidate(data) {
  return data.type !== 'application/json'
}


/**
 * 验证是否为外部链接
 * @description 检查路径是否为外部链接（以http:、https:、mailto:或tel:开头）
 * @param {string} path - 需要验证的路径
 * @returns {boolean} 如果是外部链接则返回true
 * @example
 * isExternal('https://www.example.com') // 返回 true
 * isExternal('/home') // 返回 false
 * isExternal('mailto:<EMAIL>') // 返回 true
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}
export const sleep = (ms = 0) => {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

/**
 * 验证器
 * @param v 值
 * @returns 是否有效
 */
export const validators = {
  /**
   * 验证邮箱
   * @param v 值
   * @returns 是否有效
   */
  email: (v) => {
    const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return pattern.test(v) || 'Please enter a valid email address'
  },
  /**
   * 验证必填项
   * @param v 值
   * @returns 是否有效
   */
  required: (v) => !!v || 'This field is required',
}

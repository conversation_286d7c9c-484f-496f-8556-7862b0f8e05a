import { createVNode, render } from 'vue';
import Notification from '@/components/notfication/Notfication.vue';

// Container for notifications
let notificationContainer = null;

// Create or get notification container
const getNotificationContainer = () => {
  if (!notificationContainer) {
    notificationContainer = document.createElement('div');
    notificationContainer.className = 'notifications-container';
    document.body.appendChild(notificationContainer);
  }
  return notificationContainer;
};

// Counter for unique IDs
let notificationCount = 0;

// Create notification
const createNotification = (options) => {
  const id = `notification-${Date.now()}-${notificationCount++}`;
  
  const container = document.createElement('div');
  container.id = id;
  getNotificationContainer().appendChild(container);

  // Create notification component
  const vnode = createVNode(Notification, {
    ...options,
    onClose: () => {
      options.onClose?.();
      removeNotification(id);
    }
  });

  // Render notification
  render(vnode, container);

  return {
    close: () => {
      removeNotification(id);
    }
  };
};

// Remove notification
const removeNotification = (id) => {
  const container = document.getElementById(id);
  if (container) {
    render(null, container);
    container.remove();
  }
};

// Notification types
export const notification = {
  info: (options) => {
    return createNotification({
      ...options,
      type: 'info'
    });
  },
  success: (options) => {
    return createNotification({
      ...options,
      type: 'success'
    });
  },
  warning: (options) => {
    return createNotification({
      ...options,
      type: 'warning'
    });
  },
  error: (options) => {
    return createNotification({
      ...options,
      type: 'error'
    });
  }
};

export default notification; 
import { useUserStore } from '../stores/user-store'

/**
 * 字符权限校验（OR 关系）
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
function hasPermiOr(value) {
  if (value && value instanceof Array && value.length > 0) {
    const userStore = useUserStore()
    const permissions = userStore.permissions
    const all_permission = '*:*:*'

    return value.some((permission) => {
      return permissions.some((userPermission) => {
        return all_permission === userPermission || userPermission === permission
      })
    })
  } else {
    console.error(`需要权限!`)
    return false
  }
}

/**
 * 字符权限校验（AND 关系）
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
function hasPermiAnd(value) {
  if (value && value instanceof Array && value.length > 0) {
    const userStore = useUserStore()
    const permissions = userStore.permissions
    const all_permission = '*:*:*'

    return value.every((permission) => {
      return permissions.some((userPermission) => {
        return all_permission === userPermission || userPermission === permission
      })
    })
  } else {
    console.error(`需要权限!`)
    return false
  }
}

/**
 * 角色权限校验（OR 关系）
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
function hasRoleOr(value) {
  if (value && value instanceof Array && value.length > 0) {
    const userStore = useUserStore()
    const roles = userStore.roles
    const super_admin = 'admin'

    return value.some((role) => {
      return roles.some((userRole) => {
        return super_admin === userRole || userRole === role
      })
    })
  } else {
    console.error(`需要角色! `)
    return false
  }
}

/**
 * 角色权限校验（AND 关系）
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
function hasRoleAnd(value) {
  if (value && value instanceof Array && value.length > 0) {
    const userStore = useUserStore()
    const roles = userStore.roles
    const super_admin = 'admin'

    return (
      roles.some((userRole) => super_admin === userRole) ||
      value.every((role) => {
        return roles.some((userRole) => userRole === role)
      })
    )
  } else {
    console.error(`需要角色!`)
    return false
  }
}

export default { hasPermiOr, hasPermiAnd, hasRoleOr, hasRoleAnd }

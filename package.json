{"name": "vuestic-admin", "private": true, "version": "3.1.0", "scripts": {"dev": "vite --host", "dev:local": "vite", "build": "vue-tsc --noEmit && vite build", "build:ci": "vite build", "analyze": "vite build --mode analyze", "start:ci": "serve -s ./dist", "prelint": "npm run format", "format": "prettier --write .", "preview": "vite preview"}, "dependencies": {"@gtm-support/vue-gtm": "^2.0.0", "@types/crypto-js": "^4.2.2", "@vuestic/compiler": "latest", "@vuestic/tailwind": "^0.1.3", "markdown-it": "^14.1.0", "@vueuse/core": "^10.6.1", "axios": "^1.7.9", "chart.js": "^4.4.1", "chartjs-chart-geo": "^4.2.8", "crypto-js": "^4.2.0", "epic-spinners": "^2.0.0", "file-saver": "^2.0.5", "@vuepic/vue-datepicker": "^11.0.2", "flag-icons": "^6.15.0", "ionicons": "^4.6.3", "medium-editor": "^5.23.3", "nprogress": "0.2.0", "pinia": "^2.1.7", "qrcode.vue": "^3.6.0", "register-service-worker": "^1.7.1", "sass": "^1.69.5", "serve": "^14.2.1", "uuid": "^11.0.3", "vue": "3.5.8", "vue-chartjs": "^5.3.0", "vue-i18n": "^9.6.2", "vue-router": "^4.2.5", "vuestic-ui": "^1.10.2"}, "devDependencies": {"@intlify/unplugin-vue-i18n": "^1.5.0", "@storybook/addon-essentials": "^7.4.6", "@storybook/addon-interactions": "^7.4.6", "@storybook/addon-links": "^7.4.6", "@storybook/blocks": "^7.4.6", "@storybook/testing-library": "^0.2.2", "@storybook/vue3": "^7.4.6", "@storybook/vue3-vite": "^7.4.6", "@types/file-saver": "^2.0.7", "@types/medium-editor": "^5.0.5", "@types/node": "^20.9.0", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "autoprefixer": "^10.4.13", "eslint": "^8.13.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-storybook": "^0.6.15", "eslint-plugin-vue": "^9.18.1", "husky": "^8.0.1", "json-server": "^1.0.0-beta.3", "lint-staged": "^15.1.0", "postcss": "^8.4.21", "prettier": "^3.1.0", "rollup-plugin-visualizer": "^6.0.3", "storybook": "^7.4.6", "tailwindcss": "^3.4.0", "typescript": "^5.2.2", "vite": "^5.4.9", "vite-plugin-compression": "^0.5.1", "vue-eslint-parser": "^9.3.2", "vue-tsc": "^2.1.6"}}
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'node:path'
import { fileURLToPath } from 'node:url'
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite'
import { vuestic } from '@vuestic/compiler/vite'
import compression from 'vite-plugin-compression'
import { visualizer } from 'rollup-plugin-visualizer'

// 获取当前文件目录路径
const __dirname = fileURLToPath(new URL('.', import.meta.url))

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd())
  const isProd = mode === 'production'

  return {
    // 基础路径配置（根据项目需要设置）
    base: '/',

    // 构建配置
    build: {
      // 使用esbuild压缩（默认值）
      minify: 'esbuild',

      // 使用esbuild的方式移除console和debugger
      esbuild: {
        pure: isProd ? ['console.log', 'console.info', 'console.debug', 'console.warn', 'debugger'] : [],
        drop: isProd ? ['console', 'debugger'] : [],
      },

      // 兼容性配置
      target: 'esnext',

      // 资源缓存优化
      assetsInlineLimit: 4096, // 小于4kb的资源内联为base64
      cssCodeSplit: true,
      sourcemap: !isProd, // 生产环境不生成sourcemap

      // Rollup 配置
      rollupOptions: {
        output: {
          // 静态资源分类打包
          chunkFileNames: isProd ? 'assets/js/[name]-[hash].js' : 'assets/js/[name].js',
          entryFileNames: isProd ? 'assets/js/[name]-[hash].js' : 'assets/js/[name].js',
          assetFileNames: isProd ? 'assets/[ext]/[name]-[hash].[ext]' : 'assets/[ext]/[name].[ext]',

          // 拆分代码块
          manualChunks: (id) => {
            // 基础库拆分
            if (id.includes('node_modules')) {
              if (id.includes('vue') || id.includes('pinia') || id.includes('vue-router')) {
                return 'vue-vendor'
              }
              if (id.includes('vuestic-ui')) {
                return 'vuestic-vendor'
              }
              if (id.includes('vue-i18n')) {
                return 'i18n-vendor'
              }
              if (id.includes('chart.js') || id.includes('vue-chartjs')) {
                return 'chart-vendor'
              }
              // 其他第三方库
              return 'vendors'
            }

            // 公共组件拆分
            if (id.includes('/src/components/')) {
              return 'components'
            }
          }
        }
      },
    },

    // 插件配置
    plugins: [
      // Vue 插件
      vue(),

      // Vuestic 插件（放在 vue 插件之后）
      vuestic({
        devtools: !isProd, // 生产环境禁用devtools
        cssLayers: true,
      }),

      // i18n 插件
      VueI18nPlugin({
        include: resolve(__dirname, './src/i18n/locales/**'),
      }),

      // Gzip压缩插件（生产环境启用）
      isProd && compression({
        verbose: false,
        disable: false,
        threshold: 10240, // 大于10kb的文件才会被压缩
        algorithm: 'gzip',
        ext: '.gz',
      }),

      // Brotli压缩插件（生产环境启用）
      isProd && compression({
        verbose: false,
        disable: false,
        threshold: 10240,
        algorithm: 'brotliCompress',
        ext: '.br',
      }),

      // 构建分析插件（仅在分析模式下启用）
      mode === 'analyze' && visualizer({
        open: true,
        filename: 'stats.html',
        gzipSize: true,
        brotliSize: true,
      }),
    ].filter(Boolean), // 过滤掉false值

    // 解析配置
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
      },
    },

    // 开发服务器配置
    server: {
      port: 5173,
      open: true,
      cors: true,
    },
  }
})